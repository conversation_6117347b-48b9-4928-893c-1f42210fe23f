"""
Deprecated
【已废弃】

该模块提供MCP服务器功能，实现为一个Library，与document.py等库有同等地位。
主要功能包括：

1. MCPServerLibrary类：MCP服务器的库实现
   - 基于FastMCP框架，提供工具服务
   - 实现工具注册和调用机制
   - 内建基本工具（天气、日期时间等）
   - 可通过add_tool方法扩展更多工具
   - 支持客户端连接功能，可以连接到其他MCP服务器

该模块实现了将MCP服务器功能集成到库系统中，可以和其他库一样被Agent加载和使用。
"""

import os
import json
import asyncio
import traceback
import sys
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from mcp.server.fastmcp import FastMCP, Context
from mcp import ClientSession
from mcp.client.sse import sse_client
from contextlib import AsyncExitStack

from libraries.library import Library
from log import logger
import config  # 导入配置模块
import uvicorn

class MCPServerLibrary(Library):
    """
    MCP服务器库，将MCP服务器功能封装为一个Library
    同时也支持作为客户端连接到其他MCP服务器
    """
    # 定义库的提示词
    library_prompt = """这是一个MCP服务器库，提供MCP服务功能，可以注册和提供工具。
该库会自动启动一个MCP服务器，可以被MCP客户端连接并使用其工具。
同时，该库也可以作为客户端连接到其他MCP服务器。

服务器功能:
- start_server(): 启动MCP服务器
- stop_server(): 停止MCP服务器

客户端功能:
- connect(): 连接到远程MCP服务器
- disconnect(): 断开与远程服务器的连接
"""

    def __init__(self):
        """初始化MCP服务器库"""
        super().__init__("mcp_server", "MCP服务器工具库", prompt=self.library_prompt)
        
        # 服务器相关属性
        # 获取端口配置
        import socket
        # 动态分配一个未被占用的端口
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('localhost', 0))
        self.port = sock.getsockname()[1]
        sock.close()
        self.server_running = False
        self.server = None
        self.server_task = None
        
        # 存储要添加到服务器的工具
        self.pending_tools = []
        
        # 知识库存储
        self.knowledge_storage = {}
        
        # 客户端相关属性
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.connected = False
        self.connecting = False  # 防止重复连接的标志
        
        # 设置MCP服务器URL
        self.mcp_url = f"http://localhost:{self.port}/sse"
        
        logger.info(f"MCP服务器库初始化完成，端口: {self.port}")
    
    #####################################
    # 服务器相关功能
    #####################################
    async def start_server(self) -> Dict[str, Any]:
        """
        启动MCP服务器
        """
        if self.server_running:
            return {"success": False, "error": "服务器已经在运行中"}
        
        try:
            # 创建MCP服务器
            self.server = FastMCP("MeowAgent服务", port=self.port)
            
            # 添加内置工具
            self.server.add_tool(self.get_weather)
            self.server.add_tool(self.get_current_date)
            
            # 添加待处理的工具
            for tool_info in self.pending_tools:
                self.server.add_tool(tool_info["func"])
            
            # 启动服务器任务
            starlette_app = self.server.sse_app()
            config = uvicorn.Config(
                starlette_app,
                host=self.server.settings.host,
                port=self.server.settings.port,
                log_level=self.server.settings.log_level.lower(),
            )
            server = uvicorn.Server(config)
            asyncio.create_task(server.serve())

            self.server_running = True
            
            logger.info(f"MCP服务器已启动，端口: {self.port}")
            return {
                "success": True,
                "message": f"MCP服务器已成功启动，端口: {self.port}"
            }
        except Exception as e:
            error_msg = f"启动MCP服务器失败: {traceback.format_exc()}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def stop_server(self) -> Dict[str, Any]:
        """
        停止MCP服务器
        """
        if not self.server_running:
            return {"success": False, "error": "服务器未运行"}
        
        try:
            # 停止服务器任务
            if self.server_task:
                self.server_task.cancel()
                self.server_task = None
            
            self.server = None
            self.server_running = False
            
            logger.info("MCP服务器已停止")
            return {
                "success": True,
                "message": "MCP服务器已成功停止"
            }
        except Exception as e:
            error_msg = f"停止MCP服务器失败: {traceback.format_exc()}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    # 内置工具实现
    def get_weather(self, city: str) -> Dict[str, Any]:
        """获取指定城市的天气信息"""
        try:
            # 模拟天气数据
            weather = {
                '北京': {"temp": '25°C', "condition": '晴朗', "humidity": '40%'},
                '上海': {"temp": '28°C', "condition": '多云', "humidity": '65%'},
                '广州': {"temp": '30°C', "condition": '小雨', "humidity": '80%'},
                '深圳': {"temp": '29°C', "condition": '阴天', "humidity": '75%'}
            }
            
            if city in weather:
                return {
                    "success": True,
                    "data": weather[city]
                }
            else:
                return {
                    "success": False,
                    "error": f"没有{city}的天气数据"
                }
        except Exception as e:
            logger.error(f"获取天气信息错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"获取天气信息发生错误: {str(e)}"
            }

    # 日期工具
    def get_current_date(self) -> Dict[str, Any]:
        """获取当前日期和时间"""
        try:
            now = datetime.now()
            return {
                "success": True,
                "data": {
                    "date": now.strftime("%Y-%m-%d"),
                    "time": now.strftime("%H:%M:%S"),
                    "timestamp": int(now.timestamp() * 1000)
                }
            }
        except Exception as e:
            logger.error(f"获取日期时间错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"获取日期时间发生错误: {str(e)}"
            }
    
    #####################################
    # 客户端相关功能
    #####################################
    async def connect(self, mcp_url: Optional[str] = None) -> Dict[str, Any]:
        """
        连接到MCP服务器
        
        参数:
            mcp_url: MCP服务器URL，如果为None则使用配置中的URL
        """
        if mcp_url:
            self.mcp_url = mcp_url
            
        if self.connected:
            return {"success": True, "message": "已经连接到MCP服务器"}

        if not self.mcp_url:
            return {"success": False, "error": "MCP服务器URL未配置"}
        
        try:
            # 使用sse_client连接
            stdio_transport = await self.exit_stack.enter_async_context(sse_client(url=self.mcp_url))
            self.stdio, self.write = stdio_transport
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))
            
            # 初始化会话
            await self.session.initialize()
            
            self.connected = True
            
            # 加载并缓存工具列表
            response = await self.session.list_tools()
            tools = response.tools
            tool_names = [tool.name for tool in tools]
            logger.info(f"已连接到服务器 ({self.name})，可用工具: {tool_names}")
            
            # 更新工具缓存
            await self._refresh_remote_tools()
            
            return {
                "success": True, 
                "message": f"已成功连接到MCP服务器: {self.mcp_url}",
                "available_tools": tool_names
            }
        except Exception as e:
            error_msg = f"连接MCP服务器失败: {traceback.format_exc()}"
            logger.error(error_msg)
            self.connected = False
            self.session = None
            return {"success": False, "error": error_msg}
    
    async def disconnect(self) -> Dict[str, Any]:
        """断开与MCP服务器的连接"""
        if not self.connected:
            return {"success": False, "error": "未连接到MCP服务器"}
        
        try:
            await self._close_client_connection()
            return {"success": True, "message": "已断开与MCP服务器的连接"}
        except Exception as e:
            error_msg = f"断开MCP服务器连接失败: {traceback.format_exc()}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _close_client_connection(self):
        """关闭客户端连接"""
        try:
            await self.exit_stack.aclose()
        except Exception as e:
            logger.error(f"关闭MCP连接失败: {traceback.format_exc()}")
        
        self.connected = False
        self.session = None
        self.stdio = None
        self.write = None

    async def ensure_connected(self):
        """确保已连接到MCP服务器，如未连接则尝试连接"""
        if not self.connected and not self.connecting:
            self.connecting = True
            logger.info(f"正在连接MCP服务器 ({self.name})...")
            result = await self.connect()
            self.connecting = False
            
            if result.get("success", False):
                logger.info(f"MCP服务器连接成功 ({self.name})")
            else:
                logger.warning(f"无法连接到MCP服务器 ({self.name})，部分依赖MCP的功能将不可用")
                
        return self.connected
    
    async def _refresh_remote_tools(self):
        """刷新远程工具列表"""
        if not self.connected or not self.session:
            return
        
        try:
            response = await self.session.list_tools()
            tools = response.tools
            
            # 清除现有远程工具
            # 创建工具字典存储远程工具
            remote_tools = {}
            
            # 添加MCP工具到工具注册表
            for tool in tools:
                remote_tools[tool.name] = {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema,
                    "execute": self._create_remote_tool_executor(tool.name),
                    "is_remote": True
                }
            
            # 合并到工具字典中
            for name, tool in remote_tools.items():
                if name not in self.tools:  # 避免覆盖本地工具
                    self.tools[name] = tool
            
            logger.info(f"已获取{len(remote_tools)}个远程MCP工具: {', '.join(remote_tools.keys())}")
        except Exception as e:
            logger.error(f"获取远程工具列表失败: {traceback.format_exc()}")
    
    def _create_remote_tool_executor(self, tool_name):
        """创建远程工具执行函数"""
        async def execute_remote_tool(**params):
            # 确保已连接
            if not await self.ensure_connected():
                return {"success": False, "error": "无法连接到MCP服务器"}
            
            try:
                if self.session:
                    result = await self.session.call_tool(tool_name, params)
                    return result.content
                else:
                    return {"success": False, "error": "MCP服务器未连接"}
            except Exception as e:
                logger.error(f"执行远程工具 {tool_name} 失败: {traceback.format_exc()}")
                return {"success": False, "error": str(e)}
        
        return execute_remote_tool
    
    async def execute_remote_tool(self, name: str, params: Dict[str, Any]) -> Any:
        """
        执行远程MCP工具
        
        参数:
            name: 工具名称
            params: 工具参数
            
        返回:
            工具执行结果
        """
        # 确保已连接
        if not await self.ensure_connected():
            return {"success": False, "error": "无法连接到MCP服务器"}
        
        if name not in self.tools or not self.tools[name].get("is_remote", False):
            await self._refresh_remote_tools()
            if name not in self.tools or not self.tools[name].get("is_remote", False):
                return {"success": False, "error": f"远程工具 {name} 不存在"}
        
        tool = self.tools[name]
        return await tool["execute"](**params)
    
    async def close(self):
        """关闭MCP服务器和客户端连接"""
        # 停止服务器
        if self.server_running:
            await self.stop_server()
        
        # 关闭客户端连接
        if self.connected:
            await self._close_client_connection()
    
    async def initialize(self):
        """
        初始化MCP服务器库，自动启动服务器
        在Agent启动时被调用
        """ 
        logger.info("正在自动初始化并启动MCP服务器...")
        result = await self.start_server()
        if result.get("success", False):
            logger.info("MCP服务器自动启动成功")
        else:
            logger.warning(f"MCP服务器自动启动失败: {result.get('error', '未知错误')}")
        
        # 尝试连接到远程服务器
        if self.mcp_url:
            logger.info("尝试连接到远程MCP服务器...")
            await self.ensure_connected()
            
        await super().initialize()
