[project]
name = "meowagent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alibabacloud-oss-v2>=1.1.1",
    "colorama>=0.4.6",
    "fastapi>=0.115.12",
    "function-schema>=0.4.5",
    "jinja2>=3.1.6",
    "json5>=0.10.0",
    "mcp[cli]>=1.5.0",
    "motor>=3.7.0",
    "openai>=1.68.2",
    "oss2>=2.19.1",
    "prompt-toolkit>=3.0.51",
    "pydantic>=2.11.3",
    "python-dotenv>=1.1.0",
    "pyyaml>=6.0.2",
    "textual-dev>=1.7.0",
    "tortoise-orm[asyncpg]>=0.25.0",
    "transformers>=4.51.1",
    "uuid>=1.30",
    "uvicorn>=0.34.0",
    "websockets>=15.0.1",
]

[dependency-groups]
dev = [
    "pyright>=1.1.398",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-xdist>=3.6.1",
    "watchfiles>=1.0.5",
    "yappi>=1.6.10",
]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-s"
testpaths = [
    "tests",
]

[[tool.uv.index]]
url = "http://mirrors.aliyun.com/pypi/simple/"

