"""
该模块定义了 MeowAgent 框架中工具库 (Library) 的核心基础架构。
它允许开发者创建、管理和集成各种工具，供智能体 (Agent) 调用。

主要功能和逻辑包括：

1.  **`Library` 抽象基类**:
    *   所有具体的工具库都应继承此类。
    *   负责管理一组相关的工具函数。
    *   在实例化时，通过 `config` 参数或直接参数（`name`, `description`, `prompt`）接收库的元数据。
    *   提供 `initialize` 和 `close` 方法，用于库的生命周期管理（例如，初始化连接、释放资源），子类可以覆盖这些方法。

2.  **工具自动发现与注册**:
    *   `@register_tool` 装饰器：用于标记一个异步方法 (async def) 为可供智能体调用的工具。如果标记的不是异步函数，会抛出 ValueError。
    *   `_auto_register_tools` 方法：在 `Library` 实例化时被调用，自动扫描类中所有被 `@register_tool` 装饰的方法，并调用 `register_tool` 方法进行注册。
    *   `register_tool` 方法 (实例方法)：
        *   使用 `function_schema.get_function_schema` 自动从工具函数的类型注解和文档字符串中提取参数模式 (input_schema) 和描述 (description)。
        *   工具的全局唯一名称格式为 `library_name.tool_function_name`。
        *   将工具的详细信息（包括名称、原始函数名、描述、执行函数引用、输入模式）存储在库实例的 `tools` 字典中。

3.  **工具元数据与检索**:
    *   `get_tools()`: 返回库中所有已注册工具的列表，每个工具包含其名称、描述和输入模式。
    *   `get_tool(name)`: 根据工具的全局唯一名称检索特定的工具信息。
    *   `get_library_details()`: 返回包含库名称、描述、提示词、所有工具列表以及工具数量的详细字典。

4.  **工具执行**:
    *   `execute_tool(name, params)`: 异步执行指定的工具。根据工具名称找到对应的函数，并将 `params` 作为关键字参数传递给该函数。

5.  **动态提示词管理**:
    *   `prompt` 属性：每个库可以关联一个提示词字符串，用于指导智能体如何使用该库中的工具。
    *   `get_prompt()`: 获取当前库的提示词。
    *   `update_prompt(new_prompt)`: 允许动态更新库的提示词。

该模块通过上述机制，为 MeowAgent 提供了一个标准化的、可扩展的工具系统。
开发者可以通过创建 `Library` 的子类，并使用 `@register_tool` 装饰器，
轻松地将自定义功能封装为工具，供智能体在对话或任务执行过程中按需调用。
"""

import os
import traceback
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Callable, List, Optional
from function_schema import get_function_schema
from log import logger

# 添加装饰器函数
def register_tool(func: Callable) -> Callable:
    """
    装饰器，用于标记需要注册的工具方法
    
    参数:
        func: 要注册为工具的函数
        
    返回:
        被装饰的函数
    """

    if not asyncio.iscoroutinefunction(func):
        raise ValueError("工具必须是异步函数")

    setattr(func, '_is_tool', True)
    return func

# 工具库抽象基类
class Library(ABC):
    """
    工具库抽象基类，用于管理一组相关的工具函数
    """
    def __init__(self, name: Optional[str] = None, description: Optional[str] = None, prompt: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        """
        初始化工具库
        
        参数:
            name: 工具库名称 (如果 config 中也提供了 'name', config 优先)
            description: 工具库描述 (如果 config 中也提供了 'description', config 优先)
            prompt: 可选的、描述库功能的提示词字符串 (如果 config 中也提供了 'prompt', config 优先)
            config: 可选的配置字典，可以覆盖 name, description, prompt
        """
        effective_config = config or {}
        self.name = effective_config.get('name', name)
        self.description = effective_config.get('description', description)
        self.prompt = effective_config.get('prompt', prompt) # 存储提示词
        self.tools = {}
        
        # 自动注册被装饰的方法
        self._auto_register_tools()
    
    def _auto_register_tools(self):
        """自动注册所有被标记为工具的方法"""
        logger.debug(f"库 {self.name} 开始自动注册工具...")
        
        registered_count = 0
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue
                
            attr = getattr(self, attr_name)
            if callable(attr) and getattr(attr, '_is_tool', False):
                logger.debug(f"库 {self.name} 发现工具方法: {attr_name}")
                self.register_tool(attr)
                registered_count += 1
                
        logger.debug(f"库 {self.name} 自动注册完成，共 {registered_count} 个工具")
    
    def register_tool(self, func: Callable):
        """
        注册工具到库中，使用function_schema自动生成参数模式
        
        参数:
            func: 工具函数，接受参数并返回结果
        """
        # 获取函数元数据
        schema = get_function_schema(func)
        original_name = schema["name"]
        
        # 使用"库名.工具名"的格式作为全局唯一名称
        name = f"{self.name}.{original_name}"
        
        logger.debug(f"库 {self.name} 注册工具: 原始名={original_name}, 全名={name}")
        
        # 使用函数的文档字符串作为描述
        description = schema.get("description")
        
        # 构建工具字典，确保处理可能不存在的parameters字段
        tool_dict = {
            "name": name,
            "original_name": original_name,  # 保存原始名称以便参考
            "description": description,
            "execute": func,
        }
        
        # 安全地添加input_schema
        if "parameters" in schema:
            tool_dict["input_schema"] = schema["parameters"]
        else:
            tool_dict["input_schema"] = {"type": "object", "properties": {}}
        
        self.tools[name] = tool_dict
        logger.debug(f"库 {self.name} 工具注册完成: {name}")
        
        # 记录当前库中的所有工具
        logger.debug(f"库 {self.name} 当前所有工具: {list(self.tools.keys())}")
    
    def get_tools(self) -> List[Dict]:
        """
        获取库中所有工具
        
        返回:
            工具列表
        """
        return [
            {
                "name": tool["name"],  # 已经是"库名.工具名"格式
                "description": tool["description"],
                "input_schema": tool["input_schema"]
            } for tool in self.tools.values()
        ]
    
    def get_tool(self, name: str) -> Optional[Dict]:
        """
        根据名称获取工具
        
        参数:
            name: 工具名称（应为"库名.工具名"格式）
            
        返回:
            工具信息，如果不存在则返回None
        """
        # 直接使用全名查找
        return self.tools.get(name)
    
    def get_prompt(self) -> Optional[str]:
        """返回该库的提示词"""
        return self.prompt
        
    def update_prompt(self, new_prompt: str) -> None:
        """
        动态更新工具库的提示词
        
        参数:
            new_prompt: 新的提示词内容
        """
        self.prompt = new_prompt
        # 这里可以添加额外的提示词更新逻辑，比如通知观察者等

    async def execute_tool(self, name: str, params: Dict[str, Any]) -> Any:
        """
        执行工具
        
        参数:
            name: 工具名称（应为"库名.工具名"格式）
            params: 工具参数
            
        返回:
            工具执行结果
        """
        logger.debug(f"库 {self.name} 尝试执行工具: {name}, 参数: {params}")
        logger.debug(f"库 {self.name} 中的所有工具: {list(self.tools.keys())}")
        
        if name not in self.tools:
            error_msg = f"工具 {name} 不存在于库 {self.name} 中"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        tool = self.tools[name]
        logger.debug(f"找到工具 {name}, 执行中...")
        
        # 解包参数字典传递给工具函数
        result = await tool["execute"](**params)
        logger.debug(f"工具 {name} 执行结果: {result}")
        return result
    
    async def close(self):
        """关闭库连接，子类根据需要覆盖此方法"""
        pass
    
    async def initialize(self):
        """
        初始化库，在Agent启动时被调用
        子类可以覆盖此方法来实现自定义初始化逻辑
        """
        pass

    async def get_library_details(self) -> Dict[str, Any]:
        """
        获取库的详细信息，
        返回:
            Dict[str, Any]: 包含库详细信息的字典
        """
        # 获取工具列表（调用已有方法）
        tools = self.get_tools()
        
        # 构建详细信息字典
        details = {
            "name": self.name,
            "description": self.description,
            "prompt": self.get_prompt(),
            "tools": tools,
            # 添加其他可能有用的元数据
            "tool_count": len(tools)
        }
        
        return details

