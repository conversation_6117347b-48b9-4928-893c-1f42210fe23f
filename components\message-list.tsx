"use client"
import {
  Dnd<PERSON>ontext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
  type DragStartEvent,
  type DragOverEvent,
  type UniqueIdentifier,
  DragOverlay,
} from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { useState, useEffect } from "react"
import type { ChatMessageDisplay } from "@/types/message"
import SortableMessage from "@/components/sortable-message"
import MessageItem from "@/components/message-item"

interface MessageListProps {
  messages: ChatMessageDisplay[]
  onMessagesChange: (messages: ChatMessageDisplay[]) => void
  onDeleteMessage?: (messageId: string) => void
  onUpdateMessage?: (message: ChatMessageDisplay) => void
}

export default function MessageList({
  messages,
  onMessagesChange,
  onDeleteMessage,
  onUpdateMessage,
}: MessageListProps) {
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null)
  const [overId, setOverId] = useState<UniqueIdentifier | null>(null)
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id)
  }

  const handleDragOver = (event: DragOverEvent) => {
    setOverId(event.over?.id || null)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setActiveId(null)
    setOverId(null)

    if (over && active.id !== over.id) {
      const oldIndex = messages.findIndex((message) => message.id === String(active.id))
      const newIndex = messages.findIndex((message) => message.id === String(over.id))

      if (oldIndex !== -1 && newIndex !== -1) {
        const newMessages = arrayMove(messages, oldIndex, newIndex)
        onMessagesChange(newMessages)
      }
    }
  }

  const handleDragCancel = () => {
    setActiveId(null)
    setOverId(null)
  }

  const handleMessageDelete = (id: string) => {
    if (onDeleteMessage) {
      onDeleteMessage(id);
    } else {
      const newMessages = messages.filter((message) => message.id !== id);
      onMessagesChange(newMessages);
      console.warn("MessageList: onDeleteMessage prop not provided, local delete fallback used (undesirable).")
    }
  }

  const activeMessage = activeId ? messages.find((message) => message.id === String(activeId)) : null

  const getInsertPosition = () => {
    if (!activeId || !overId || activeId === overId) return null

    const activeIndex = messages.findIndex((message) => message.id === String(activeId))
    const overIndex = messages.findIndex((message) => message.id === String(overId))

    if (activeIndex === -1 || overIndex === -1) return null;

    return overIndex
  }

  const insertPosition = getInsertPosition()

  if (!isMounted) {
    return (
      <div className="space-y-4">
        {messages.map((message) => (
          <div key={message.id} className="relative">
            <MessageItem message={message} onEdit={() => {}} onDelete={() => {}} />
          </div>
        ))}
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <SortableContext items={messages.map((message) => message.id)} strategy={verticalListSortingStrategy}>
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div key={message.id} className="relative">
              {activeId && insertPosition === index && (
                <div className="absolute -top-2 left-0 right-0 h-1 bg-blue-500 rounded-full animate-pulse z-10" />
              )}
              <SortableMessage 
                message={message} 
                onUpdate={onUpdateMessage}
                onDelete={handleMessageDelete} 
              />
            </div>
          ))}

          {activeId && insertPosition === messages.length && (
            <div className="relative h-2">
              <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500 rounded-full animate-pulse z-10" />
            </div>
          )}
        </div>
      </SortableContext>

      <DragOverlay adjustScale={false} zIndex={1000}>
        {activeId && activeMessage ? (
          <MessageItem
            message={activeMessage}
            onEdit={() => {}}
            onDelete={() => {}}
            isDragging={true}
            showDragHandle={true}
          />
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
