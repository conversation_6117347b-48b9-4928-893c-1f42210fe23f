---

## 工作流开发指南

### 1. 引言

MeowAgent 提供了工作流机制来有效引导LLM智能体执行任务。框架支持**使用Python代码编写工作流**。

这种方式结合了Python语言的能力与LLM的智能，实现了更灵活的任务流程编排。通过Python编写工作流，开发者可以：

- 使用条件语句和循环控制LLM的执行流程
- 通过变量实现动态状态管理
- 与现有Python库和项目代码集成
- 处理复杂的多步骤任务逻辑

作为MeowAgent框架的核心组件之一，工作流引擎与其他功能模块无缝集成：

- **工具系统**：支持本地Python工具库和远程MCP工具服务器
- **持久化长时记忆**：与工作流协同工作，在步骤间保持状态
- **智能文档处理**：在工作流中可直接调用文档检索能力
- **多智能体管理**：可通过工作流协调多个智能体的任务

**MCP工具集成**：MeowAgent通过[模型上下文协议（MCP）](https://modelcontextprotocol.io/introduction)支持多种外部工具。只需在项目根目录的`mcp.json`中配置MCP服务器信息，即可在工作流中调用这些工具。MeowAgent支持：

- 细粒度工具指定：可在`ExecuteStep`的`ActionDefinition`中具体指定需要调用的MCP工具
- 库级别指定：也可通过库名统一引用一组相关工具，简化工作流定义

本指南将介绍如何创建和实现Python工作流，以及如何与LLM智能体和多种工具交互。

**主要优势：**

*   **控制流程:** 使用Python的`if/else`、`for/while`循环实现条件逻辑和流程控制。
*   **代码复用:** 调用项目中现有的Python函数和库。
*   **状态管理:** 使用Python变量和数据结构跟踪和管理状态。
*   **表达能力:** 对于复杂逻辑，Python比YAML更直观和易于维护。
*   **生态集成:** 可以集成Python的第三方库。
*   **工具协同:** 无缝集成本地和MCP远程工具，扩展智能体能力。

### 2. 先决条件

*   熟悉 Python 基础语法，特别是异步编程 (`async`/`await`) 和生成器 (`yield`)。
*   了解 MeowAgent 的基本概念，如 Agent、Library、WorkflowStep（工作流步骤）等。
*   已设置好 MeowAgent 开发环境。
*   （可选）如需MCP工具，确保已配置好相应的MCP服务器。

### 3. 创建 Python 工作流文件

1.  **位置:** Python 工作流文件应放置在项目根目录下的 `workflows/` 文件夹中。
2.  **命名:** 文件名（不含 `.py` 后缀）将作为该工作流的名称被框架识别和加载。例如，`my_research_workflow.py` 文件定义的工作流将被识别为 `my_research_workflow`。
3.  **必要组件:** 每个 Python 工作流文件至少需要包含一个名为 `steps` 的**异步生成器函数**。可以选择性地包含元数据。

### 4. Python 工作流的核心结构

一个标准的 Python 工作流文件包含以下部分：

```python
# workflows/disk_manager_workflow.py

# 1. 导入必要的类型
from typing import Dict, Any, AsyncGenerator, List
from libraries.workflow_models import WorkflowStep, ExecuteStep, ConditionStep, GenerateStep, HaltStep, UserInputStep, ActionDefinition
from message import UserMessage
from log import logger

# 2. (可选) 定义元数据
WORKFLOW_DESCRIPTION = "Linux系统磁盘管理助手：自动检查、分析和扩容逻辑卷"
INITIAL_REGISTERS: Dict[str, Any] = {
    "machine_info": {},
    "disk_info": {
        "physical_volumes": [],
        "volume_groups": [],
        "logical_volumes": [],
        "filesystems": [],
        "usage_stats": {}
    },
    "expansion_candidates": [],
    "last_operation": None
}

# 3. 定义核心的异步生成器函数
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    磁盘管理工作流：
    1. 收集系统信息和磁盘状态
    2. 分析空间使用情况
    3. 确定是否需要扩容及扩容方案
    4. 在确认后执行扩容操作
    """
    # 获取用户需求
    user_message: UserMessage = yield UserInputStep(
        name="get_initial_request", 
        description="询问用户的磁盘管理需求，了解需要监控或扩容的磁盘。"
    )
    
    # 获取机器基本信息
    yield ExecuteStep(
        name="collect_system_info",
        description="收集系统基本信息",
        actions=[
            ActionDefinition(names=["system-tools.execute_command"], min_calls=1, max_calls=1),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
        ]
    )
    
    # 获取物理卷和卷组信息
    yield ExecuteStep(
        name="check_physical_volumes",
        description="查询物理卷和卷组信息",
        actions=[
            ActionDefinition(names=["system-tools.execute_command"], min_calls=2, max_calls=5)
        ]
    )
    
    # 获取逻辑卷信息
    yield ExecuteStep(
        name="check_logical_volumes",
        description="查询逻辑卷信息和使用情况",
        actions=[
            ActionDefinition(names=["system-tools.execute_command"], min_calls=2, max_calls=5),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
        ]
    )
    
    # 分析文件系统使用情况
    yield ExecuteStep(
        name="analyze_filesystem_usage",
        description="分析文件系统使用情况",
        actions=[
            ActionDefinition(names=["system-tools.execute_command"], min_calls=1, max_calls=3)
        ]
    )
    
    # 判断是否需要扩容
    needs_expansion = yield ConditionStep(
        name="determine_expansion_need",
        description="判断是否存在需要扩容的磁盘",
        condition_description="根据收集的信息，判断是否有磁盘使用率超过阈值(如80%)需要扩容?"
    )
    
    if needs_expansion:
        # 生成扩容建议
        yield GenerateStep(
            name="generate_expansion_plan",
            description="生成磁盘扩容建议",
            content_description="基于当前的磁盘使用情况和可用空间，提出详细的扩容方案，包括建议扩容的逻辑卷、扩容大小和操作步骤。"
        )
        
        # 获取用户确认
        user_approval: UserMessage = yield UserInputStep(
            name="get_expansion_approval", 
            description="请用户确认是否执行扩容操作，以及具体扩容哪个磁盘。"
        )
        
        # 检查用户是否批准扩容
        is_approved = yield ConditionStep(
            name="check_approval",
            description="检查用户是否批准执行扩容",
            condition_description="用户是否明确同意执行扩容操作?"
        )
        
        if is_approved:
            # 执行扩容操作
            yield ExecuteStep(
                name="perform_disk_expansion",
                description="执行磁盘扩容操作",
                actions=[
                    ActionDefinition(names=["system-tools.execute_command"], min_calls=1, max_calls=5),
                    ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
                ]
            )
            
            # 验证扩容结果
            yield ExecuteStep(
                name="verify_expansion",
                description="验证扩容操作结果",
                actions=[
                    ActionDefinition(names=["system-tools.execute_command"], min_calls=1, max_calls=3)
                ]
            )
            
            # 报告扩容结果
            yield GenerateStep(
                name="report_expansion_result",
                description="报告扩容结果",
                content_description="详细说明扩容操作的执行过程、结果和当前状态。"
            )
        else:
            # 用户不同意扩容，提供手动指导
            yield GenerateStep(
                name="provide_manual_guidance",
                description="提供手动扩容指导",
                content_description="提供详细的手动扩容步骤说明，包括所需命令和注意事项。"
            )
    else:
        # 不需要扩容，生成系统状态报告
        yield GenerateStep(
            name="generate_status_report",
            description="生成系统磁盘状态报告",
            content_description="总结当前系统磁盘使用情况，确认所有磁盘空间充足。"
        )
    
    # 询问是否需要设置监控
    needs_monitoring = yield ConditionStep(
        name="check_monitoring_need",
        description="询问是否需要设置磁盘监控",
        condition_description="用户是否表示需要设置长期磁盘使用监控?"
    )
    
    if needs_monitoring:
        # 设置磁盘监控
        yield ExecuteStep(
            name="setup_disk_monitoring",
            description="设置磁盘使用监控",
            actions=[
                ActionDefinition(names=["system-tools.execute_command"], min_calls=1, max_calls=3),
                ActionDefinition(names=["memory.write_file"], min_calls=1, max_calls=1)
            ]
        )
    
    # 结束工作流
    yield HaltStep(name="complete_workflow", description="完成磁盘管理工作流并总结结果")
```

**关键点解释:**

*   **元数据 (可选):**
    *   `WORKFLOW_DESCRIPTION` (类型: `str`): 对工作流功能的描述。框架会读取此变量以在 `list_available_workflows` 等工具中显示。
    *   `INITIAL_REGISTERS` (类型: `Dict[str, Any]`): 定义工作流启动时的初始寄存器值。这些寄存器可以在工作流执行期间被读取或修改（例如通过 `workflow.set_register_value` 工具）。
*   **`steps` 异步生成器函数:**
    *   **签名:** 必须是 `async def steps() -> AsyncGenerator[WorkflowStep, Any]:`。
        *   `async def`: 表明这是一个异步函数，可以使用 `await`。
        *   `AsyncGenerator[WorkflowStep, Any]`: 类型提示，表明：
            *   它是一个异步生成器。
            *   每次 `yield` 出去的值是 `WorkflowStep` 的某个子类实例（如 `NopStep`, `ExecuteStep` 等）。
            *   可以通过 `.asend()` 方法接收值，如 `ConditionStep` 的布尔结果或 `UserInputStep` 的用户消息。
    *   **`yield` 关键字:** 这是 Python 工作流的核心。
        *   `yield <WorkflowStep 对象>`: 当执行到 `yield` 时，生成器函数会**暂停**，并将 `<WorkflowStep 对象>` 返回给调用者（`WorkflowLibrary`）。`WorkflowLibrary` 会根据这个对象的类型和内容来指导 Agent/LLM 的下一步行动（例如，要求 LLM 调用工具、生成文本或判断条件）。
        *   **接收返回值:** 
            * **`ConditionStep`:** 当 LLM 调用了 `condition_branch(result)`，`WorkflowLibrary` 会使用 `generator.asend(result)` 将布尔结果 `result` 发送回生成器。`yield ConditionStep(...)` 表达式的值就是这个 `result`。
            * **`UserInputStep`:** 当用户提供输入后，`WorkflowLibrary` 会使用 `generator.asend(user_message)` 将 `UserMessage` 对象发送回生成器。`yield UserInputStep(...)` 表达式的值就是这个 `user_message` 对象。

### 5. WorkflowStep 类型详解

你需要从 `libraries.workflow_models` 导入并在 `steps` 函数中 `yield` 这些步骤类的实例。

*   **`NopStep` (无操作):**
        *   **注意: 不推荐使用** - 在新的工作流设计中应避免使用，通常可以直接使用更具语义的其他Step类型。
        *   用途：表示一个空操作，通常用于流程的开始、结束或逻辑标记点。
        *   关键参数：`name` (推荐，用于标识), `description`。
        *   执行：`WorkflowLibrary` 收到 `NopStep` 后，通常会立即自动调用生成器的 `anext()` 来获取下一步。
        *   示例：`yield NopStep(name="initialization_complete", description="初始化完成")`

*   **`ExecuteStep` (执行工具):**
    *   用途：指示 LLM 调用一个或多个工具。
    *   关键参数：`name`, `description`, `actions` (类型: `List[ActionDefinition]`)。
        *   `ActionDefinition`: 定义一组相关的工具调用要求。
            *   `names` (类型: `List[str]`): 允许调用的工具/库名称列表（例如 `["memory.add_memory"]` 或 `["web_search", "document"]`）。
            *   `min_calls` (类型: `int`, 默认 1): 这组工具至少需要被调用的次数。
            *   `max_calls` (类型: `int`, 默认 1): 这组工具最多能被调用的次数。
    *   执行：LLM 需要根据 `actions` 的要求调用工具。`WorkflowExecutor` 会跟踪每个 `ActionDefinition` 的调用次数。当所有 `min_calls` 满足后，LLM 可以选择继续调用（直到 `max_calls`）或调用 `workflow.next_step()`（如果允许）。如果所有 `max_calls` 都已满足，`WorkflowExecutor` 可能会自动调用 `anext()` 前进。
    *   示例：
        ```python
        yield ExecuteStep(
            name="search_web_and_docs",
            description="搜索网页和本地文档",
            actions=[
                ActionDefinition(names=["web_search.search"], min_calls=1, max_calls=2), # 搜索网页 1-2 次
                ActionDefinition(names=["document.search_in_documents"], min_calls=0, max_calls=1) # 搜索文档 0-1 次
            ]
        )
        ```

*   **`ConditionStep` (条件判断):**
    *   用途：指示 LLM 进行逻辑判断，并将结果反馈给 Python 工作流。
    *   关键参数：`name`, `description`, `condition_description` (给 LLM 的自然语言条件描述)。
    *   执行：LLM 需要评估 `condition_description`，然后**必须**调用 `workflow.condition_branch(result: bool)`。这个 `result` 会通过 `generator.asend(result)` 发送回 `steps` 函数，并成为 `yield ConditionStep(...)` 表达式的值。
    *   示例：
        ```python
        # 等待 LLM 调用 condition_branch 并接收结果
        user_is_satisfied = yield ConditionStep(
            name="check_user_satisfaction",
            description="判断用户是否对当前结果满意",
            condition_description="分析用户的最新回复，判断他是否对之前的回答表示满意或需要进一步信息？"
        )
        
        # 在 Python 中直接使用结果
        if user_is_satisfied:
            logger.info("用户满意，继续下一步。")
            # yield ... # 下一步骤
        else:
            logger.info("用户不满意，尝试提供更多信息。")
            # yield ... # 不同的步骤
        ```

*   **`UserInputStep` (等待用户输入):**
    *   用途：暂停工作流执行，等待用户提供输入，并将用户输入传递回工作流。
    *   关键参数：`name`, `description`。
    *   执行：LLM 会生成一个简短的提示给用户，并且工作流会暂停执行。当用户提供输入后，`WorkflowExecutor` 会将 `UserMessage` 对象发送回生成器，作为 `yield UserInputStep(...)` 表达式的值。
    *   示例：
        ```python
        # 等待用户输入并接收 UserMessage 对象
        user_message: UserMessage = yield UserInputStep(
            name="get_user_preference",
            description="等待用户输入他们的偏好。"
        )
        
        # 在 Python 中直接使用用户输入内容
        user_input = user_message.content
        logger.info(f"用户输入了: {user_input}")
        
        # 根据用户输入决定下一步
        if "帮助" in user_input:
            yield GenerateStep(
                name="provide_help",
                description="提供帮助信息"
            )
        else:
            # 其他处理逻辑
            pass
        ```

*   **`GenerateStep` (生成回复):**
    *   用途：指示 LLM 直接生成文本回复给用户，不调用任何工具。
    *   关键参数：`name`, `description`, `content_description` (对期望生成内容的描述), `wait_user` (类型: `bool`, 默认 `False`)。
        *   `wait_user=False`: 生成回复后，`WorkflowExecutor` 会自动调用 `anext()` 获取下一步。
        *   `wait_user=True`: 生成回复后，工作流暂停，等待用户下一次输入后，`WorkflowExecutor` 才会调用 `anext()` 获取下一步。
        *   **注意:** `wait_user=True` 是旧机制，建议使用新的 `UserInputStep` 来等待用户输入。
    *   示例：
        ```python
        yield GenerateStep(
            name="summarize_findings",
            description="总结搜索结果并回复用户",
            content_description="根据之前的网页搜索和文档搜索结果，生成一个简洁的摘要。"
        )
        
        # 如需等待用户输入，建议使用 UserInputStep
        user_message = yield UserInputStep(name="wait_for_feedback", description="等待用户对摘要的反馈。")
        ```

*   **`HaltStep` (终止流程):**
    *   用途：明确指示工作流执行结束。
    *   关键参数：`name`, `description`。
    *   执行：`WorkflowLibrary` 收到 `HaltStep` 后，通常会认为工作流已完成。LLM 被指示生成最终的结束语。之后生成器不应再 `yield` 任何步骤。
    *   示例：`yield HaltStep(name="final_summary", description="提供最终总结并结束对话")`

*   **`JumpStep` (跳转):**
    *   虽然存在 `JumpStep` 模型，但在 Python 工作流中**很少直接使用**。因为 Python 的 `if/else`, 循环等控制流结构天然地实现了跳转逻辑，无需像 YAML 那样显式定义跳转目标。

### 6. 控制流与状态管理

Python工作流可以使用标准Python代码控制执行流程：

*   **循环 (`while`, `for`):** 实现重复执行某段逻辑，直到满足退出条件。
*   **条件分支 (`if`, `elif`, `else`):** 根据 `ConditionStep` 返回的结果、`UserInputStep` 返回的用户输入，或内部变量的值，执行不同的步骤序列。
*   **变量:** 在 `steps` 函数内部定义的变量可以用来跟踪状态（例如重试次数、收集到的信息列表等）。
*   **函数调用:** 可以将复杂的逻辑封装在辅助函数中（同步或异步），并在 `steps` 生成器中调用它们。

```python
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    max_attempts = 3
    attempts = 0
    search_results = []

    while attempts < max_attempts:
        attempts += 1
        yield ExecuteStep(
            name=f"attempt_{attempts}_search",
            description=f"第 {attempts} 次尝试搜索",
            actions=[ActionDefinition(names=["web_search.search"], min_calls=1, max_calls=1)]
        )
        
        # (假设 web_search.search 工具调用的结果会通过某种方式更新状态或寄存器)
        # 此处仅为示例，实际可能需要更复杂的交互来获取工具结果
        
        is_sufficient = yield ConditionStep(
            name=f"check_sufficiency_{attempts}",
            description="检查搜索结果是否足够",
            condition_description=f"第 {attempts} 次搜索的结果是否已经足够回答用户的问题？"
        )
        
        if is_sufficient:
            # 结果足够，跳出循环
            break
            
    if attempts == max_attempts:
        yield GenerateStep(name="give_up", description="告知用户无法找到足够信息")
        # 获取用户的下一步指示
        user_message = yield UserInputStep(name="get_next_instruction", description="等待用户提供下一步指示。")
    else:
        yield GenerateStep(name="present_results", description="整理并呈现找到的结果")
        # 获取用户反馈
        user_message = yield UserInputStep(name="get_feedback", description="等待用户对结果的反馈。")

    yield HaltStep(name="end_process", description="结束处理流程")
```

### 7. 加载与执行机制 (内部)

1.  `WorkflowLibrary` 在初始化时会扫描 `workflows/` 目录。
2.  遇到 `.py` 文件，它会导入模块，查找 `WORKFLOW_DESCRIPTION`, `INITIAL_REGISTERS` 和 `steps` 异步生成器函数。
3.  当一个 Python 工作流被设置为当前工作流时 (`set_current_workflow`)，`WorkflowLibrary` 会调用 `steps()` 函数，获取一个**异步生成器对象**，并将其存储在 `WorkflowState` 中。
4.  `WorkflowLibrary` (通过 `WorkflowExecutor`) 会调用 `await generator.__anext__()` 来获取第一个 `WorkflowStep`。
5.  根据返回的 `WorkflowStep` 指导 LLM。
6.  如果 LLM 调用了 `condition_branch(result)`，`WorkflowExecutor` 会调用 `await generator.asend(result)` 将结果发送回生成器，并获取下一个 `WorkflowStep`。
7.  如果用户提供了输入（对应于 `UserInputStep`），`WorkflowExecutor` 会调用 `await generator.asend(user_message)` 将用户消息对象发送回生成器。
8.  如果需要前进到下一步（例如处理 `NopStep`，或 `GenerateStep` 且 `wait_user=False` 之后，或 `ExecuteStep` 自动前进时），`WorkflowExecutor` 会调用 `await generator.__anext__()`。
9.  这个过程一直持续，直到生成器函数执行完毕（隐式或显式 `return`，或者 `yield HaltStep` 后不再 `yield`）。

### 8. 示例

以下是三个复杂示例：

#### 示例 1: Kubernetes 集群运维助手

这个工作流展示了如何利用 MCP 工具和长期记忆构建 Kubernetes 集群监控与问题诊断助手。

```python
# workflows/k8s_assistant_workflow.py
import json
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, List
from libraries.workflow_models import (
    WorkflowStep, ExecuteStep, ConditionStep, 
    GenerateStep, HaltStep, UserInputStep, ActionDefinition
)
from message import UserMessage
from log import logger

WORKFLOW_DESCRIPTION = "Kubernetes 集群运维助手：监控集群状态、诊断问题、执行修复方案"
INITIAL_REGISTERS: Dict[str, Any] = {
    "cluster_contexts": [],
    "monitored_namespaces": [],
    "recent_incidents": [],
    "diagnosis_history": {},
    "last_action_timestamp": None
}

async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    Kubernetes 集群运维助手工作流：
    1. 获取用户问题或请求
    2. 集群状态检查与问题诊断
    3. 生成修复方案并征求用户确认
    4. 执行修复并记录
    """
    # 获取用户问题或请求
    user_message: UserMessage = yield UserInputStep(
        name="get_user_request",
        description="询问用户的 Kubernetes 集群问题或请求"
    )
    
    # 检索历史记忆和文档资料
    yield ExecuteStep(
        name="retrieve_context",
        description="检索集群历史记录和相关文档",
        actions=[
            ActionDefinition(names=[
                "memory.list_directory",
                "memory.change_directory",
                "memory.read_file"
            ], min_calls=1, max_calls=5),
            ActionDefinition(names=["document.search_in_documents"], min_calls=1, max_calls=2)
        ]
    )
    
    # 使用 k8s-tools MCP 服务获取集群状态
    yield ExecuteStep(
        name="gather_cluster_state",
        description="使用 MCP k8s-tools 检查集群状态",
        actions=[
            ActionDefinition(names=[
                "k8s-tools.get_nodes",
                "k8s-tools.get_pods",
                "k8s-tools.get_deployments",
                "k8s-tools.get_services",
                "k8s-tools.describe_resource"
            ], min_calls=2, max_calls=10)
        ]
    )
    
    # 分析问题类型
    problem_type = yield ConditionStep(
        name="analyze_problem_type",
        description="判断问题类型",
        condition_description="这是资源不足问题还是配置错误？如果是资源不足返回 True，如果是配置错误返回 False"
    )
    
    # 根据问题类型执行不同诊断逻辑
    if problem_type:  # 资源不足问题
        yield ExecuteStep(
            name="resource_usage_analysis",
            description="分析资源使用情况",
            actions=[
                ActionDefinition(names=[
                    "k8s-tools.get_resource_usage",
                    "k8s-tools.get_resource_limits"
                ], min_calls=1, max_calls=3),
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
            ]
        )
    else:  # 配置错误问题
        yield ExecuteStep(
            name="configuration_analysis",
            description="分析配置问题",
            actions=[
                ActionDefinition(names=[
                    "k8s-tools.get_config",
                    "k8s-tools.validate_yaml",
                    "k8s-tools.compare_configs"
                ], min_calls=1, max_calls=3),
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
            ]
        )
    
    # 在记忆系统中查找类似问题的历史解决方案
    yield ExecuteStep(
        name="search_similar_solutions",
        description="在记忆中搜索类似问题的解决方案",
        actions=[
            ActionDefinition(names=[
                "memory.change_directory",
                "memory.list_directory",
                "memory.read_file"
            ], min_calls=1, max_calls=5)
        ]
    )
    
    # 生成解决方案
    yield GenerateStep(
        name="generate_solution",
        description="基于诊断结果和历史数据生成解决方案",
        content_description="提供详细的问题分析和解决步骤，包含命令示例和可能的替代方案。"
    )
    
    # 获取用户确认
    user_confirmation: UserMessage = yield UserInputStep(
        name="get_user_confirmation",
        description="请用户确认是否执行建议的解决方案"
    )
    
    # 检查用户是否同意执行
    should_execute = yield ConditionStep(
        name="check_user_approval",
        description="检查用户是否同意执行解决方案",
        condition_description="用户是否明确同意执行提出的解决方案？"
    )
    
    if should_execute:
        # 执行解决方案
        yield ExecuteStep(
            name="execute_solution",
            description="执行解决方案",
            actions=[
                ActionDefinition(names=[
                    "k8s-tools.apply_yaml",
                    "k8s-tools.scale_resource",
                    "k8s-tools.restart_pods",
                    "k8s-tools.update_config"
                ], min_calls=1, max_calls=5)
            ]
        )
        
        # 记录解决方案和结果
        timestamp = datetime.now().isoformat()
        yield ExecuteStep(
            name="record_solution",
            description="记录解决方案和结果",
            actions=[
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1),
                ActionDefinition(names=[
                    "memory.change_directory",
                    "memory.write_file"
                ], min_calls=1, max_calls=2)
            ]
        )
        
        # 验证解决方案效果
        yield ExecuteStep(
            name="verify_solution",
            description="验证解决方案效果",
            actions=[
                ActionDefinition(names=[
                    "k8s-tools.get_resource_status",
                    "k8s-tools.wait_for_condition",
                    "k8s-tools.compare_states"
                ], min_calls=1, max_calls=3)
            ]
        )
        
        # 生成执行报告
        yield GenerateStep(
            name="generate_execution_report",
            description="生成详细的执行报告",
            content_description="详细说明执行的操作、结果和验证过程，包括任何警告或建议。"
        )
    else:
        # 如果用户不同意执行，提供手动指导
        yield GenerateStep(
            name="provide_manual_guidance",
            description="提供手动解决步骤指南",
            content_description="详细说明用户可以手动执行的步骤，包含每个命令的解释和预期结果。"
        )
    
    # 询问是否需要进一步协助
    need_more_help: UserMessage = yield UserInputStep(
        name="check_further_assistance",
        description="询问用户是否需要进一步协助"
    )
    
    needs_more = yield ConditionStep(
        name="evaluate_further_help",
        description="评估是否需要进一步帮助",
        condition_description="用户是否表示需要更多帮助或有新问题？"
    )
    
    if needs_more:
        # 如果用户表示需要更多帮助，生成反馈并继续新一轮对话
        yield GenerateStep(
            name="acknowledge_continuation",
            description="确认继续协助",
            content_description="确认将继续提供帮助，并请用户详细描述新问题或额外需求。"
        )
        # 工作流会回到开始，用户可以提出新问题
    else:
        # 结束本次交互
        yield HaltStep(name="complete_interaction", description="完成交互并总结")
```

#### 示例 2: 大规模文档处理与分析流程

这个工作流展示了如何利用MeowAgent处理大规模文档集合，执行多阶段分析并生成结构化洞见。

```python
# workflows/document_processing_workflow.py
import os
from typing import Dict, Any, AsyncGenerator, List, Set
from libraries.workflow_models import (
    WorkflowStep, ExecuteStep, ConditionStep, 
    GenerateStep, HaltStep, UserInputStep, ActionDefinition
)
from message import UserMessage
from log import logger

WORKFLOW_DESCRIPTION = "大规模文档处理流程：自动分析、分类、提取见解并生成报告"
INITIAL_REGISTERS: Dict[str, Any] = {
    "document_collections": {},
    "processing_stats": {
        "total_documents": 0,
        "processed_documents": 0,
        "extraction_counts": {},
        "categories": {}
    },
    "extraction_schema": None,
    "category_definitions": None,
    "report_format": "markdown"
}

async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    大规模文档处理工作流：
    1. 获取用户需求和处理参数
    2. 对文档集合执行多阶段分析
    3. 生成结构化报告并收集用户反馈
    """
    # 获取用户的文档处理需求
    user_message: UserMessage = yield UserInputStep(
        name="get_document_task",
        description="询问用户的文档处理需求、目标和特殊要求"
    )
    
    # 使用 MCP document-processor 服务获取可用文档集合
    yield ExecuteStep(
        name="list_document_collections",
        description="列出可用的文档集合",
        actions=[
            ActionDefinition(names=["document-processor.list_collections"], min_calls=1, max_calls=1)
        ]
    )
    
    # 获取用户选择的文档集合
    collection_selection: UserMessage = yield UserInputStep(
        name="get_collection_selection",
        description="请用户选择要处理的文档集合"
    )
    
    # 获取选定集合的元数据
    yield ExecuteStep(
        name="get_collection_metadata",
        description="获取选定文档集合的元数据",
        actions=[
            ActionDefinition(names=["document-processor.get_collection_metadata"], min_calls=1, max_calls=1),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
        ]
    )
    
    # 创建处理计划
    yield GenerateStep(
        name="create_processing_plan",
        description="创建文档处理计划",
        content_description="基于文档集合的特性和用户需求，制定详细的处理计划，包括处理阶段、抽取目标和期望输出。"
    )
    
    # 获取用户对处理计划的确认
    plan_feedback: UserMessage = yield UserInputStep(
        name="get_plan_feedback",
        description="获取用户对处理计划的反馈和修改建议"
    )
    
    # 确定提取模式和分类标准
    yield ExecuteStep(
        name="define_extraction_schema",
        description="定义提取模式和分类标准",
        actions=[
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=3),
            ActionDefinition(names=["memory.write_file"], min_calls=1, max_calls=2)
        ]
    )
    
    # 使用 MCP 服务执行文档预处理
    yield ExecuteStep(
        name="preprocess_documents",
        description="执行文档预处理",
        actions=[
            ActionDefinition(names=[
                "document-processor.convert_formats",
                "document-processor.clean_text",
                "document-processor.detect_languages"
            ], min_calls=1, max_calls=5)
        ]
    )
    
    # 检查预处理结果
    preprocess_successful = yield ConditionStep(
        name="check_preprocessing",
        description="检查预处理是否成功",
        condition_description="文档预处理是否顺利完成，没有严重错误？"
    )
    
    if not preprocess_successful:
        # 处理预处理错误
        yield ExecuteStep(
            name="handle_preprocessing_errors",
            description="处理预处理错误",
            actions=[
                ActionDefinition(names=["document-processor.get_error_logs"], min_calls=1, max_calls=1),
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
            ]
        )
        
        # 向用户报告错误
        yield GenerateStep(
            name="report_preprocessing_errors",
            description="报告预处理错误",
            content_description="详细说明预处理阶段遇到的错误，可能的原因和建议解决方法。"
        )
        
        # 询问用户是否继续
        continue_decision: UserMessage = yield UserInputStep(
            name="get_continuation_decision",
            description="询问用户是否继续处理"
        )
        
        should_continue = yield ConditionStep(
            name="check_continuation",
            description="检查是否继续处理",
            condition_description="用户是否希望跳过有问题的文档继续处理？"
        )
        
        if not should_continue:
            yield HaltStep(name="terminate_processing", description="终止处理并总结问题")
            return
    
    # 主处理循环 - 根据文档数量可能需要分批处理
    yield ExecuteStep(
        name="set_batch_processing",
        description="设置批处理参数",
        actions=[
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
        ]
    )
    
    # 使用 document-processor 进行内容提取
    yield ExecuteStep(
        name="extract_document_content",
        description="从文档中提取关键内容",
        actions=[
            ActionDefinition(names=[
                "document-processor.extract_entities",
                "document-processor.extract_topics",
                "document-processor.extract_sentiment",
                "document-processor.extract_relationships"
            ], min_calls=2, max_calls=8),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=3)
        ]
    )
    
    # 使用记忆系统存储提取的内容
    yield ExecuteStep(
        name="store_extracted_content",
        description="将提取的内容存储到记忆系统",
        actions=[
            ActionDefinition(names=[
                "memory.change_directory",
                "memory.write_file"
            ], min_calls=1, max_calls=5)
        ]
    )
    
    # 使用 MCP analytics-tools 服务分析提取的内容
    yield ExecuteStep(
        name="analyze_extracted_content",
        description="分析提取的内容",
        actions=[
            ActionDefinition(names=[
                "analytics-tools.cluster_data",
                "analytics-tools.find_patterns",
                "analytics-tools.generate_statistics"
            ], min_calls=1, max_calls=5),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=3)
        ]
    )
    
    # 生成初步分析报告
    yield GenerateStep(
        name="generate_preliminary_report",
        description="生成初步分析报告",
        content_description="基于提取的内容和分析结果，生成包含主要发现和初步洞见的报告。"
    )
    
    # 获取用户对初步报告的反馈
    report_feedback: UserMessage = yield UserInputStep(
        name="get_report_feedback",
        description="获取用户对初步报告的反馈"
    )
    
    # 判断是否需要深入分析
    needs_deeper_analysis = yield ConditionStep(
        name="check_deeper_analysis",
        description="判断是否需要深入分析",
        condition_description="基于用户反馈，是否需要对特定领域进行更深入的分析？"
    )
    
    if needs_deeper_analysis:
        # 执行深入分析
        yield ExecuteStep(
            name="perform_deep_analysis",
            description="执行深入分析",
            actions=[
                ActionDefinition(names=["analytics-tools.deep_analysis"], min_calls=1, max_calls=3),
                ActionDefinition(names=["document-processor.compare_documents"], min_calls=0, max_calls=2),
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
            ]
        )
    
    # 生成最终报告
    yield ExecuteStep(
        name="prepare_final_report",
        description="准备最终报告数据",
        actions=[
            ActionDefinition(names=["workflow.get_register_value"], min_calls=1, max_calls=5),
            ActionDefinition(names=["analytics-tools.generate_visualizations"], min_calls=0, max_calls=3)
        ]
    )
    
    yield GenerateStep(
        name="generate_final_report",
        description="生成最终综合报告",
        content_description="生成全面的分析报告，包括关键发现、模式、关系、统计数据和针对性建议。"
    )
    
    # 保存报告到记忆系统
    yield ExecuteStep(
        name="save_report_to_memory",
        description="将最终报告保存到记忆系统",
        actions=[
            ActionDefinition(names=[
                "memory.change_directory",
                "memory.write_file"
            ], min_calls=1, max_calls=2)
        ]
    )
    
    # 获取用户最终评价
    final_feedback: UserMessage = yield UserInputStep(
        name="get_final_feedback",
        description="获取用户对最终报告的评价和后续需求"
    )
    
    # 结束处理流程
    yield HaltStep(name="complete_document_processing", description="完成文档处理并总结")
```

#### 示例 3: 交互式任务编排与执行助手

这个工作流展示了如何利用记忆系统实现交互式任务编排，智能体与用户协作制定和执行计划。

```python
# workflows/interactive_tasking_workflow.py
from typing import Dict, Any, AsyncGenerator, List
from libraries.workflow_models import (
    WorkflowStep, ExecuteStep, ConditionStep, 
    GenerateStep, HaltStep, UserInputStep, ActionDefinition
)
from message import UserMessage
from log import logger

WORKFLOW_DESCRIPTION = "交互式任务编排助手：规划、审核、执行复杂任务流程"
INITIAL_REGISTERS: Dict[str, Any] = {
    "task_plan": None,
    "execution_status": {
        "current_task": None,
        "completed_tasks": [],
        "pending_tasks": [],
        "failed_tasks": []
    },
    "environment_context": {},
    "execution_history": []
}

async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    交互式任务编排工作流：
    1. 收集任务需求并生成执行计划
    2. 用户审核和调整计划
    3. 逐步执行任务并实时反馈
    4. 适应性处理错误和变更
    """
    # 获取用户的任务需求
    user_message: UserMessage = yield UserInputStep(
        name="get_task_requirements",
        description="询问用户希望完成的任务和具体需求"
    )
    
    # 收集环境上下文
    yield ExecuteStep(
        name="gather_environment_context",
        description="收集任务执行环境的上下文信息",
        actions=[
            ActionDefinition(names=["system-tools.get_environment"], min_calls=1, max_calls=1),
            ActionDefinition(names=["system-tools.check_prerequisites"], min_calls=1, max_calls=1),
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
        ]
    )
    
    # 从记忆中检索相关经验
    yield ExecuteStep(
        name="retrieve_relevant_experiences",
        description="从记忆中检索相关任务经验",
        actions=[
            ActionDefinition(names=[
                "memory.list_directory",
                "memory.change_directory",
                "memory.read_file",
                "memory.search_files"
            ], min_calls=1, max_calls=5)
        ]
    )
    
    # 生成初步任务计划
    yield GenerateStep(
        name="generate_initial_plan",
        description="生成初步任务执行计划",
        content_description="基于用户需求和环境上下文，生成详细的任务执行计划，包括步骤顺序、依赖关系、预计耗时和可能的风险点。"
    )
    
    # 保存初步计划到记忆
    yield ExecuteStep(
        name="save_initial_plan",
        description="保存初步计划到记忆系统",
        actions=[
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1),
            ActionDefinition(names=[
                "memory.change_directory",
                "memory.write_file"
            ], min_calls=1, max_calls=2)
        ]
    )
    
    # 用户审核计划
    plan_feedback: UserMessage = yield UserInputStep(
        name="get_plan_feedback",
        description="获取用户对初步计划的反馈和修改建议"
    )
    
    # 检查是否需要修改计划
    needs_plan_revision = yield ConditionStep(
        name="check_plan_revision",
        description="检查是否需要修改计划",
        condition_description="用户是否提出了具体的计划修改建议？"
    )
    
    # 如果需要修改计划
    if needs_plan_revision:
        # 分析用户反馈
        yield ExecuteStep(
            name="analyze_plan_feedback",
            description="分析用户的计划修改反馈",
            actions=[
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
            ]
        )
        
        # 修改计划
        yield GenerateStep(
            name="revise_task_plan",
            description="根据用户反馈修改任务计划",
            content_description="根据用户的具体反馈，调整任务步骤、优先级或执行方式，并明确指出变更内容。"
        )
        
        # 再次保存修改后的计划
        yield ExecuteStep(
            name="save_revised_plan",
            description="保存修改后的计划",
            actions=[
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1),
                ActionDefinition(names=["memory.write_file"], min_calls=1, max_calls=1)
            ]
        )
        
        # 再次获取用户确认
        final_plan_feedback: UserMessage = yield UserInputStep(
            name="get_final_plan_approval",
            description="获取用户对修改后计划的最终确认"
        )
    
    # 确认开始执行任务
    start_confirmation: UserMessage = yield UserInputStep(
        name="confirm_execution_start",
        description="确认用户是否准备好开始执行任务"
    )
    
    # 检查用户是否同意开始执行
    ready_to_start = yield ConditionStep(
        name="check_execution_readiness",
        description="检查用户是否准备好开始执行",
        condition_description="用户是否明确表示准备开始执行任务计划？"
    )
    
    if not ready_to_start:
        yield GenerateStep(
            name="acknowledge_delay",
            description="确认延迟执行",
            content_description="确认将延迟执行任务，并询问用户何时希望开始或是否有其他考虑。"
        )
        yield HaltStep(name="postpone_execution", description="推迟任务执行")
        return
    
    # 初始化执行状态
    yield ExecuteStep(
        name="initialize_execution",
        description="初始化任务执行状态",
        actions=[
            ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2)
        ]
    )
    
    # 任务执行循环
    execution_complete = False
    while not execution_complete:
        # 获取下一个待执行任务
        yield ExecuteStep(
            name="get_next_task",
            description="获取下一个待执行任务",
            actions=[
                ActionDefinition(names=["workflow.get_register_value"], min_calls=1, max_calls=2),
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
            ]
        )
        
        # 检查是否所有任务已完成
        execution_complete = yield ConditionStep(
            name="check_execution_complete",
            description="检查是否所有任务已完成",
            condition_description="所有计划任务是否都已经完成？"
        )
        
        if execution_complete:
            break
        
        # 执行当前任务
        yield GenerateStep(
            name="announce_current_task",
            description="宣布当前执行的任务",
            content_description="告知用户即将执行的任务、预期结果和注意事项。"
        )
        
        # 使用MCP执行具体任务
        yield ExecuteStep(
            name="execute_current_task",
            description="执行当前任务",
            actions=[
                # 动态选择合适的工具执行当前任务
                ActionDefinition(names=[
                    "system-tools.execute_command",
                    "deployment-tools.apply_changes",
                    "analytics-tools.process_data",
                    "content-tools.generate_content"
                ], min_calls=1, max_calls=5)
            ]
        )
        
        # 验证任务执行结果
        task_successful = yield ConditionStep(
            name="verify_task_execution",
            description="验证任务是否成功执行",
            condition_description="当前任务是否成功完成并达到预期结果？"
        )
        
        # 更新执行状态
        yield ExecuteStep(
            name="update_execution_status",
            description="更新任务执行状态",
            actions=[
                ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=2),
                ActionDefinition(names=["memory.write_file"], min_calls=1, max_calls=1)
            ]
        )
        
        if not task_successful:
            # 处理任务失败
            yield GenerateStep(
                name="report_task_failure",
                description="报告任务失败",
                content_description="详细说明任务失败的原因、影响和可能的解决方案。"
            )
            
            # 询问用户如何处理失败
            failure_response: UserMessage = yield UserInputStep(
                name="get_failure_handling",
                description="询问用户如何处理任务失败"
            )
            
            # 分析用户对失败的处理指示
            handle_approach = yield ConditionStep(
                name="analyze_failure_handling",
                description="分析失败处理方式",
                condition_description="用户是希望重试失败的任务(True)还是跳过继续执行后续任务(False)？"
            )
            
            if handle_approach:  # 重试
                yield ExecuteStep(
                    name="prepare_retry",
                    description="准备重试任务",
                    actions=[
                        ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
                    ]
                )
                continue  # 回到循环开始，会再次执行同一任务
            else:  # 跳过
                yield ExecuteStep(
                    name="mark_task_skipped",
                    description="标记任务为已跳过",
                    actions=[
                        ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
                    ]
                )
                # 继续循环，会执行下一个任务
        
        # 报告任务进度
        yield GenerateStep(
            name="report_progress",
            description="报告任务执行进度",
            content_description="总结当前任务的执行结果，以及整体计划的完成进度。"
        )
        
        # 检查是否需要用户输入才能继续
        needs_user_input = yield ConditionStep(
            name="check_user_input_requirement",
            description="检查是否需要用户输入才能继续",
            condition_description="下一步任务是否需要用户提供额外信息或决策才能继续？"
        )
        
        if needs_user_input:
            # 获取用户输入
            user_input: UserMessage = yield UserInputStep(
                name="get_required_input",
                description="获取执行下一步所需的用户输入"
            )
            
            # 处理用户输入
            yield ExecuteStep(
                name="process_user_input",
                description="处理用户输入",
                actions=[
                    ActionDefinition(names=["workflow.set_register_value"], min_calls=1, max_calls=1)
                ]
            )
    
    # 所有任务执行完毕，生成执行总结
    yield ExecuteStep(
        name="compile_execution_summary",
        description="编译执行总结数据",
        actions=[
            ActionDefinition(names=["workflow.get_register_value"], min_calls=1, max_calls=3),
            ActionDefinition(names=[
                "memory.read_file",
                "memory.list_directory"
            ], min_calls=1, max_calls=3)
        ]
    )
    
    yield GenerateStep(
        name="generate_execution_summary",
        description="生成执行总结报告",
        content_description="提供任务执行的全面总结，包括成功项、失败项、耗时、资源使用情况和建议的后续行动。"
    )
    
    # 保存执行总结到记忆
    yield ExecuteStep(
        name="save_execution_summary",
        description="保存执行总结到记忆系统",
        actions=[
            ActionDefinition(names=[
                "memory.change_directory",
                "memory.write_file"
            ], min_calls=1, max_calls=2)
        ]
    )
    
    # 获取用户最终反馈
    final_feedback: UserMessage = yield UserInputStep(
        name="get_final_feedback",
        description="获取用户对整个执行过程的最终反馈"
    )
    
    # 完成工作流
    yield HaltStep(name="complete_task_execution", description="完成任务执行流程")
```

这些示例展示了 MeowAgent 在复杂场景下的能力：

1. **专业领域集成**：通过 MCP 服务器接入 Kubernetes 管理、文档处理、系统工具等专业能力
2. **复杂工作流控制**：使用循环、条件分支和状态管理处理复杂业务逻辑
3. **用户交互协作**：在关键决策点征求用户确认，实现人机协作
4. **记忆系统集成**：利用长期记忆存储和检索关键信息
5. **错误处理与恢复**：优雅处理执行异常并提供恢复机制
6. **多工具协同**：无缝组合多种工具和服务解决复杂问题

*   **循环与重试:** 使用`while`循环实现网页搜索重试机制。
*   **条件执行:** 使用条件语句控制工作流分支。
*   **状态跟踪:** 通过Python变量和日志管理流程状态。
*   **用户交互:** 使用`UserInputStep`获取用户输入并据此改变执行路径。
*   **组合步骤:** 组合不同类型的步骤实现完整业务逻辑。

### 9. 最佳实践

*   **清晰命名:** 为 Python 文件、`steps` 函数内的步骤（使用 `name` 参数）和变量使用清晰、描述性的名称。
*   **代码注释:** 添加注释解释复杂的逻辑或步骤的目的。
*   **模块化:** 对于非常复杂的工作流，可以将部分逻辑拆分到同一文件内的辅助异步函数中，然后在 `steps` 生成器中调用它们。
*   **日志记录:** 使用 `log.py` 提供的 `logger` 在关键节点记录信息，便于调试。
*   **错误处理:** 考虑在 `steps` 函数中使用 `try...except` 来捕获潜在的错误（例如，工具调用失败反映到下一步的状态判断），但这通常由 Agent 或 Library 层面处理，工作流层面更关注流程本身。
*   **生成器结束:** 确保生成器在逻辑完成后能正常结束（函数执行完毕或 `yield HaltStep` 后停止 `yield`）。
*   **用户交互设计:** 合理使用 `UserInputStep` 获取用户输入，使工作流能够响应用户的实时反馈。
*   **减少 wait_user=True 的使用:** 优先使用 `UserInputStep` 而非 `GenerateStep` 的 `wait_user` 参数来等待用户输入，前者提供更好的类型安全和清晰的语义。

### 10. MCP工具集成

MeowAgent借助[模型上下文协议（MCP）](https://modelcontextprotocol.io/introduction)实现了与外部工具的无缝连接。MCP是一个开放协议，它标准化了应用程序如何为LLM提供上下文的方式。类似于USB-C为设备提供标准连接方式，MCP为AI模型提供了连接各种数据源和工具的标准方法。

#### 配置MCP服务器

在项目根目录创建或编辑`mcp.json`文件以配置MCP服务器：

配置成功后，MeowAgent会自动连接这些服务器并将其工具注册到系统中。

#### 在工作流中使用MCP工具

可以在`ExecuteStep`中通过两种方式引用MCP工具：

1. **细粒度工具指定** - 直接指定服务器和工具名称：

```python
yield ExecuteStep(
    name="search_web",
    description="搜索网络获取最新信息",
    actions=[
        ActionDefinition(names=["web-search.search"], min_calls=1, max_calls=1)
    ]
)
```

2. **库级别指定** - 引用整个服务器提供的所有工具：

```python
yield ExecuteStep(
    name="code_operations",
    description="执行代码相关操作",
    actions=[
        ActionDefinition(names=["code-context"], min_calls=1, max_calls=5)
    ]
)
```

#### MCP工具的优势

* **标准化集成** - 遵循统一协议，简化工具接入
* **安全性** - 数据保持在你的基础设施内，增强隐私保护  
* **灵活切换** - 可以在不同LLM提供商和供应商之间切换
* **丰富生态** - 可使用不断增长的预构建集成列表

通过MCP工具集成，工作流可以轻松调用外部能力如代码分析、网络搜索、数据库查询等，大大扩展了LLM的操作空间。

### 11. 结论

Python工作流结合了Python编程语言的灵活性与LLM的智能，为构建复杂任务流程提供了有效工具。通过异步生成器机制，开发者可以编写处理动态场景的工作流，并充分利用Python的控制结构和生态系统。新增的 `UserInputStep` 进一步增强了工作流与用户的交互能力，使得工作流可以更加自然地接收和处理用户输入。对于需要复杂逻辑控制和用户交互的场景，Python工作流是YAML声明式工作流的有力补充。

---
