"""
该模块提供了强大且灵活的工作流程管理功能，旨在指导大型语言模型 (LLM) 智能体执行复杂任务。
其核心是 `WorkflowLibrary` 类，它充当了工作流程定义、状态管理和执行逻辑的中心枢纽。

主要功能和逻辑包括：

1.  **工作流程定义与加载**:
    *   支持两种类型的工作流程定义：
        *   **YAML 工作流**: 通过 YAML 文件定义结构化的、基于步骤的流程。每个步骤包含明确的操作类型 (如 NOP, EXECUTE, CONDITION, JUMP, GENERATE, HALT, USER_INPUT)。
        *   **Python 工作流**: 通过 Python 异步生成器函数定义动态的、程序化的流程。生成器 `yield` 出 `WorkflowStep` 对象来控制流程。
    *   自动从指定的 "workflows" 文件夹加载所有 YAML 和 Python 工作流定义。
    *   使用 `WorkflowSource` 和 `PythonWorkflowSource` 等模型来封装加载的工作流元数据和源。

2.  **工作流程状态管理 (`WorkflowState`)**:
    *   维护当前活动工作流程的状态，包括：
        *   当前工作流名称 (`workflow_name`) 和来源类型 (`source_type`: 'yaml' 或 'python')。
        *   当前步骤索引 (`step_index`，主要用于 YAML) 或当前 `yield` 的步骤对象 (用于 Python)。
        *   工作流寄存器 (`registers`)，用于在步骤间传递数据。
        *   标记位，如 `is_waiting_for_user`，指示工作流是否在等待用户输入。
    *   为 YAML 和 Python 工作流提供不同的状态推进逻辑 (如 `advance_step` 用于 YAML, `advance_python_step_next`/`advance_python_step_send` 用于 Python)。

3.  **工作流程执行与控制**:
    *   提供一系列注册工具 (`@register_tool`)，供 LLM 调用以与工作流交互：
        *   `list_available_workflows()`: 列出所有已加载的工作流。
        *   `get_workflow_steps()`: 获取 YAML 工作流的步骤详情 (Python 工作流不直接暴露静态步骤列表)。
        *   `set_current_workflow()`: 设置或切换当前活动的工作流。
        *   `get_current_workflow()`: 获取当前工作流的状态和当前步骤信息。
        *   `next_step()`: 手动前进到工作流的下一步 (用于 NOP, 无条件 JUMP, 或满足条件的 EXECUTE 步骤)。
        *   `condition_branch()`: 根据条件判断结果 (True/False) 进行分支跳转。
        *   `set_register_value()` / `get_register_value()`: 读写工作流寄存器。
    *   与 `WorkflowExecutor` (在 `self.executor` 中实例化) 紧密协作，处理实际的步骤执行逻辑，包括：
        *   `pre_process()`: 在 LLM 处理用户消息前，根据当前工作流步骤执行预处理逻辑 (例如，如果 Python 工作流在 `USER_INPUT` 后自动前进)。
        *   `post_process()`: 在 LLM 生成回复后，根据工作流步骤执行后处理逻辑 (例如，自动前进到 GENERATE 步骤的下一步，或处理 HALT 步骤)。

4.  **动态提示词工程 (`WorkflowPromptBuilder`)**:
    *   使用 `WorkflowPromptBuilder` (在 `self.prompt_builder` 中实例化) 动态构建和更新提供给 LLM 的系统提示词。
    *   提示词包含核心规则、操作类型说明、当前工作流状态 (包括当前步骤的详细信息、寄存器值等)，以及从工作流定义中加载的 few-shot 示例。
    *   根据活动工作流的类型 (YAML/Python) 和当前步骤，定制提示内容。

5.  **与 Agent 框架集成**:
    *   作为 Agent 的一个库被加载和管理。
    *   `set_agent()`: 允许 Agent 实例注入自身引用，以便库可以访问 Agent 的其他部分 (如其他库、输出连接)。
    *   `get_allowed_tools()`: 根据当前工作流步骤的操作类型 (特别是 `EXECUTE` 步骤) 和状态 (如 `is_waiting_for_user`)，动态确定并限制 LLM 当前可以调用的工具集。
    *   `get_few_shot_examples()`: 从当前工作流定义中提取 few-shot 示例，供 Agent 注入到 LLM 的上下文中。
    *   `initialize()`: 执行异步初始化任务，例如设置在配置中指定的默认 Python 工作流。
    *   `_send_workflow_status_update_if_possible()`: 通过 Agent 的输出连接发送工作流状态更新，用于前端 UI 展示或其他监听器。
    *   `get_library_details()`: 提供一个全面的工作流运行时快照，主要供前端使用。

6.  **错误处理与日志**:
    *   内置了对工作流加载、状态转换和执行过程中的各种潜在错误的捕获和记录。
    *   使用 `logger` 模块进行详细的日志输出。

该模块通过上述机制，为智能体提供了一个强大且结构化的任务执行框架，
能够引导 LLM 精确地遵循预定义或动态生成的复杂流程，从而提高任务执行的可靠性、
一致性和可控性。
"""

import os
import yaml
import json
import traceback
from typing import Dict, List, Any, Optional, Annotated, Union
# 导入 AsyncGenerator 用于 Python 工作流类型提示
from typing import AsyncGenerator
from libraries.library import Library, register_tool
from log import logger  # 导入logger
from message import AssistantMessage, SystemMessage, UserMessage, Message, ToolCallFunction, ToolCall, ToolMessage
from libraries.workflow_loader import load_workflows_from_folder
from libraries.workflow_preprocess import preprocess_workflow, auto_fill_next_steps
from libraries.workflow_examples import generate_text_examples_from_structured, generate_history_examples_from_structured
from libraries.workflow_prompt_builder import WorkflowPromptBuilder
from libraries.workflow_executor import WorkflowExecutor
from libraries.workflow_models import (
    WorkflowDefinition, WorkflowStep, ActionDefinition,
    ExecuteStep, ConditionStep, JumpStep, GenerateStep, NopStep, HaltStep, WorkflowOperation,
    # 导入 WorkflowSource 和 PythonWorkflowSource
    WorkflowSource, PythonWorkflowSource, UserInputStep, SwitchStep
)
from libraries.workflow_state import WorkflowState
import config as config_module

# 导入 Agent 类型用于类型提示，但避免循环导入
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from agent import Agent
# 导入 dumps 用于序列化时间戳
from utils import dumps
import asyncio # 确保 asyncio 已导入

class WorkflowLibrary(Library):
    """
    工作流程库，提供预定义的结构化任务流程 (YAML 或 Python)。
    LLM可以使用此库查询可用流程及其步骤，以指导其执行复杂任务。
    维护当前工作流状态，为智能体提供任务流程指导。
    支持图灵完备的工作流执行模式，包含步骤跟踪和跳转逻辑。
    """
    # 将基础提示词的核心部分定义为类变量或在 __init__ 中定义为不可变部分
    CORE_BASE_PROMPT = r"""**工作流程库**

你当前正在使用工作流程库来指导任务执行。系统已自动加载所有可用工作流。

**可用工具:**
- `workflow.list_available_workflows()`: 查看所有已定义的工作流程及其描述。
- `workflow.get_workflow_steps(workflow_name: str)`: 获取指定工作流程的详细步骤 (仅限YAML工作流)。
- `workflow.set_current_workflow(workflow_name: str)`: 设置当前要执行的工作流程。
- `workflow.get_current_workflow()`: 获取当前正在执行的工作流程信息和状态。
- `workflow.next_step()`: 前进到下一步（用于 EXECUTE 步骤动作完成后、NOP 或无条件 JUMP 操作）。
- `workflow.condition_branch(condition_result: bool)`: 根据条件判断结果进行分支跳转（用于 CONDITION 操作和带条件的 JUMP）。

**操作类型说明 (Operations):**
- **NOP:** 无操作，仅用于流程标记或跳转，通过 `next_step()` 前进。
- **EXECUTE:** 执行指定的工具或动作。你需要调用列出的工具。当所有 `min_calls` 满足后，可以通过调用 `next_step()` 前进到下一步。
- **CONDITION:** 条件判断。评估指定的条件，然后调用 `condition_branch(result)` (传入 True/False) 来跳转。
- **JUMP:** 跳转。无条件跳转通过 `next_step()` 执行；带条件的跳转类似于 CONDITION，需要调用 `condition_branch(result)`。
- **GENERATE:** 生成文本回复。直接生成文本，不调用任何工具。如果设置了 `wait_user=true`，则在生成后等待用户输入；否则自动前进。
- **HALT:** 终止工作流执行。生成最终的结论或总结性回复。

**核心规则 (Rules):**
- **必须** 始终有一个激活的工作流程。如果当前没有，必须先设置一个。
- **你必须像CPU执行指令一样，严格按照当前步骤的指示执行。** 不得自行决定执行流程或跳过步骤。
- **必须** 根据当前步骤的操作类型调用正确的工具 (`next_step`, `condition_branch`, 或 EXECUTE 指定的工具)。
- **EXECUTE 步骤:** 必须注意每个动作的 `min_calls` 和 `max_calls` 限制。只有满足所有 `min_calls` 后才能调用 `next_step()`。

**核心原则 (Principles):**
- **信息整合:** 在每次回复前，参考当前步骤要求，结合记忆、文档或其他工具的输出来生成回复或决策。
- **选择性记忆:** （如果使用记忆库）只记忆真正重要和有价值的信息。
- **自然融合:** 在回复中自然地融入上下文信息，避免机械重复。
- **精确执行:** 严格按照工作流中的指令和跳转逻辑执行。
- **状态追踪:** 理解并利用工作流寄存器 (`registers`) 来维护和传递状态。
"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化工作流程库并从文件夹加载工作流定义
        
        参数:
            config: 配置字典，可选包含 'agent' 键
        """
        # 更新类型提示以反映 WorkflowSource
        self.workflows: Dict[str, WorkflowSource] = {}  # 工作流名称 -> WorkflowSource对象

        # 引入 WorkflowState
        self.state = WorkflowState()

        # 新增 prompt_builder 实例
        self.prompt_builder = WorkflowPromptBuilder()
        
        # executor 将在 set_agent 调用时初始化
        self.executor: Optional[WorkflowExecutor] = None

        # 不在初始化时设置 agent，等待 set_agent 调用
        self.agent = None

        # 使用核心基础提示词初始化 base_prompt
        self.base_prompt = self.CORE_BASE_PROMPT

        # 空的few_shot_examples列表，将从工作流YAML文件中加载
        self.few_shot_examples = []

        # 生成初始提示词 - 后续会随当前工作流状态动态更新
        self.library_prompt = self.base_prompt # 初始设置

        # --- 用于延迟异步初始化的状态 ---
        self._needs_async_init: bool = False
        self._pending_default_workflow_name: Optional[str] = None

        # 调用父类初始化
        super().__init__(name="workflow", description="预定义工作流库 (支持YAML和Python)", prompt=self.library_prompt, config=config)

        # 自动加载workflows文件夹中的工作流定义
        self.workflows = load_workflows_from_folder("workflows")

        # 尝试识别并标记默认工作流
        if config and 'default_workflow_name' in config:
            default_workflow_name = config['default_workflow_name']
        else:
            default_workflow_name = config_module.DEFAULT_WORKFLOW_NAME
            logger.warning(f"初始化 WorkflowLibrary 时 config 中未找到 'default_workflow_name'，回退到全局配置: {default_workflow_name}")

        self._try_set_default_workflow(default_workflow_name)

        # 如果不需要异步初始化，则现在更新提示
        if not self._needs_async_init:
            self._update_library_prompt()
        else:
            # 如果需要异步初始化，先显示一个通用提示
            self.library_prompt = self.base_prompt + "\n\n**状态:** 正在等待异步初始化默认工作流..."
            self.update_prompt(self.library_prompt)

    def _update_library_prompt(self):
        """
        根据当前工作流状态更新库的提示词。
        会根据工作流类型传递额外信息给 prompt_builder。
        """
        python_file_path = None
        workflow_description = "(未知)"

        # 如果有活动工作流，尝试获取额外信息
        if self.state.workflow_name and self.state.source_type:
            workflow_source = self.workflows.get(self.state.workflow_name)
            if workflow_source:
                workflow_description = workflow_source.description
                if self.state.source_type == 'python':
                    # 推断 Python 文件路径
                    python_file_path = os.path.join("workflows", f"{self.state.workflow_name}.py")
                    # 可以在这里添加文件存在性检查，但 build 方法中也应处理
            else:
                 logger.warning(f"无法在 self.workflows 中找到当前工作流 '{self.state.workflow_name}' 的源信息。")

        # 使用 self.prompt_builder 构建提示词
        self.library_prompt = self.prompt_builder.build(
            state=self.state,
            base_prompt=self.base_prompt,
            library_name=self.name,
            # 传递额外信息给 build 方法
            workflow_description=workflow_description, 
            python_file_path=python_file_path
        )

        # 更新库的提示词
        self.update_prompt(self.library_prompt)

    # --- 修改内部设置工作流方法 (使其 async) ---
    async def _set_workflow_internal(self, workflow_name: str, workflow_source: WorkflowSource) -> bool:
        """
        内部方法，用于实际设置工作流状态 (异步执行)。
        根据 workflow_source 的类型调用不同的状态设置方法。
        返回 True 如果成功设置，False 如果失败。
        """
        try:
            if workflow_source.type == 'yaml':
                # --- 处理 YAML 工作流 (保持同步) ---
                # 确保 source 是 WorkflowDefinition
                if not isinstance(workflow_source.source, WorkflowDefinition):
                     logger.error(f"工作流 '{workflow_name}' 类型为 yaml 但源数据无效: {type(workflow_source.source)}")
                     return False
                workflow_def = workflow_source.source

                # 预处理和验证
                workflow_def = preprocess_workflow(workflow_def)
                auto_fill_next_steps(workflow_def)
                # 触发 Pydantic 验证
                _ = workflow_def.get_parsed_steps() 
                
                # 调用 state 的同步方法进行状态初始化
                self.state.set_yaml_workflow(workflow_name, workflow_def)
                logger.info(f"YAML 工作流 '{workflow_name}' 状态已初始化。")
                
                # 尝试加载工作流中的示例
                self._load_examples_from_workflow(workflow_source)

                # 检查初始步骤是否存在 (仅记录警告)
                initial_step = self.state.get_current_step()
                if not initial_step:
                    logger.warning(f"YAML 工作流 '{workflow_name}' 没有有效的初始步骤 (索引 {self.state.step_index})。")
                
                # 更新提示
                self._update_library_prompt()
                return True

            elif workflow_source.type == 'python':
                # --- 处理 Python 工作流 (异步) ---
                 # 确保 source 是 PythonWorkflowSource
                if not isinstance(workflow_source.source, PythonWorkflowSource):
                    logger.error(f"工作流 '{workflow_name}' 类型为 python 但源数据无效: {type(workflow_source.source)}")
                    return False
                python_source = workflow_source.source

                # 调用 state 的同步方法进行状态初始化 (创建生成器)
                self.state.set_python_workflow(workflow_name, python_source)
                logger.info(f"Python 工作流 '{workflow_name}' 状态已初始化，正在获取第一个步骤...")

                # --- 异步获取第一个步骤 --- 
                try:
                     await self.state.advance_python_step_next()
                     logger.info(f"Python 工作流 '{workflow_name}' 第一个步骤已成功获取。")
                except StopAsyncIteration:
                     # 如果生成器在第一次调用 next 时就结束了
                     logger.error(f"Python 工作流 '{workflow_name}' 在获取第一个步骤时就已结束 (StopAsyncIteration)。工作流为空或立即完成。")
                     # 状态已经在 advance_python_step_next 中部分清理，这里确保完全清理并更新提示
                     self.state.clear()
                     self._update_library_prompt()
                     return False # 认为设置失败，因为没有可执行步骤
                except Exception as first_step_error:
                     logger.error(f"Python 工作流 '{workflow_name}' 在获取第一个步骤时发生错误: {first_step_error}")
                     logger.debug(traceback.format_exc())
                     self.state.clear()
                     self._update_library_prompt()
                     return False # 设置失败
                
                # 尝试加载 Python 工作流的示例 (如果以后支持)
                # self._load_examples_from_workflow(workflow_source)

                # 检查初始步骤是否成功 yield
                initial_step = self.state.get_current_step()
                if not initial_step:
                    # 理论上如果 advance_python_step_next 成功，这里应该有步骤
                    logger.warning(f"Python 工作流 '{workflow_name}' 在获取第一个步骤后未能检索到当前步骤。")
                
                # 更新提示
                self._update_library_prompt()
                return True
            else:
                logger.error(f"未处理的工作流类型 '{workflow_source.type}' 在加载示例时")
                return False

        except Exception as e:
            logger.error(f"设置工作流 '{workflow_name}' 时发生意外错误: {e}")
            logger.debug(traceback.format_exc())
            self.state.clear() # 清理状态
            self._update_library_prompt() # 更新提示为无工作流状态
            return False

    async def _send_workflow_completion_notification(self, workflow_name: str):
        """
        发送工作流完成通知给Agent。
        
        参数:
            workflow_name: 完成的工作流名称
        """
        if self.agent and self.agent.output_connection:
            try:
                await self.agent.output_connection.send({
                    "type": "workflow_completed",
                    "content": {
                        "workflow_name": workflow_name,
                        "auto_exit": True,  # Python工作流完成后始终自动退出
                        "message": f"工作流 '{workflow_name}' 已成功完成"
                    },
                    "conversation_id": self.agent.conversation_id,
                    "timestamp": dumps({"time": asyncio.get_event_loop().time()})
                })
                logger.info(f"已发送工作流完成通知: {workflow_name} (auto_exit: True)")
            except Exception as e:
                logger.error(f"发送工作流完成通知失败: {e}")
                logger.debug(traceback.format_exc())

    async def _send_workflow_status_update_if_possible(self):
        """
        如果 agent 和 output_connection 可用，则发送 workflow_status_update。
        中文注释：如果 agent 和 output_connection 可用，则发送 workflow_status_update。
        """
        if self.agent and self.agent.output_connection:
            try:
                current_workflow_details = await self.get_library_details()
                # 确保 get_library_details 返回的 payload 是合适的，它本身可能包含 success 字段
                # payload 应该是 get_library_details 的直接结果
                await self.agent.output_connection.send({
                    "type": "workflow_status_update",
                    "payload": current_workflow_details, # get_library_details() 的结果作为 payload
                    "conversation_id": self.agent.conversation_id,
                    "timestamp": dumps({"time": asyncio.get_event_loop().time()})
                })
                logger.debug(f"Sent workflow_status_update for workflow: {self.state.workflow_name}")
            except Exception as e:
                logger.error(f"Error sending workflow_status_update for {self.state.workflow_name}: {e}")
                logger.debug(traceback.format_exc())

    @register_tool
    async def list_available_workflows(self) -> Dict[str, List[Dict[str, object]]]:
        """
        列出所有可用的工作流程及其描述和来源类型。
        """
        available = []
        for name, workflow_source in self.workflows.items(): # 使用 workflow_source 变量名
            available.append({
                "name": name,
                "description": workflow_source.description, # 使用 workflow_source 的属性
                "type": "workflow",
                "source_type": workflow_source.type # 添加来源类型
            })
        return {"workflows": available}

    @register_tool
    async def get_workflow_steps(
        self,
        workflow_name: Annotated[str, "要查询其步骤的工作流程的名称"]
    ) -> Dict[str, object]:
        """
        获取指定工作流程的详细步骤列表。
        对于 YAML 工作流，返回步骤定义；对于 Python 工作流，提示步骤列表不可用。
        """
        workflow_source = self.workflows.get(workflow_name)
        if not workflow_source:
            return {
                "success": False,
                "error": f"未找到名为 '{workflow_name}' 的工作流程。",
                "available_workflows": list(self.workflows.keys())
            }

        if workflow_source.type == 'yaml':
            # 确保 source 是 WorkflowDefinition
            if isinstance(workflow_source.source, WorkflowDefinition):
                workflow_def = workflow_source.source
                # 返回步骤和初始寄存器定义
                result = {
                    "success": True,
                    "workflow_name": workflow_name,
                    "source_type": "yaml",
                    "description": workflow_def.description,
                    # 返回原始步骤字典列表，而不是解析后的对象
                    "steps": workflow_def.steps,
                    "initial_registers": workflow_def.registers
                }
                return result
            else:
                 logger.error(f"工作流 '{workflow_name}' 类型为 yaml 但源数据无效: {type(workflow_source.source)}")
                 return {"success": False, "error": f"内部错误：工作流 '{workflow_name}' 类型为 yaml 但源数据无效"}
        elif workflow_source.type == 'python':
             # 对于 Python 工作流，不返回步骤列表
             return {
                 "success": False,
                 "workflow_name": workflow_name,
                 "source_type": "python",
                 "error": "Python 定义的工作流不支持直接查看静态步骤列表。请使用 get_current_workflow 查看当前状态。",
                 "description": workflow_source.description # 仍然可以返回描述
             }
        else:
            # 未知类型，理论上不应该发生
            return {"success": False, "error": f"工作流 '{workflow_name}' 存在未知类型 '{workflow_source.type}'"}

    def _load_examples_from_workflow(self, workflow_source: WorkflowSource) -> None:
        """
        从工作流来源中加载示例，包括提示词示例和历史示例。
        现在只附加示例，不重置 base_prompt 的核心部分。
        处理 YAML 和 Python 的差异。
        """
        # 重置示例部分
        self.few_shot_examples = []
        prompt_examples_appendix = "" # 用于存储要附加到基础提示词的示例文本

        # 确保 workflow_source 不为 None
        if workflow_source is None:
            logger.warning("尝试从 None 工作流来源加载示例")
            self.base_prompt = self.CORE_BASE_PROMPT
            return

        # --- 处理 YAML 工作流示例 ---
        if workflow_source.type == 'yaml' and isinstance(workflow_source.source, WorkflowDefinition):
            workflow_def = workflow_source.source
            workflow_name = self.state.workflow_name or "unnamed_workflow"

            # 首先检查是否有结构化示例
            prompt_examples_structured = workflow_def.prompt_examples_structured or []
            if prompt_examples_structured:
                # 使用结构化示例生成详细文本示例和历史示例
                try:
                    # 类型转换：将 List[StructuredExample] 显式转换为 List[Dict]
                    examples_as_dicts = [ex.model_dump() for ex in prompt_examples_structured] # 使用 model_dump()

                    generated_prompt_text = generate_text_examples_from_structured(
                        workflow_def, # 确认类型是 WorkflowDefinition
                        examples_as_dicts, # 传递 List[Dict]
                        workflow_name=workflow_name
                    )
                    generated_history = generate_history_examples_from_structured(
                        workflow_def, # 确认类型是 WorkflowDefinition
                        examples_as_dicts # 传递 List[Dict]
                    )
                except Exception as e:
                    logger.error(f"从结构化示例生成文本或历史时出错: {e}")
                    generated_prompt_text = ""
                    generated_history = []

                # 将生成的示例文本添加到 appendix
                if generated_prompt_text:
                    prompt_examples_appendix = "\n\n--- 工作流特定示例 ---\n" + generated_prompt_text
                    logger.info(f"已从 YAML 结构化定义生成提示词示例文本 ({len(generated_prompt_text)} 个字符)")

                # 使用生成的历史示例
                if generated_history:
                    self.few_shot_examples = generated_history
                    logger.info(f"已从 YAML 结构化定义生成 {len(generated_history)} 个历史示例")

            else:
                # 回退到传统方式：加载提示词示例文本 (用于附加)
                prompt_examples_text = workflow_def.prompt_examples_text or ""
                if prompt_examples_text:
                    # 将示例文本添加到 appendix
                    prompt_examples_appendix = "\n\n--- 工作流特定示例 ---\n" + prompt_examples_text
                    logger.info(f"已从 YAML 加载提示词示例文本 ({len(prompt_examples_text)} 个字符)")

                # 加载历史示例 (用于few_shot_examples)
                history_examples = workflow_def.history_examples or []
                if history_examples:
                    self.few_shot_examples = history_examples
                    logger.info(f"已从 YAML 加载 {len(history_examples)} 个历史示例")
                else:
                    logger.info("YAML 工作流中没有找到历史示例")

        # --- 处理 Python 工作流示例 (当前不支持) ---
        elif workflow_source.type == 'python':
            logger.info("Python 工作流当前不支持加载示例。")
            # 可以考虑未来从 Python 模块加载示例的逻辑
            pass

        else:
            logger.warning(f"未处理的工作流类型 '{workflow_source.type}' 在加载示例时")

        # 将核心提示词和示例部分组合起来
        self.base_prompt = self.CORE_BASE_PROMPT + prompt_examples_appendix

    def _try_set_default_workflow(self, default_workflow_name: str):
        """
        尝试设置默认的工作流。
        对于 YAML 工作流，会同步设置。
        对于 Python 工作流，会标记需要异步初始化，并存储名称。
        """
        workflow_source = self.workflows.get(default_workflow_name)
        if workflow_source:
            if workflow_source.type == 'python':
                # 标记 Python 工作流需要异步初始化
                logger.info(f"检测到默认工作流 '{default_workflow_name}' 是 Python 类型。将推迟到 async_initialize() 中设置。")
                self._needs_async_init = True
                self._pending_default_workflow_name = default_workflow_name
            elif workflow_source.type == 'yaml':
                # 同步设置 YAML 工作流
                logger.info(f"尝试同步设置默认 YAML 工作流: {default_workflow_name}")
                if self._set_yaml_workflow_internal_sync(default_workflow_name, workflow_source):
                    logger.info(f"已成功同步设置默认 YAML 工作流: {default_workflow_name}")
                else:
                    logger.error(f"同步设置默认 YAML 工作流 '{default_workflow_name}' 失败！请检查工作流文件和配置。")
                    self.state.clear()
            else:
                logger.error(f"未知的默认工作流类型: {workflow_source.type} for '{default_workflow_name}'")
        else:
            logger.error(f"未找到配置的默认工作流: '{default_workflow_name}'！请确保该工作流存在且名称正确。")
            self.state.clear()

    # --- 新增：用于同步设置 YAML 工作流的内部方法 --- 
    def _set_yaml_workflow_internal_sync(self, workflow_name: str, workflow_source: WorkflowSource) -> bool:
        """ 内部方法，用于同步设置 YAML 工作流状态 (主要用于 __init__) """
        if workflow_source.type != 'yaml':
             logger.error("_set_yaml_workflow_internal_sync 只能用于 YAML 类型")
             return False
             
        try:
            if not isinstance(workflow_source.source, WorkflowDefinition):
                 logger.error(f"YAML 工作流 '{workflow_name}' 源数据无效: {type(workflow_source.source)}")
                 return False
            workflow_def = workflow_source.source

            # 预处理和验证
            workflow_def = preprocess_workflow(workflow_def)
            auto_fill_next_steps(workflow_def)
            _ = workflow_def.get_parsed_steps() 
            
            # 调用 state 的同步方法
            self.state.set_yaml_workflow(workflow_name, workflow_def)
            logger.info(f"(Sync) YAML 工作流 '{workflow_name}' 状态已初始化。")
            
            # 加载示例
            self._load_examples_from_workflow(workflow_source)

            # 检查初始步骤
            initial_step = self.state.get_current_step()
            if not initial_step:
                logger.warning(f"(Sync) YAML 工作流 '{workflow_name}' 没有有效的初始步骤。")
            
            # 更新提示
            self._update_library_prompt()
            return True
            
        except Exception as e:
            logger.error(f"同步设置 YAML 工作流 '{workflow_name}' 时发生错误: {e}")
            logger.debug(traceback.format_exc())
            self.state.clear()
            self._update_library_prompt()
            return False

    @register_tool
    async def set_current_workflow(
        self,
        workflow_name: Annotated[str, "要设置为当前工作流的工作流名称"]
    ) -> Dict[str, object]:
        """
        设置当前要执行的工作流程 (YAML 或 Python)。
        将初始化执行环境并准备步骤执行。
        """
        # --- 新增：如果正在等待异步初始化，则取消 ---
        if self._needs_async_init:
            logger.warning(f"用户通过 set_current_workflow 设置新工作流，取消待处理的默认工作流 '{self._pending_default_workflow_name}' 初始化。")
            self._needs_async_init = False
            self._pending_default_workflow_name = None
        # ---

        workflow_source = self.workflows.get(workflow_name) # 获取 WorkflowSource
        if not workflow_source:
            return {
                "success": False,
                "error": f"未找到名为 '{workflow_name}' 的工作流程。",
                "available_workflows": list(self.workflows.keys())
            }

        # 使用内部方法设置工作流 (await 因为方法是 async)
        success = await self._set_workflow_internal(workflow_name, workflow_source)

        if not success:
             # _set_workflow_internal 内部已记录错误并清理状态
             return {
                 "success": False,
                 "error": f"设置工作流 '{workflow_name}' (类型: {workflow_source.type}) 失败。请检查日志。"
             }

        # _set_workflow_internal 内部已调用 _update_library_prompt()

        # 返回结果
        result = {
            "success": True,
            "message": f"已成功设置当前工作流为 '{workflow_name}' (类型: {workflow_source.type})",
            # 对于 Python 工作流，workflow_source.source 是 PythonWorkflowSource，不可直接序列化
            # 仅在 YAML 时返回详细定义
            "workflow_details": workflow_source.source.model_dump() if workflow_source.type == 'yaml' and isinstance(workflow_source.source, WorkflowDefinition) else {
                 "description": workflow_source.description,
                 "initial_registers": workflow_source.initial_registers
            }
        }

        # 获取初始步骤以返回给用户 (从 state 获取，已适配 Python)
        initial_step = self.state.get_current_step()
        if initial_step:
            # 使用 Pydantic 模型的 dict() 方法
            result["current_step"] = initial_step.model_dump() # 使用 model_dump()
        else:
             # 如果是 Python 工作流且第一步获取失败，这里会是 None
             result["current_step"] = None
             result["warning"] = "工作流已设置，但未能获取第一个有效步骤。"

        result["registers"] = self.state.registers
        # 添加库的当前完整提示词
        result["library_prompt"] = self.library_prompt

        # 成功设置工作流后，发送状态更新
        await self._send_workflow_status_update_if_possible()
        return result

    @register_tool
    async def get_current_workflow(self) -> Dict[str, object]:
        """
        获取当前正在执行的工作流程信息和当前执行状态。
        """
        if not self.state.workflow_name:
            return {
                "success": False,
                "error": "当前没有设置工作流程。请先使用set_current_workflow()设置一个工作流。",
                "available_workflows": list(self.workflows.keys())
            }

        # 构建返回结果，从 self.state 获取信息
        result = {
            "success": True,
            "workflow_name": self.state.workflow_name,
            "source_type": self.state.source_type, # 添加来源类型
        }

        # 根据类型添加描述和步骤信息
        if self.state.source_type == 'yaml' and self.state.workflow_data:
            result["description"] = self.state.workflow_data.description
            result["steps"] = self.state.workflow_data.steps # 返回原始步骤字典
            # 添加完整的工作流定义
            result["workflow_definition"] = self.state.workflow_data.model_dump()
        elif self.state.source_type == 'python':
            # Python 工作流不返回静态步骤列表
            # 可以从 state.workflow_data (如果存储了元数据) 或直接从 workflow_source 获取描述
            workflow_source = self.workflows.get(self.state.workflow_name)
            if workflow_source and isinstance(workflow_source.source, PythonWorkflowSource):
                 result["description"] = workflow_source.source.description
            else:
                 result["description"] = "(无法获取描述)"
            result["steps"] = "(Python 工作流不支持静态步骤列表)"
        else:
             result["description"] = "(未知类型或无数据)"
             result["steps"] = "(未知类型或无数据)"

        # 当前步骤信息 (统一从 state 获取)
        current_step = self.state.get_current_step()
        result["current_step_index"] = self.state.step_index # 对于 Python 为 None
        if current_step:
            # 统一使用Pydantic的 model_dump() 方法
            result["current_step"] = current_step.model_dump()
        else:
            result["current_step"] = None # 可能已结束或未初始化
        
        # 寄存器状态
        result["registers"] = self.state.registers
        # 添加库的当前完整提示词
        result["library_prompt"] = self.library_prompt
        # 添加基础提示词
        result["base_prompt"] = self.base_prompt

        return result

    async def get_library_details(self) -> Dict[str, Any]:
        """
        获取当前工作流的运行时详细信息，为前端提供全面的工作流运行时快照。
        返回当前激活的工作流名称、来源类型、当前步骤详情、寄存器值以及提示词相关信息。
        """
        if not self.state.workflow_name:
            return {
                "success": False,
                "error": "当前没有活动的工作流程。",
                "available_workflows": list(self.workflows.keys())
            }
            
        # 获取当前步骤
        current_step = self.state.get_current_step()
        current_step_data = current_step.model_dump() if current_step else None
        
        # 获取工作流描述
        workflow_description = "(未知)"
        workflow_source = self.workflows.get(self.state.workflow_name)
        if workflow_source:
            workflow_description = workflow_source.description
        
        # 构建详细信息
        details = {
            "success": True,
            "workflow_name": self.state.workflow_name,
            "source_type": self.state.source_type,
            "description": workflow_description,
            "current_step_index": self.state.step_index,
            "current_step": current_step_data,
            "registers": self.state.registers,
            "base_prompt": self.base_prompt,
            "library_prompt": self.library_prompt,
            # 添加执行状态统计
            "status": {
                "has_active_workflow": bool(self.state.workflow_name),
                "workflow_running": bool(current_step),
                "register_count": len(self.state.registers)
            }
        }
        
        # 根据工作流类型添加特定信息
        if self.state.source_type == 'yaml' and self.state.workflow_data:
            details["total_steps"] = len(self.state.workflow_data.steps)
            details["initial_registers"] = self.state.workflow_data.registers
            # 添加工作流定义摘要（不包含完整步骤，避免过大）
            details["workflow_summary"] = {
                "name": self.state.workflow_name,
                "description": self.state.workflow_data.description,
                "step_count": len(self.state.workflow_data.steps),
                "has_few_shot_examples": bool(
                    getattr(self.state.workflow_data, "prompt_examples_text", None) or 
                    getattr(self.state.workflow_data, "prompt_examples_structured", None) or
                    getattr(self.state.workflow_data, "history_examples", None)
                )
            }
        elif self.state.source_type == 'python':
            python_source = None
            if workflow_source and isinstance(workflow_source.source, PythonWorkflowSource):
                python_source = workflow_source.source
                
            if python_source:
                details["python_workflow_info"] = {
                    "description": python_source.description,
                    "initial_registers": python_source.initial_registers,
                    # "module_name": python_source.module_name,
                    "file_path": f"workflows/{self.state.workflow_name}.py"
                }
            else:
                details["python_workflow_info"] = {"error": "Python工作流源信息不可用"}
        
        return details

    @register_tool
    async def next_step(self) -> Dict[str, object]:
        """
        执行工作流的下一步操作。
        适用于需要手动前进的步骤，如 NOP、无条件 JUMP，或已满足条件的 EXECUTE。
        将根据工作流类型 (YAML/Python) 调用不同的状态推进逻辑。
        """
        if not self.state.workflow_name:
            return {
                "success": False,
                "error": "当前没有设置工作流或未初始化步骤。"
            }

        # 获取当前步骤 (从 state, 已适配 Python)
        current_step = self.state.get_current_step()
        if not current_step:
            return {
                "success": False,
                "error": f"找不到当前活动步骤。"
            }

        # --- 验证当前步骤是否允许调用 next_step ---
        # 需要 WorkflowExecutor 来检查 EXECUTE 步骤的 min_calls 状态
        # 这里简化检查：仅允许 NOP 和无条件 JUMP
        # EXECUTE 步骤的 next_step 调用逻辑在 executor 中判断更合适
        is_unconditional_jump = (
            isinstance(current_step, JumpStep) and
            current_step.next_condition is None
        )
        allowed = isinstance(current_step, NopStep) or is_unconditional_jump

        # TODO: 考虑 EXECUTE 步骤满足 min_calls 后的 next_step 权限
        # 这需要在 executor 验证后，或者在这里访问 executor 的状态判断逻辑
        # 暂时根据计划，假定 EXECUTE 满足条件后也可以调用 next_step
        if isinstance(current_step, ExecuteStep):
            # 简单的假设：如果不是强制要求调用工具，则允许 next_step
            # (这部分逻辑应该在 Executor 中更精确地处理)
            pass # 允许尝试调用，让 Executor 验证

        if not allowed and not isinstance(current_step, ExecuteStep):
            error_msg = (
                f"当前步骤 '{current_step.name or current_step.index}' ({current_step.operation}类型) "
                f"通常不应手动调用 next_step()。请检查是否应调用 condition_branch() 或等待自动前进。"
            )
            return {"success": False, "error": error_msg}

        # --- 根据来源类型推进状态 ---
        previous_step_name_or_index = current_step.name or current_step.index
        try:
            if self.state.source_type == 'yaml':
                if not isinstance(current_step, (NopStep, JumpStep, ExecuteStep)):
                     # 再次确认 YAML 下非预期调用 next_step
                     return {"success": False, "error": f"YAML 步骤 {previous_step_name_or_index} ({current_step.operation}) 不支持手动调用 next_step"}

                # 对于 NOP 和无条件 JUMP，获取 next 索引
                # 对于 EXECUTE，也使用 next 索引（假设已满足条件）
                next_index = getattr(current_step, 'next', None)

                if not isinstance(next_index, int):
                    error_msg = f"YAML 工作流错误：步骤 '{previous_step_name_or_index}' 缺少有效的 next 整数索引。"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 调用 state 的 advance_step 方法更新状态 (YAML专用)
                self.state.advance_step(next_index)

            elif self.state.source_type == 'python':
                 # 调用 state 的 advance_python_step_next 方法 (Python 专用)
                 # 假设 WorkflowState 中有此方法
                await self.state.advance_python_step_next()

            else:
                return {"success": False, "error": f"未知的 工作流类型 '{self.state.source_type}'"}

        except StopAsyncIteration as stop_ex:
            # Python 工作流结束
            # 尝试从异常参数中获取工作流名称
            workflow_name = getattr(stop_ex, 'args', [None])[0] if getattr(stop_ex, 'args', None) else self.state.workflow_name
            if not workflow_name:
                workflow_name = self.state.workflow_name or "unknown_workflow"
            
            logger.info(f"Python 工作流 '{workflow_name}' 已结束 (StopAsyncIteration)。")
            
            self.state.clear() # 清理状态
            self._update_library_prompt() # 更新为无工作流提示
            
            # 发送工作流完成消息给Agent
            await self._send_workflow_completion_notification(workflow_name)
            
            return {
                "success": True,
                "message": f"Python 工作流 '{workflow_name}' 已成功执行完毕。",
                "current_step": None,
                "current_step_index": None,
                "registers": {},
                "workflow_completed": True,
                "auto_exit": True
            }
        except Exception as e:
             logger.error(f"推进工作流 '{self.state.workflow_name}' 时发生错误: {e}")
             logger.debug(traceback.format_exc())
             # 保持当前状态还是清除？暂时保持并报错
             return {"success": False, "error": f"推进工作流时发生内部错误: {e}"}

        # --- 获取新状态并返回 ---
        new_current_step = self.state.get_current_step()
        if not new_current_step:
            # 如果下一步无效 (例如 YAML 索引错误或 Python yield None?)
            # advance_step/advance_python_step_next 内部应处理无效跳转
            logger.error(f"[严重错误] 工作流 '{self.state.workflow_name}' 前进后未能获取有效步骤！")
            return {
                "success": False,
                "error": f"工作流前进后状态错误：无法获取下一步骤。"
            }

        # 获取新步骤的友好名称
        new_step_display = new_current_step.name or new_current_step.index # Python 步骤可能只有 name

        # 更新提示词
        self._update_library_prompt()

        # 返回成功信息
        action_message = "完成并前进" # 通用消息

        # 状态推进后，发送状态更新
        await self._send_workflow_status_update_if_possible()
        return {
            "success": True,
            "message": f"已{action_message}从步骤 '{previous_step_name_or_index}' 到 '{new_step_display}'",
            "current_step": new_current_step.model_dump(), # 使用 model_dump()
            "current_step_index": self.state.step_index, # Python 为 None
            "registers": self.state.registers
        }

    @register_tool
    async def condition_branch(
        self,
        condition_result: Annotated[bool, "条件判断的结果，True或False"]
    ) -> Dict[str, object]:
        """
        条件分支判断，根据条件结果跳转到不同的步骤 (YAML) 或发送结果给生成器 (Python)。
        适用于 CONDITION 或带条件的 JUMP 操作步骤。
        """
        if not self.state.workflow_name:
            return {
                "success": False,
                "error": "当前没有执行工作流或未初始化步骤。"
            }

        # 获取当前步骤 (从 state, 已适配 Python)
        current_step = self.state.get_current_step()
        if not current_step:
            return {
                "success": False,
                "error": f"找不到当前活动步骤。"
            }
        
        # 获取当前步骤的友好名称
        previous_step_name_or_index = current_step.name or current_step.index

        # --- 验证当前步骤是否允许调用 condition_branch ---
        is_condition = isinstance(current_step, ConditionStep)
        is_conditional_jump = (
            isinstance(current_step, JumpStep) and
            current_step.next_condition is not None
        )

        if not is_condition and not is_conditional_jump:
            return {
                "success": False,
                "error": f"当前步骤 '{previous_step_name_or_index}' ({current_step.operation}) 不是条件判断类型，无法使用 condition_branch()。"
            }

        # --- 根据来源类型推进状态 ---
        try:
            if self.state.source_type == 'yaml':
                # 根据条件结果确定下一步索引
                if condition_result:
                    next_index = getattr(current_step, 'true_branch', None)
                else:
                    next_index = getattr(current_step, 'false_branch', None)

                if not isinstance(next_index, int):
                    branch = 'true_branch' if condition_result else 'false_branch'
                    error_msg = f"YAML 工作流错误：步骤 '{previous_step_name_or_index}' 的 {branch} 不是有效的整数索引。"
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

                # 调用 state 的 advance_step 方法更新状态 (YAML专用)
                self.state.advance_step(next_index)

            elif self.state.source_type == 'python':
                # 调用 state 的 advance_python_step_send 方法 (Python 专用)
                # 假设 WorkflowState 中有此方法
                await self.state.advance_python_step_send(condition_result)

            else:
                return {"success": False, "error": f"未知的 工作流类型 '{self.state.source_type}'"}

        except StopAsyncIteration as stop_ex:
            # Python 工作流结束
            # 尝试从异常参数中获取工作流名称
            workflow_name = getattr(stop_ex, 'args', [None])[0] if getattr(stop_ex, 'args', None) else self.state.workflow_name
            if not workflow_name:
                workflow_name = self.state.workflow_name or "unknown_workflow"
            
            logger.info(f"Python 工作流 '{workflow_name}' 在发送条件结果后已结束 (StopAsyncIteration)。")
            
            self.state.clear() # 清理状态
            self._update_library_prompt() # 更新为无工作流提示
            
            # 发送工作流完成消息给Agent
            await self._send_workflow_completion_notification(workflow_name)
            
            return {
                "success": True,
                "message": f"Python 工作流 '{workflow_name}' 在处理条件后已成功执行完毕。",
                "condition_result": condition_result,
                "current_step": None,
                "current_step_index": None,
                "registers": {},
                "workflow_completed": True,
                "auto_exit": True
            }
        except Exception as e:
             logger.error(f"处理条件分支 '{self.state.workflow_name}' 时发生错误: {e}")
             logger.debug(traceback.format_exc())
             # 保持当前状态还是清除？暂时保持并报错
             return {"success": False, "error": f"处理条件分支时发生内部错误: {e}"}

        # --- 获取新状态并返回 ---
        new_current_step = self.state.get_current_step()
        if not new_current_step:
            # 如果下一步无效
            logger.error(f"[严重错误] 工作流 '{self.state.workflow_name}' 处理条件分支后未能获取有效步骤！")
            return {
                "success": False,
                "error": f"工作流条件分支后状态错误：无法获取下一步骤。"
            }

        # 获取目标步骤的友好名称
        target_step_display = new_current_step.name or new_current_step.index

        # 更新提示词
        self._update_library_prompt()

        # 状态推进后，发送状态更新
        await self._send_workflow_status_update_if_possible()
        return {
            "success": True,
            "message": f"条件判断结果为 {'真' if condition_result else '假'}，已跳转到步骤 '{target_step_display}'",
            "condition_result": condition_result,
            "current_step_index": self.state.step_index, # 从 state 获取
            "current_step": new_current_step.model_dump(), # 使用 model_dump()
            "registers": self.state.registers # 从 state 获取
        }

    @register_tool
    async def set_register_value(
        self,
        register_name: Annotated[str, "要设置的寄存器名称"],
        value: Annotated[object, "要设置的寄存器值"]
    ) -> Dict[str, object]:
        """
        设置工作流寄存器的值。
        """
        # 检查工作流是否已设置 (不区分类型)
        if not self.state.workflow_name:
            return {
                "success": False,
                "error": "当前没有设置工作流。"
            }

        # 检查寄存器是否在初始定义中 (对 Python 来说是 initial_registers)
        initial_registers = {}
        if self.state.source_type == 'yaml' and self.state.workflow_data:
            initial_registers = self.state.workflow_data.registers
        elif self.state.source_type == 'python':
            # 从加载的 workflow_source 获取
            workflow_source = self.workflows.get(self.state.workflow_name)
            if workflow_source and isinstance(workflow_source.source, PythonWorkflowSource):
                initial_registers = workflow_source.source.initial_registers
            
        # 允许设置不在初始定义中的寄存器吗？目前 YAML 实现似乎允许
        # if register_name not in initial_registers:
        #    return {
        #        "success": False,
        #        "error": f"工作流初始定义中未包含寄存器 '{register_name}'。",
        #        "available_registers": list(initial_registers.keys())
        #    }

        # 设置寄存器值 (调用 state 的 update_register 方法)
        old_value = self.state.registers.get(register_name)
        self.state.update_register(register_name, value)

        # 更新提示词
        self._update_library_prompt()

        # 状态推进后，发送状态更新
        await self._send_workflow_status_update_if_possible()
        return {
            "success": True,
            "message": f"已将寄存器 '{register_name}' 的值从 {old_value} 设置为 {value}",
            "register_name": register_name,
            "old_value": old_value,
            "new_value": value,
            "registers": self.state.registers # 从 state 获取
        }

    @register_tool
    async def get_register_value(
        self,
        register_name: Annotated[str, "要获取的寄存器名称"]
    ) -> Dict[str, object]:
        """
        获取工作流寄存器的值。
        """
        # 检查寄存器是否存在 (在 state 中)
        if register_name not in self.state.registers:
            return {
                "success": False,
                "error": f"找不到寄存器 '{register_name}'。",
                "available_registers": list(self.state.registers.keys())
            }

        return {
            "success": True,
            "register_name": register_name,
            "value": self.state.registers.get(register_name) # 从 state 获取
        }

    @register_tool
    async def select_case_target(
        self,
        target_case_name: Annotated[str, "要选择的分支案例名称"]
    ) -> Dict[str, object]:
        """
        选择 SWITCH 步骤中的特定分支案例。
        LLM 在遇到 SwitchStep 时必须调用此工具来选择下一步要执行的分支。
        """
        # 验证当前是否处于 SWITCH 步骤
        current_step = self.state.get_current_step()
        if not current_step:
            return {
                "success": False,
                "error": "当前没有活动的工作流步骤。"
            }
        
        # 导入 SwitchStep 类
        from libraries.workflow_models import SwitchStep
        
        if not isinstance(current_step, SwitchStep):
            return {
                "success": False,
                "error": f"当前步骤不是 SWITCH 类型，无法选择分支。当前步骤类型: {current_step.operation}"
            }
        
        # 查找匹配的案例
        matched_case = None
        for case in current_step.cases:
            if case.name == target_case_name:
                matched_case = case
                break
        
        if not matched_case:
            available_cases = [case.name for case in current_step.cases]
            return {
                "success": False,
                "error": f"找不到名为 '{target_case_name}' 的分支案例。",
                "available_cases": available_cases
            }
        
        # 切换到新的工作流分支
        try:
            # 创建新的生成器实例
            new_generator = matched_case.next()
            
            # 更新 WorkflowState
            old_generator = self.state.generator_instance
            self.state.generator_instance = new_generator
            self.state.current_yielded_step = None
            self.state.is_waiting_for_user = False
            self.state.reset_current_step_action_counts()
            
            # 驱动新生成器获取第一个步骤
            await self.state.advance_python_step_next()
            
            # 更新提示词
            self._update_library_prompt()
            
            # 发送工作流状态更新
            await self._send_workflow_status_update_if_possible()
            
            logger.info(f"已成功切换到工作流分支 '{target_case_name}'")
            
            # 获取新分支的第一个步骤信息
            new_current_step = self.state.get_current_step()
            
            return {
                "success": True,
                "message": f"已成功切换到工作流分支 '{target_case_name}'",
                "selected_case": target_case_name,
                "case_description": matched_case.description,
                "new_current_step": new_current_step.model_dump() if new_current_step else None,
                "registers": self.state.registers
            }
            
        except StopAsyncIteration as stop_ex:
            # 新分支立即结束（空工作流或只有 halt）
            workflow_name = getattr(stop_ex, 'args', [None])[0] if getattr(stop_ex, 'args', None) else self.state.workflow_name
            if not workflow_name:
                workflow_name = self.state.workflow_name or "unknown_workflow"
            
            logger.info(f"选择的工作流分支 '{target_case_name}' 立即结束。")
            
            self.state.clear()
            self._update_library_prompt()
            
            # 发送工作流完成消息
            await self._send_workflow_completion_notification(workflow_name)
            
            return {
                "success": True,
                "message": f"选择的工作流分支 '{target_case_name}' 已立即完成。",
                "selected_case": target_case_name,
                "workflow_completed": True,
                "auto_exit": True
            }
            
        except Exception as e:
            logger.error(f"切换到工作流分支 '{target_case_name}' 时发生错误: {e}")
            logger.debug(traceback.format_exc())
            
            # 恢复原状态（如果可能）
            if 'old_generator' in locals():
                self.state.generator_instance = old_generator
            
            return {
                "success": False,
                "error": f"切换到工作流分支时发生内部错误: {e}"
            }

    # --- Agent 交互方法 (基本无需修改) ---
    def get_allowed_tools(self, agent: "Agent") -> Optional[List[str]]:
        """
        获取当前步骤允许使用的工具列表。
        封装了原 workflow_executor.get_allowed_tools_for_current_step 的逻辑。
        应该能处理来自 YAML 或 Python 的 WorkflowStep 对象。

        参数:
            agent: Agent对象，用于检查库是否存在以及获取其工具。
        
        返回:
            允许的工具名称列表 (格式: '库名.工具名')，或None表示不限制。
        """
        # 从 state 获取状态信息
        current_step = self.state.get_current_step()
        library_name = self.name # Workflow 库自身的名称

        if not current_step:
            # 没有当前步骤，不限制工具
            return None
            
        # 当处于等待用户输入状态时，不允许任何工具调用
        if self.state.is_waiting_for_user or isinstance(current_step, UserInputStep):
            logger.debug(f"当前处于等待用户输入状态 (is_waiting_for_user={self.state.is_waiting_for_user}, step={current_step.__class__.__name__})，返回空工具列表")
            return []

        # 基础控制工具
        allowed_tools = []

        if isinstance(current_step, ExecuteStep):
            # 遍历每个动作组
            for action in current_step.actions:
                # 遍历每个动作组内的所有 names
                for name in getattr(action, 'names', []):
                    if not name:
                        continue
                    # 检查是库名还是特定工具名
                    is_lib_ref = "." not in name and agent and name in agent.libraries
                    if is_lib_ref:
                        # 是库名，扩展该库下的所有工具
                        try:
                            library_tools = list(agent.libraries[name].tools.keys())
                            allowed_tools.extend(library_tools)
                            logger.debug(f"允许工具列表：添加库 '{name}' 下的工具: {library_tools}")
                        except Exception as e:
                            logger.error(f"获取库 {name} 工具时出错: {e}")
                    else:
                        # 是特定工具名，直接添加
                        allowed_tools.append(name)
                        logger.debug(f"允许工具列表：添加特定工具: {name}")
            # 检查是否可以调用 next_step (由 Executor 决定)
            # 这里总是允许，让 Executor 做实际检查
            allowed_tools.append(f"{library_name}.next_step")

        elif isinstance(current_step, ConditionStep):
            allowed_tools.append(f"{library_name}.condition_branch")
        
        elif isinstance(current_step, SwitchStep):
            allowed_tools.append(f"{library_name}.select_case_target")
        
        elif isinstance(current_step, JumpStep):
            if current_step.next_condition:
                allowed_tools.append(f"{library_name}.condition_branch")
            else:
                allowed_tools.append(f"{library_name}.next_step")
            
        elif isinstance(current_step, NopStep):
            allowed_tools.append(f"{library_name}.next_step")
            
        # GenerateStep 不应该调用任何工具，已由前面的空列表条件处理
        # HALT 步骤不应该调用任何工具，将默认返回空列表
        # UserInputStep 也不应该调用任何工具，已由前面的空列表条件处理
        
        # 去重后返回
        return list(dict.fromkeys(allowed_tools))

    # set_agent 方法保持不变
    def set_agent(self, agent):
        """
        设置Agent实例的引用，并初始化 WorkflowExecutor。
        如果 Executor 已存在，则更新其 agent 引用。

        参数:
            agent: Agent实例
        """
        self.agent = agent
        # 初始化或更新 executor
        if self.executor:
            self.executor.agent = agent # 更新现有 executor 的 agent 引用
            logger.info("WorkflowExecutor 已更新 Agent 引用。")
        else:
            self.executor = WorkflowExecutor(agent=self.agent)
            logger.info("WorkflowExecutor 已通过 set_agent 初始化。")

    # get_few_shot_examples 保持不变 (目前只从 YAML 加载)
    def get_few_shot_examples(self) -> List[List[Message]]:
        """
        能力接口：提供 Few-Shot 示例 (目前仅支持从YAML加载)

        返回：
            List[List[Message]]，每个子列表是一组完整的示例对话
        说明：
            任何库只要实现此方法，Agent会自动将其few-shot示例注入到系统提示中。
        """
        # 这里需要将现有的字典列表转换为Message对象列表
        structured_examples = []
        
        # 确保 self.few_shot_examples 是列表
        if not isinstance(self.few_shot_examples, list):
            logger.warning(f"few_shot_examples 不是列表: {type(self.few_shot_examples)}")
            return []
            
        for example_group in self.few_shot_examples:
            # 确保 example_group 是列表
            if not isinstance(example_group, list):
                 logger.warning(f"few_shot_examples 中的元素不是列表: {type(example_group)}")
                 continue
                 
            message_group = []
            for message_dict in example_group:
                 # 确保 message_dict 是字典
                if not isinstance(message_dict, dict):
                     logger.warning(f"历史示例中的消息不是字典: {type(message_dict)}")
                     continue
                     
                role = message_dict.get("role")
                content = message_dict.get("content", "")
                
                try:
                    if role == "user":
                        message_group.append(UserMessage(content=content))
                    elif role == "assistant":
                        tool_calls = []
                        raw_tool_calls = message_dict.get("tool_calls")
                        if isinstance(raw_tool_calls, list):
                            for tc_dict in raw_tool_calls:
                                # 确保 tc_dict 是字典
                                if not isinstance(tc_dict, dict):
                                    logger.warning(f"工具调用不是字典: {type(tc_dict)}")
                                    continue
                                # 检查函数结构
                                func_dict = tc_dict.get("function")
                                if isinstance(func_dict, dict) and "name" in func_dict and "arguments" in func_dict:
                                    func = ToolCallFunction(
                                        name=func_dict["name"],
                                        # arguments 应该是字符串
                                        arguments=str(func_dict["arguments"])
                                    )
                                    tool_call = ToolCall(
                                        id=str(tc_dict.get("id", "")), # 确保 id 是字符串
                                        function=func
                                    )
                                    tool_calls.append(tool_call)
                                else:
                                     logger.warning(f"工具调用函数结构无效: {func_dict}")
                        message_group.append(AssistantMessage(content=content, tool_calls=tool_calls if tool_calls else None))
                    elif role == "system":
                        message_group.append(SystemMessage(content=content))
                    elif role == "tool":
                        message_group.append(ToolMessage(
                            content=content,
                            # 确保 tool_call_id 是字符串
                            tool_call_id=str(message_dict.get("tool_call_id", ""))
                        ))
                    else:
                        logger.warning(f"未知的示例角色: {role}")
                except Exception as e:
                    logger.error(f"创建示例消息时出错: {e}, 字典: {message_dict}")
            
            if message_group:
                structured_examples.append(message_group)
        
        return structured_examples

    # pre_process 和 post_process 保持不变 (依赖 executor)
    async def pre_process(self, user_message: Optional[UserMessage], messages: List[Message]) -> None:
        """
        能力接口：委托给 WorkflowExecutor 处理 Pre-Process 逻辑。
        
        参数:
            user_message: 用户消息对象，可能为None（在user_ready消息中）
            messages: 消息对象列表
        """
        if not self.executor:
            logger.error("WorkflowExecutor 尚未初始化，无法执行 pre_process。")
            return
            
        # 直接调用 executor 的 pre_process 方法
        # executor 的 pre_process 需要能够处理 state (包含 source_type)
        await self.executor.pre_process(state=self.state, user_message=user_message, messages=messages)
        
        # --- 新增：检查Python工作流是否自动前进到非等待状态 ---
        # 如果用户消息让工作流前进到非等待状态，我们可能需要立即调用模型处理新步骤
        if (self.state.source_type == 'python' and 
            not self.state.is_waiting_for_user and 
            self.state.current_yielded_step and 
            not isinstance(self.state.current_yielded_step, UserInputStep)):
            logger.info("接收用户输入后，Python工作流已自动前进到非等待状态，准备处理新步骤。")
            # 这里不执行特殊操作，让agent正常处理用户消息后的LLM调用即可
        
        # pre_process 可能修改 state，完成后需要更新提示词
        self._update_library_prompt()

    async def post_process(self, assistant_message: AssistantMessage, messages: List[Message]) -> tuple[bool, List[Message]]:
        """
        能力接口：委托给 WorkflowExecutor 处理 Post-Process 逻辑。
        
        参数:
            assistant_message: 助手消息对象
            messages: 消息对象列表
            
        返回:
            (是否需要重新生成, 需要添加的消息列表)
        """
        if not self.executor:
            logger.error("WorkflowExecutor 尚未初始化，无法执行 post_process。")
            return False, []

        # 直接调用 executor 的 post_process 方法
        # executor 的 post_process 需要能够处理 state (包含 source_type)
        should_resend, additional_messages = await self.executor.post_process(
            state=self.state, 
            assistant_message=assistant_message, 
            messages=messages, 
            library_name=self.name
        )
        # post_process 可能修改 state，完成后需要更新提示词
        self._update_library_prompt()
        
        return should_resend, additional_messages

    async def initialize(self):
        """
        执行异步初始化任务，特别是设置默认的 Python 工作流。
        此方法应在库实例化后，在异步上下文中调用。
        """
        # 安全检查：确保 WorkflowExecutor 已经初始化
        if not self.executor:
            if self.agent:
                # 如果有 agent 但没有 executor，尝试初始化
                self.executor = WorkflowExecutor(agent=self.agent)
                logger.warning("WorkflowExecutor 在 initialize 时补充初始化")
            else:
                logger.error("WorkflowLibrary 在 initialize 时没有 Agent 引用，无法初始化 WorkflowExecutor")
                return
        
        if self._needs_async_init and self._pending_default_workflow_name:
            logger.info(f"开始异步初始化默认 Python 工作流: {self._pending_default_workflow_name}")
            workflow_source = self.workflows.get(self._pending_default_workflow_name)
            if workflow_source and workflow_source.type == 'python':
                success = await self._set_workflow_internal(self._pending_default_workflow_name, workflow_source)
                if success:
                    logger.info(f"已成功异步初始化默认 Python 工作流: {self._pending_default_workflow_name}")
                else:
                    logger.error(f"异步初始化默认 Python 工作流 {self._pending_default_workflow_name} 失败。")
                    # 保持无工作流状态
                    self.state.clear()
                    self._update_library_prompt() # 更新提示以反映失败/无工作流
            else:
                logger.error(f"无法找到待处理的默认 Python 工作流 '{self._pending_default_workflow_name}' 或类型不匹配。")
                # 保持无工作流状态
                self.state.clear()
                self._update_library_prompt()

            # 无论成功与否，都标记异步初始化已尝试完成
            self._needs_async_init = False
            self._pending_default_workflow_name = None
            # 更新提示词 (如果成功，会显示工作流状态；如果失败，会显示无工作流)
            # _set_workflow_internal 内部会调用 _update_library_prompt，失败时上面也调用了
            # 为保险起见，如果上面没有设置成功，这里也更新一次
            if not self.state.workflow_name:
                 self._update_library_prompt()
            # 默认工作流设置（或失败）后，发送状态更新
            await self._send_workflow_status_update_if_possible()

        elif self._needs_async_init:
             logger.warning("需要异步初始化，但待处理的工作流名称丢失。")
             self._needs_async_init = False # 重置标志
             self._update_library_prompt() # 更新为无工作流提示
        else:
            logger.debug("无需异步初始化或已完成。")
