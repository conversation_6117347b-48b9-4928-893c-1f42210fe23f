"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Play, Send, Plus, Paperclip, MessageSquare, Code } from "lucide-react"
import MessageList from "@/components/message-list"
import type { ChatMessageDisplay, FormattedApiMessage } from "../types/message"
import { PanelGroup, Panel, PanelResizeHandle } from "react-resizable-panels"
import { API_BASE_URL } from "@/lib/config"
import FormattedMessageItem from "./formatted-message-item"

interface FormattedConversationHistoryResponse {
    history: FormattedApiMessage[];
}

interface ChatAreaProps {
  messages: ChatMessageDisplay[]
  onMessagesChange: (messages: ChatMessageDisplay[]) => void
  onAddMessage: (message: Partial<ChatMessageDisplay>) => void
  onRun: () => void
  onDeleteMessage?: (messageId: string) => void
  onUpdateMessage?: (message: ChatMessageDisplay) => void
  isSyncing?: boolean
  currentConversationId: string | null
}

const SCROLL_THRESHOLD = 50; // px, 当用户滚动位置距离底部小于此值时，视为在底部

export default function ChatArea({
  messages,
  onMessagesChange,
  onAddMessage,
  onRun,
  onDeleteMessage,
  onUpdateMessage,
  isSyncing = false,
  currentConversationId,
}: ChatAreaProps) {
  const [inputValue, setInputValue] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Refs for scrollable areas
  const leftPanelScrollRef = useRef<HTMLDivElement>(null);
  const rightPanelScrollRef = useRef<HTMLDivElement>(null);

  // Formatted messages state
  const [formattedApiMessages, setFormattedApiMessages] = useState<FormattedApiMessage[]>([])
  const [isLoadingFormatted, setIsLoadingFormatted] = useState(false)
  const [errorFormatted, setErrorFormatted] = useState<string | null>(null)

  // 新增：左侧面板消息加载状态
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)

  // 新增：面板折叠状态
  const [isMessagePanelCollapsed, setIsMessagePanelCollapsed] = useState(false)
  const [isFormattedPanelCollapsed, setIsFormattedPanelCollapsed] = useState(false)

  useEffect(() => {
    // 滚动到最新消息 (左侧面板)
    const leftPanel = leftPanelScrollRef.current;
    if (leftPanel) {
      const isScrolledToBottom = leftPanel.scrollHeight - leftPanel.clientHeight <= leftPanel.scrollTop + SCROLL_THRESHOLD;
      if (isScrolledToBottom) {
        messagesEndRef.current?.scrollIntoView({ behavior: "auto" }); // Changed to auto for potentially less disruptive scroll
      }
    }
    
    // 监听消息变化，重置加载状态
    // 当消息更新时，关闭加载状态
    setIsLoadingMessages(false);
  }, [messages])

  // 当 currentConversationId 变化时，立即清空两侧消息列表
  useEffect(() => {
    // 清空左侧消息 (通常由父组件通过 props 更新 messages 实现，但为确保，这里可以再次强调)
    // onMessagesChange([]); // 假设 onMessagesChange([]) 会触发父组件清空 messages
    // 立即清空右侧格式化消息
    setFormattedApiMessages([]);
    // 如果有必要，也重置左侧的 isLoadingMessages 状态，但通常 messages 变化时已处理
    // setIsLoadingMessages(false); 
    // 重置右侧加载和错误状态
    setIsLoadingFormatted(false);
    setErrorFormatted(null);

  }, [currentConversationId]);

  // 当 currentConversationId 或 messages (左侧消息) 变化时，获取或刷新右侧格式化消息
  // 注意：此 useEffect 不应在开始时清空 formattedApiMessages，以避免刷新时的白屏
  useEffect(() => {
    const fetchFormattedMessages = async () => {
      if (!currentConversationId) {
        // 如果没有 currentConversationId，确保 formattedApiMessages 也是空的
        // (上面的 effect 应该已经处理了，但作为双重保障)
        if (formattedApiMessages.length > 0) setFormattedApiMessages([]); 
        return;
      }
      setIsLoadingFormatted(true);
      setErrorFormatted(null);
      // 注意：此处不再有 setFormattedApiMessages([])，防止刷新白屏

      const rightPanel = rightPanelScrollRef.current;
      let oldScrollTop = 0;
      let oldScrollHeight = 0;
      let wasScrolledToBottom = false;

      if (rightPanel) {
        oldScrollTop = rightPanel.scrollTop;
        oldScrollHeight = rightPanel.scrollHeight;
        wasScrolledToBottom = rightPanel.scrollHeight - rightPanel.clientHeight <= rightPanel.scrollTop + SCROLL_THRESHOLD;
      }

      try {
        const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages/formatted`)
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ detail: response.statusText }))
          throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to load formatted messages'}`)
        }
        const data: FormattedConversationHistoryResponse = await response.json()
        setFormattedApiMessages(data.history || [])

        requestAnimationFrame(() => {
          if (rightPanel) {
            if (wasScrolledToBottom) {
              rightPanel.scrollTop = rightPanel.scrollHeight;
            } else {
              // Try to maintain scroll position relative to the content that was visible
              const newScrollHeight = rightPanel.scrollHeight;
              rightPanel.scrollTop = Math.max(0, oldScrollTop + (newScrollHeight - oldScrollHeight));
            }
          }
        });

      } catch (error) {
        console.error("加载格式化消息失败:", error)
        setErrorFormatted(error instanceof Error ? error.message : String(error))
        setFormattedApiMessages([])
      } finally {
        setIsLoadingFormatted(false)
      }
    }

    fetchFormattedMessages()
  }, [currentConversationId, messages]) // 依赖项保持不变，因为左侧消息变化可能也需要刷新右侧

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // 设置左侧面板加载状态
      setIsLoadingMessages(true);
      // 创建新消息对象，只包含必要的前端信息
      // 后端将负责创建完整的 UserMessage 并设置 display_style
      const newMessageForFrontend: Partial<ChatMessageDisplay> = {
        id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // 临时前端ID
        content: inputValue.trim(),
        // display_style 和 timestamp 将由后端在持久化时或前端在收到同步回的消息时填充
        // 对于即时显示，可以暂时给一个默认的 display_style（如果 MessageList 需要）
        // 但更好的做法是 onAddMessage 应该触发一次到后端的同步，然后用后端返回的完整消息更新UI
        display_style: 'user', // 临时用于本地乐观更新，会被后端覆盖
        timestamp: new Date().toISOString(), // 临时时间戳
      }
      // @ts-ignore // 移除或确保类型正确转换
      onAddMessage(newMessageForFrontend as ChatMessageDisplay) // 类型断言可能需要调整
      setInputValue("")
      // 聚焦输入框
      inputRef.current?.focus()

      // Force scroll to bottom on send message for left panel
      requestAnimationFrame(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      });
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden border-r border-gray-200 dark:border-gray-800">
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center space-x-2">
          <span className="font-medium">当前对话</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">({messages.length}条消息)</span>
        </div>
        <Button onClick={() => {
          setIsLoadingMessages(true);
          onRun();
        }} className="bg-blue-600 hover:bg-blue-700 text-white">
          <Play className="mr-2 h-4 w-4" />
          运行
        </Button>
      </div>

      <PanelGroup direction="horizontal" className="flex-1 overflow-hidden">
        <Panel 
          defaultSize={50} 
          minSize={10} 
          collapsible={true} 
          collapsedSize={5} // 折叠后宽度为5%
          onCollapse={() => setIsMessagePanelCollapsed(true)}
          onExpand={() => setIsMessagePanelCollapsed(false)}
          className="h-full flex flex-col" // 确保 Panel 自身能弹性布局其内容
        >
          {isMessagePanelCollapsed ? (
            <div className="flex-1 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400 p-4">
              <MessageSquare className="h-8 w-8 mb-2" />
              <span className="text-xs text-center">消息列表</span>
            </div>
          ) : (
            <div 
              ref={leftPanelScrollRef} // Assign ref to the scrollable div
              className="h-full overflow-y-auto p-4 flex flex-col scrollbar-thin" // Added scrollbar-thin
            >
              <div className="flex-1">
                {/* 左侧加载提示 */}
                {isLoadingMessages && messages.length > 0 && (
                  <div className="sticky top-0 bg-gray-50 dark:bg-gray-900 p-2 mb-2 text-center text-xs text-blue-500 opacity-75 rounded-md z-10">
                    正在更新消息...
                  </div>
                )}
                <MessageList 
                  messages={messages} 
                  onMessagesChange={onMessagesChange} 
                  onDeleteMessage={onDeleteMessage}
                  onUpdateMessage={onUpdateMessage}
                />
              </div>
              <div ref={messagesEndRef} />
            </div>
          )}
        </Panel>
        <PanelResizeHandle className="w-1.5 bg-gray-200 dark:bg-gray-800 hover:bg-blue-500 dark:hover:bg-blue-600 transition-colors duration-200 cursor-col-resize flex items-center justify-center">
          <div className="w-px h-6 bg-gray-400 dark:bg-gray-600"></div>
        </PanelResizeHandle>
        <Panel 
          defaultSize={50} 
          minSize={10} 
          collapsible={true} 
          collapsedSize={5} // 折叠后宽度为5%
          onCollapse={() => setIsFormattedPanelCollapsed(true)}
          onExpand={() => setIsFormattedPanelCollapsed(false)}
          className="h-full flex flex-col" // 确保 Panel 自身能弹性布局其内容
        >
          {isFormattedPanelCollapsed ? (
            <div className="flex-1 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400 p-4">
              <Code className="h-8 w-8 mb-2" />
              <span className="text-xs text-center">格式化消息</span>
            </div>
          ) : (
            <div 
              ref={rightPanelScrollRef} // Assign ref to the scrollable div
              className="h-full overflow-y-auto p-4 scrollbar-thin" // Added scrollbar-thin
            >
              {/* 移除旧的 h3 OpenAI 格式化消息 标题，如果左侧没有对应标题的话 */}
              {/* <h3 className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">OpenAI 格式化消息</h3> */}
              
              {/* 错误状态优先显示 */}
              {errorFormatted ? (
                <div className="text-red-500 dark:text-red-400">
                  <p>加载格式化消息失败:</p>
                  <pre className="text-xs whitespace-pre-wrap">{errorFormatted}</pre>
                </div>
              ) : (
                <> 
                  {/* 加载提示: 当正在加载且有消息时显示，与左侧一致 */}
                  {isLoadingFormatted && formattedApiMessages.length > 0 && (
                    <div className="sticky top-0 bg-gray-50 dark:bg-gray-900 p-2 mb-2 text-center text-xs text-blue-500 opacity-75 rounded-md z-10">
                      正在更新消息...
                    </div>
                  )}
                  
                  {formattedApiMessages.length > 0 ? (
                    <div className="space-y-1">
                      {formattedApiMessages.map((msg, index) => (
                        <FormattedMessageItem key={`${msg.role}-${index}-${msg.content?.substring(0,10)}`} message={msg} />
                      ))}
                    </div>
                  ) : !isLoadingFormatted ? ( // 仅当没有在加载时，才显示"没有消息"
                    <p className="text-gray-500 dark:text-gray-400">没有可显示的格式化消息。</p>
                  ) : null /* 正在加载且没有消息时，不显示任何内容，保持与左侧一致 */}
                </>
              )}
            </div>
          )}
        </Panel>
      </PanelGroup>

      <div className="p-4 border-t border-gray-200 dark:border-gray-800">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" title="添加新消息">
            <Plus className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" title="上传文件">
            <Paperclip className="h-4 w-4" />
          </Button>
          <div className="flex-1 flex items-center space-x-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入消息..."
              className="flex-1"
              disabled={isSyncing}
            />
            <Button 
              onClick={handleSendMessage} 
              disabled={!inputValue.trim() || isSyncing}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
