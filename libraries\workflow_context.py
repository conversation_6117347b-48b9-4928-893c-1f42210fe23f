"""
工作流上下文模块。

该模块提供了 `WorkflowContext` 类，用于简化 Python 工作流的编写。
通过 `WorkflowContext`，用户可以使用更直观的 async/await 语法来定义工作流步骤，
而无需直接操作异步生成器的 yield/send 机制。

`WorkflowContext` 通过内部通信机制与底层的异步生成器包装器进行交互，
确保与现有的 `WorkflowState` 和 `WorkflowLibrary` 系统无缝集成。
"""

import asyncio
from typing import Dict, Any, List, Optional
from libraries.workflow_models import (
    ExecuteStep, ConditionStep, GenerateStep, UserInputStep, HaltStep, ActionDefinition
)
from message import UserMessage
from log import logger


class WorkflowContext:
    """
    工作流上下文类，为 Python 工作流提供简化的编程接口。
    
    用户可以通过此类的方法来定义工作流步骤，而不需要直接处理生成器的 yield/send 逻辑。
    """
    
    def __init__(self, step_communicator: 'StepCommunicator', registers: Dict[str, Any]):
        """
        初始化工作流上下文。
        
        参数:
            step_communicator: 用于与包装器生成器通信的对象
            registers: 工作流寄存器的引用，用于读写状态
        """
        self._communicator = step_communicator
        self._registers = registers
        
    async def execute(
        self, 
        description: str, 
        actions: List[ActionDefinition],
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 EXECUTE 步骤。
        
        参数:
            description: 步骤描述
            actions: 要执行的动作定义列表
            name: 可选的步骤名称
        """
        step = ExecuteStep(
            name=name,
            description=description,
            actions=actions
        )
        await self._communicator.send_step_and_wait(step)
        
    async def condition(
        self,
        description: str,
        name: Optional[str] = None
    ) -> bool:
        """
        执行一个 CONDITION 步骤并返回条件判断结果。
        
        参数:
            description: 条件描述
            name: 可选的步骤名称
            
        返回:
            条件判断的结果 (True/False)
        """
        step = ConditionStep(
            name=name,
            condition_description=description
        )
        result = await self._communicator.send_step_and_wait_for_result(step)
        return bool(result)
        
    async def generate(
        self,
        description: str,
        wait_user: bool = False,
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 GENERATE 步骤。
        
        参数:
            description: 生成内容的描述
            wait_user: 是否在生成后等待用户输入
            name: 可选的步骤名称
        """
        step = GenerateStep(
            name=name,
            content_description=description,
            wait_user=wait_user
        )
        await self._communicator.send_step_and_wait(step)
        
    async def user_input(
        self,
        description: str,
        name: Optional[str] = None
    ) -> UserMessage:
        """
        等待用户输入并返回用户消息。
        
        参数:
            description: 等待用户输入的提示描述
            name: 可选的步骤名称
            
        返回:
            用户输入的消息对象
        """
        step = UserInputStep(
            name=name,
            description=description
        )
        result = await self._communicator.send_step_and_wait_for_result(step)
        if isinstance(result, UserMessage):
            return result
        else:
            logger.warning(f"用户输入步骤返回了非 UserMessage 类型: {type(result)}")
            # 创建一个默认的 UserMessage
            return UserMessage(content=str(result) if result is not None else "")
        
    def get_register(self, name: str, default: Any = None) -> Any:
        """
        获取寄存器的值。
        
        参数:
            name: 寄存器名称
            default: 如果寄存器不存在时的默认值
            
        返回:
            寄存器的值
        """
        return self._registers.get(name, default)
        
    def set_register(self, name: str, value: Any) -> None:
        """
        设置寄存器的值。
        
        参数:
            name: 寄存器名称
            value: 要设置的值
        """
        logger.debug(f"通过 WorkflowContext 设置寄存器 '{name}': {self._registers.get(name)} -> {value}")
        self._registers[name] = value
        
    async def switch(
        self,
        name: str,
        description: str,
        cases: List['CaseDefinition']
    ) -> str:
        """
        执行一个 SWITCH 步骤，允许根据 LLM 的选择切换到不同的工作流分支。
        
        参数:
            name: 步骤名称
            description: 步骤描述
            cases: 可选择的分支列表
            
        返回:
            被选择的分支名称
        """
        # 延迟导入以避免循环导入
        from libraries.workflow_models import SwitchStep
        
        step = SwitchStep(
            name=name,
            description=description,
            cases=cases
        )
        result = await self._communicator.send_step_and_wait_for_result(step)
        return str(result) if result is not None else ""
        
    async def halt(
        self,
        name: Optional[str] = None
    ) -> None:
        """
        终止工作流执行。
        
        参数:
            name: 可选的步骤名称
        """
        step = HaltStep(
            name=name,
            description="工作流终止"
        )
        await self._communicator.send_step_and_wait(step)
        

class StepCommunicator:
    """
    步骤通信器，负责 WorkflowContext 与包装器生成器之间的双向通信。
    """
    
    def __init__(self):
        """初始化通信器。"""
        self._pending_step: Optional[Any] = None
        self._step_sent_event = asyncio.Event()
        self._result_received_event = asyncio.Event()
        self._received_result: Any = None
        
    async def send_step_and_wait(self, step: Any) -> None:
        """
        发送步骤给包装器生成器并等待完成信号。
        
        参数:
            step: 要发送的 WorkflowStep 对象
        """
        self._pending_step = step
        self._step_sent_event.set()
        
        # 等待步骤被处理完成
        await self._result_received_event.wait()
        self._result_received_event.clear()
        
    async def send_step_and_wait_for_result(self, step: Any) -> Any:
        """
        发送步骤给包装器生成器并等待具体的结果值。
        
        参数:
            step: 要发送的 WorkflowStep 对象
            
        返回:
            从外部接收到的结果值
        """
        self._pending_step = step
        self._step_sent_event.set()
        
        # 等待接收结果
        await self._result_received_event.wait()
        result = self._received_result
        self._result_received_event.clear()
        return result
        
    async def get_next_step(self):
        """
        等待并获取下一个要 yield 的步骤（供包装器生成器调用）。
        
        返回:
            待 yield 的 WorkflowStep 对象
        """
        await self._step_sent_event.wait()
        step = self._pending_step
        self._step_sent_event.clear()
        return step
        
    def send_result(self, result: Any) -> None:
        """
        向 WorkflowContext 发送结果（供包装器生成器调用）。
        
        参数:
            result: 要发送的结果值
        """
        self._received_result = result
        self._result_received_event.set() 