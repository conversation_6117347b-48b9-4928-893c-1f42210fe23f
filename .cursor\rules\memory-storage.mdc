---
description: 
globs: MeowAgent 提供灵活、可扩展的记忆与存储系统，支持多种后端存储方式和数据组织形式。
alwaysApply: false
---
# MeowAgent 记忆与存储系统

MeowAgent 提供灵活、可扩展的记忆与存储系统，支持多种后端存储方式和数据组织形式。

## 记忆存储架构

记忆存储系统主要由以下组件构成：

1. [memory.py](mdc:meowagent/libraries/memory.py) - 记忆库主类，提供文件系统式的操作接口
2. [memory_storage.py](mdc:meowagent/libraries/memory_storage.py) - 定义存储后端抽象接口
3. [memory_storage_fs.py](mdc:meowagent/libraries/memory_storage_fs.py) - 文件系统存储实现
4. [memory_storage_oss.py](mdc:meowagent/libraries/memory_storage_oss.py) - 阿里云对象存储实现

存储后端抽象遵循以下设计模式：

```
┌───────────────────────┐
│  MemoryLibrary        │
│  [memory.py]          │◄──────┐
└─────────────┬─────────┘       │
              │                 │
              ▼                 │
┌───────────────────────┐       │
│  MemoryStorageBase    │       │ 工厂模式
│  [memory_storage.py]  │       │ (get_memory_storage)
└─────────────┬─────────┘       │
              │                 │
    ┌─────────┴─────────┐       │
    ▼                   ▼       │
┌─────────────┐   ┌─────────────┐
│  FSStorage  │   │  OSSStorage │
└─────────────┘   └─────────────┘
```

## 存储后端抽象接口

[MemoryStorageBase](mdc:meowagent/libraries/memory_storage.py) 定义了所有存储后端必须实现的接口：

- `initialize()` - 初始化存储后端
- `get_node_by_path()` - 通过路径获取节点信息
- `get_children_of_node()` - 获取目录下的子节点
- `create_node()` - 创建文件或目录
- `update_node_content()` - 更新文件内容
- `read_file_content()` - 读取文件内容
- `rename_node()` - 重命名节点
- `move_node()` - 移动节点
- `delete_node_recursive()` - 递归删除节点
- `path_exists()` - 检查路径是否存在
- `search_nodes_by_name()` - 按名称搜索节点
- `search_file_contents()` - 在文件内容中搜索

## 文件系统式记忆组织

记忆系统使用类似文件系统的结构组织数据：

- 每个用户会话 (conversation_id) 有独立的根目录
- 支持嵌套的文件夹结构
- 文件可以存储任意文本内容
- 支持绝对路径和相对路径

示例结构：
```
/
├── user_preferences/
│   ├── hobbies.txt
│   └── location.txt
├── facts/
│   └── important_dates.txt
└── notes.txt
```

## 历史记忆持久化

[history.py](mdc:meowagent/history.py) 提供对话历史的持久化存储：

- 使用 MongoDB 作为后端存储
- 每条消息作为一个文档存储
- 支持分页加载、按ID检索和排序
- 提供消息更新、删除和重排序操作

主要功能：
```python
# 添加消息
await history.add_message(message)

# 加载最近消息
messages = await history.get_recent_messages(limit=10)

# 更新消息
await history.update_message(message_id, {"content": new_content})

# 重排序消息
await history.reorder_messages(conversation_id, message_id_list)
```

## 数据库配置

[config.py](mdc:meowagent/config.py) 中定义了数据库和存储的配置项：

- `MONGODB_URI` - MongoDB连接字符串
- `MONGODB_DB_NAME` - 数据库名称
- `MEMORY_STORAGE_TYPE` - 记忆存储类型 ("fs" 或 "oss")
- `MEMORY_FS_ROOT` - 文件系统存储根目录
- `OSS_*` - 阿里云OSS配置参数

