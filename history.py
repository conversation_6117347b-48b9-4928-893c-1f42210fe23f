"""
该模块负责对话历史的管理，使用 MongoDB 作为持久化存储后端。
核心类 `History` 为每个 `conversation_id` 提供独立的对话历史管理。

主要功能包括：

1.  **消息持久化与检索**:
    *   `add_message`: 将单个 `Message` 对象异步添加到指定对话的历史记录中，并自动管理 `order_index` 和数据库生成的 `_id`。
    *   `update_message`: 异步更新数据库中已存在的特定消息的指定字段。
    *   `delete_message`: 异步从数据库中删除指定ID的消息。
    *   `get_message_by_id`: 异步根据消息ID从数据库检索单个消息。
    *   `load_messages`: 异步从数据库加载历史消息，支持分页（`limit` 和 `skip` 参数）和排序（按 `order_index` 升序）。
    *   `get_messages`: 异步获取指定对话的所有历史消息。
    *   `get_recent_messages`: 异步获取指定对话最近的N条消息。

2.  **批量操作与管理**:
    *   `replace_all_messages`: 异步替换指定对话的所有消息为新的消息列表，会先删除旧消息再插入新消息。
    *   `reorder_messages`: 异步根据提供的消息ID列表，重新排序数据库中消息的 `order_index`。
    *   `delete_all_messages_for_conversation`: 异步删除指定对话的所有历史消息。

3.  **数据转换与处理**:
    *   在与数据库交互时，自动处理 `Message` 对象与字典格式之间的转换（依赖 `Message.to_dict()` 和 `Message.from_dict()`）。
    *   将 MongoDB 的 `ObjectId` 类型的 `_id` 字段与 `Message` 对象中的字符串 `id` 字段进行转换。
    *   确保时间戳 (`timestamp`) 字段在存入数据库时为 `datetime` 对象。

该模块通过 `database.manager.db_manager_provider` 与 MongoDB 交互，
为智能体的记忆功能提供了基础。所有数据库操作均为异步。
"""

import motor
from motor.motor_asyncio import AsyncIOMotorCollection # 更具体的类型提示
from database.manager import db_manager_provider # 使用新的 provider
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from log import logger
from message import Message # 假设 Message 类有 to_dict 和 from_dict
from bson import ObjectId # 用于处理 MongoDB _id
import asyncio # 需要 asyncio 用于异步操作
from datetime import datetime

# 使用 TYPE_CHECKING 避免循环导入，同时保留类型提示
if TYPE_CHECKING:
    from message import Message


# 历史记录 维护历史，作为一种Append Only的不变数据结构
class History:
    def __init__(self, conversation_id: Optional[str] = None):
        """
        初始化历史记录。
        如果 conversation_id 为 None，则在内存中操作。

        参数:
            conversation_id: 对话ID，用于隔离不同用户的历史记录
        """
        self.conversation_id = conversation_id
        if conversation_id is None:
            self._is_memory_mode = True
            self._memory_messages: List[Message] = []
        else:
            self._is_memory_mode = False

    async def _get_db_collection(self) -> Optional[AsyncIOMotorCollection]:
        """获取 MongoDB 集合对象。如果处于内存模式，则返回 None。"""
        if self._is_memory_mode:
            return None
        manager = await db_manager_provider.get_manager()
        return await manager.get_history_messages_collection()

    async def add_message(self, message: 'Message'):
        """
        添加消息到历史记录。
        内存模式下，简单地将消息对象添加到列表中。
        数据库模式下，持久化到MongoDB。

        参数:
            message: 消息对象
        """
        if self._is_memory_mode:
            self._memory_messages.append(message)
            return

        # 数据库模式
        if not self.conversation_id: # 双重检查，理论上不会执行到这里如果 is_memory_mode
            logger.warning("数据库模式下无法添加消息：conversation_id 未设置")
            if not message.id:
                 message.set_id(str(ObjectId())) # 给一个临时的唯一ID
            return

        collection = await self._get_db_collection()
        if collection is None: # Should not happen if not in memory mode
            logger.error("数据库集合未初始化，无法添加消息。")
            return

        # 确定 order_index
        # 注意: count_documents 给出的是总数，下一个索引是 count
        last_message_doc = await collection.find_one(
            {"conversation_id": self.conversation_id},
            sort=[("order_index", -1)],
            projection={"order_index": 1}
        )
        if last_message_doc and "order_index" in last_message_doc:
            order_index = last_message_doc["order_index"] + 1
        else:
            order_index = 0

        message_data = message.to_dict()
        message_data["conversation_id"] = self.conversation_id
        message_data["order_index"] = order_index
        
        # 确保 timestamp 是 datetime 对象，如果不是，尝试转换
        # message.to_dict() 应该返回 isoformat string, MongoDB driver 会处理
        # 但如果 message.timestamp 是 str, 这里需要处理或确保 to_dict 正确
        if isinstance(message.timestamp, str):
             try:
                 message_data["timestamp"] = datetime.fromisoformat(message.timestamp)
             except ValueError:
                 logger.warning(f"add_message: 无法将时间戳字符串 {message.timestamp} 转换为datetime对象，将使用当前时间")
                 message_data["timestamp"] = datetime.now()
        elif isinstance(message.timestamp, datetime):
             message_data["timestamp"] = message.timestamp
        else:
             logger.warning(f"add_message: 未知的时间戳类型 {type(message.timestamp)}，将使用当前时间")
             message_data["timestamp"] = datetime.now()

        if 'id' in message_data and message_data['id'] is not None:
            # 如果消息本身已经有ID (例如从其他地方同步过来的)，我们可能需要更新而不是插入
            # 但此方法是 add_message，语义上是新增。为简单起见，移除ID，让DB生成新的。
            # 或者，如果ID已存在，则应该走 update 逻辑。这里我们假设 add 总是创建新的。
            # 移除id，让MongoDB生成。如果message.id是外部设置的，并且我们希望保留它，
            # 那么应该使用 message_data['_id'] = ObjectId(message.id) 如果它是有效的ObjectId字符串，
            # 或者寻找 find_one_and_update with upsert=True 的逻辑。
            # 当前逻辑是总是插入新文档。
            if 'id' in message_data: del message_data['id']
        elif 'id' in message_data and message_data['id'] is None:
            del message_data['id'] # 移除 None 的 id

        try:
            result = await collection.insert_one(message_data)
            if result.inserted_id:
                message.set_id(str(result.inserted_id))
            else:
                # logger.error(f"消息插入成功但未返回 inserted_id: {message_data}")
                # 在某些情况下（例如使用自定义_id），insert_one可能不返回inserted_id，但操作可能已成功
                # 我们依赖于前面是否设置了 message.id
                # 如果 del message_data['id'] 被执行，inserted_id 应该总是存在
                # 如果我们尝试使用自定义_id, inserted_id 可能为None，但 result.acknowledged 会是 True
                if not message.id: # 如果此时message.id还是None，说明有问题
                    logger.error(f"消息插入MongoDB后，未能设置message.id。Result: {result.acknowledged}, InsertedId: {result.inserted_id}")
        except Exception as e:
            logger.error(f"向 MongoDB 添加消息时出错: {e}", show_traceback=True)

    async def update_message(self, message_id: str, updated_fields: Dict[str, Any]) -> bool:
        """
        更新指定ID的消息的特定字段。
        内存模式下不支持此操作。
        数据库模式下，更新 MongoDB。

        参数:
            message_id: 要更新的消息ID
            updated_fields: 包含要更新的字段及其新值的字典。

        返回:
            是否成功更新
        """
        if self._is_memory_mode:
            logger.warning("History内存模式不支持 update_message 操作。")
            return False

        # 数据库模式
        if not self.conversation_id: # 数据库模式下 conversation_id 必须存在
            logger.warning("无法更新消息：conversation_id 未设置")
            return False
        if not message_id:
            logger.warning("无法更新消息：message_id 未提供")
            return False

        collection = await self._get_db_collection()
        try:
            # 从 updated_fields 中移除不允许通过此方法修改的保护字段
            # 允许修改 'content', 'summary', 'display_style', 'tool_calls', 'tool_call_id'
            # 'timestamp' 通常在创建时设置，但如果允许API更新它，需要小心处理
            protected_fields = ['_id', 'id', 'conversation_id', 'order_index', 'object', 'role']
            update_set = {k: v for k, v in updated_fields.items() if k not in protected_fields}

            if not update_set:
                logger.warning("没有提供可更新的字段")
                return False
            
            # 如果 'content' 在更新字段中，确保它不是 None (除非 schema 允许)
            # Message 类定义 content: Optional[Any]，但具体子类可能要求 content 不为 None
            # 例如 UserMessage.content: str. 如果API允许将 UserMessage.content 更新为 None 会有问题
            # 这里假设 updated_fields 传入的值是合法的

            # 如果 'timestamp' 在更新字段中，并且是字符串，尝试转换为 datetime
            if 'timestamp' in update_set and isinstance(update_set['timestamp'], str):
                try:
                    update_set['timestamp'] = datetime.fromisoformat(update_set['timestamp'])
                except ValueError:
                    logger.warning(f"更新消息时，无法将时间戳字符串 {update_set['timestamp']} 转换为 datetime，将忽略该字段的更新。")
                    del update_set['timestamp'] # 或者返回错误

            result = await collection.update_one(
                {"_id": ObjectId(message_id), "conversation_id": self.conversation_id},
                {"$set": update_set}
            )
            # modified_count > 0 表示实际发生了修改
            # matched_count > 0 表示找到了文档
            if result.matched_count == 0:
                logger.warning(f"更新消息失败：未找到消息ID {message_id} (conv_id: {self.conversation_id})")
                return False
            if result.modified_count == 0 and result.matched_count > 0:
                logger.info(f"消息ID {message_id} (conv_id: {self.conversation_id}) 已找到但内容未更改。")
                # 这种情况也认为是"成功"的，因为请求被处理了，只是没有导致数据变化。
                # 如果需要区分"找到但未修改"和"成功修改"，可以返回更详细的信息。
                return True 
            
            return result.modified_count > 0

        except Exception as e:
            logger.error(f"更新 MongoDB 消息 ({message_id}) 时出错: {e}", show_traceback=True)
            return False

    async def delete_message(self, message_id: str) -> bool:
        """
        删除指定ID的消息。
        内存模式下不支持此操作。
        数据库模式下，从 MongoDB 删除。

        参数:
            message_id: 要删除的消息ID

        返回:
            是否成功删除
        """
        if self._is_memory_mode:
            logger.warning("History内存模式不支持 delete_message 操作。")
            return False

        # 数据库模式
        if not self.conversation_id: # 数据库模式下 conversation_id 必须存在
            logger.warning("无法删除消息：conversation_id 未设置")
            return False
        if not message_id:
            logger.warning("无法删除消息：message_id 未提供")
            return False

        collection = await self._get_db_collection()
        try:
            result = await collection.delete_one(
                {"_id": ObjectId(message_id), "conversation_id": self.conversation_id}
            )
            if result.deleted_count == 0:
                 logger.warning(f"删除消息失败：未找到消息ID {message_id} (conv_id: {self.conversation_id})")
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除 MongoDB 消息 ({message_id}) 时出错: {e}", show_traceback=True)
            return False

    async def replace_all_messages(self, new_messages: List['Message']) -> bool:
        """
        替换所有消息为新的消息列表。
        内存模式下，直接替换内存列表。
        数据库模式下，清空并重新插入 MongoDB。

        参数:
            new_messages: 新的消息对象列表

        返回:
            是否成功替换
        """
        if self._is_memory_mode:
            self._memory_messages = []
            for message in new_messages:
                self._memory_messages.append(message)
            logger.debug(f"内存模式：替换了所有消息，新消息数: {len(self._memory_messages)}")
            return True

        # 数据库模式
        if not self.conversation_id:
            logger.warning("无法替换消息：conversation_id 未设置")
            return False

        collection = await self._get_db_collection()
        try:
            # 1. 清除该用户的所有现有消息
            await collection.delete_many({"conversation_id": self.conversation_id})

            # 2. 准备并插入新消息
            if new_messages:
                messages_to_insert = []
                for i, message in enumerate(new_messages):
                    message_data = message.to_dict()
                    message_data["conversation_id"] = self.conversation_id
                    message_data["order_index"] = i # 使用列表索引作为 order_index
                    
                    if isinstance(message.timestamp, str):
                        try:
                            message_data["timestamp"] = datetime.fromisoformat(message.timestamp)
                        except ValueError:
                            message_data["timestamp"] = datetime.now()
                    elif isinstance(message.timestamp, datetime):
                        message_data["timestamp"] = message.timestamp
                    else:
                        message_data["timestamp"] = datetime.now()
                        
                    if 'id' in message_data: del message_data['id'] # MongoDB 会生成 _id
                    messages_to_insert.append(message_data)

                if messages_to_insert:
                    insert_result = await collection.insert_many(messages_to_insert)
                    # 更新传入的 new_messages 列表中每个 Message 对象的 ID
                    for i, message in enumerate(new_messages):
                        if i < len(insert_result.inserted_ids):
                            message.set_id(str(insert_result.inserted_ids[i]))
            return True
        except Exception as e:
            logger.error(f"替换 MongoDB 所有消息时出错 (conv_id: {self.conversation_id}): {e}", show_traceback=True)
            return False

    async def reorder_messages(self, ordered_message_ids: List[str]) -> bool:
        """
        根据提供的消息ID列表，重新排序消息。
        内存模式下不支持此操作。
        数据库模式下，更新 MongoDB 中的 order_index 字段。

        参数:
            ordered_message_ids: 按新顺序排列的消息ID列表

        返回:
            是否成功执行了所有更新操作
        """
        if self._is_memory_mode:
            logger.warning("History内存模式不支持 reorder_messages 操作。")
            return False

        # 数据库模式
        if not self.conversation_id:
            logger.warning(f"无法重新排序消息：conversation_id 未设置")
            return False
        
        # if not ordered_message_ids: # 允许空列表，表示清空后重新排序（虽然通常不会这样用）
        #     logger.info(f"消息ID列表为空，无需重新排序 (conv_id: {self.conversation_id})")
        #     return True 

        collection = await self._get_db_collection()
        try:
            # 使用 bulk_write 执行多个更新操作以提高效率
            operations = []
            for index, msg_id_str in enumerate(ordered_message_ids):
                try:
                    object_id = ObjectId(msg_id_str)
                    operations.append(
                        motor.UpdateOne(
                            {"_id": object_id, "conversation_id": self.conversation_id},
                            {"$set": {"order_index": index}}
                        )
                    )
                except Exception as e_id: # bson.errors.InvalidId
                    logger.warning(f"无效的消息ID '{msg_id_str}' 在重新排序时被跳过: {e_id}")
                    # 根据需求，可以选择让整个操作失败或继续处理有效ID
                    # return False 

            if not operations and ordered_message_ids: # 有传入ID但都无效
                 logger.warning(f"没有有效的操作可用于消息重新排序，所有传入ID均无效 (conv_id: {self.conversation_id})")
                 return False
            
            if not operations and not ordered_message_ids: # 传入空ID列表
                 logger.info(f"消息ID列表为空，重新排序操作完成 (conv_id: {self.conversation_id})")
                 return True


            result = await collection.bulk_write(operations, ordered=False) # ordered=False 允许部分成功
            
            # 检查是否所有预期的操作都匹配并修改了文档
            expected_modifications = len(operations)
            if result.modified_count == expected_modifications:
                logger.info(f"消息成功重新排序 (conv_id: {self.conversation_id})。修改数量: {result.modified_count}")
                return True
            else:
                logger.warning(f"消息重新排序可能部分失败 (conv_id: {self.conversation_id})。"
                               f"预期修改: {expected_modifications}, 实际修改: {result.modified_count}, 匹配数量: {result.matched_count}")
                # 即使部分成功，也可能需要返回 True，具体取决于业务逻辑
                # 如果要求原子性，那么这里应该返回 False，并在调用处处理
                return result.modified_count > 0 # 至少有一个成功就算成功

        except Exception as e:
            logger.error(f"重新排序 MongoDB 消息时出错 (conv_id: {self.conversation_id}): {e}", show_traceback=True)
            return False

    async def load_messages(self, limit: Optional[int] = None, skip: Optional[int] = None) -> List['Message']:
        """
        加载历史消息。
        内存模式下，从内存列表加载，支持分页。
        数据库模式下，从 MongoDB 加载。

        参数:
            limit: 限制加载的消息数量
            skip: 跳过指定数量的消息

        返回:
            消息对象列表
        """
        if self._is_memory_mode:
            start_index = skip if skip is not None and skip > 0 else 0
            end_index = (start_index + limit) if limit is not None and limit > 0 else len(self._memory_messages)
            
            return self._memory_messages[start_index:end_index]

        # 数据库模式
        if not self.conversation_id:
            logger.debug("load_messages 调用时 conversation_id 未设置，返回空列表")
            return []

        collection = await self._get_db_collection()
        try:
            query = collection.find({"conversation_id": self.conversation_id})
            query = query.sort("order_index", 1) # 按 order_index 升序

            if skip is not None and skip > 0:
                query = query.skip(skip)
            if limit is not None and limit > 0:
                query = query.limit(limit)

            loaded_messages: List['Message'] = []
            async for doc in query:
                # 确保 _id (ObjectId) 转换为 'id' (str)
                doc['id'] = str(doc.pop('_id', None)) # 使用 pop 以免 'id' 和 '_id' 同时存在
                try:
                    message_obj = Message.from_dict(doc) # Message.from_dict 会处理 object 类型
                    loaded_messages.append(message_obj)
                except Exception as e_deserialize:
                    logger.error(f"从数据库反序列化消息时出错 (doc id: {doc.get('id', 'N/A')}): {e_deserialize}", show_traceback=True)
                    # 可以选择跳过此消息或添加一个错误占位符
            return loaded_messages
        except Exception as e:
            logger.error(f"从 MongoDB 加载消息时出错 (conv_id: {self.conversation_id}): {e}", show_traceback=True)
            return []

    async def get_messages(self) -> List['Message']:
        """
        获取指定 conversation_id 的所有历史消息。
        直接调用 load_messages 不带 limit。

        返回:
            消息对象列表的副本
        """
        return await self.load_messages()

    async def get_recent_messages(self, n: int) -> List['Message']:
        """
        获取最近的n条消息。
        内存模式下，从内存列表末尾获取。
        数据库模式下，从 MongoDB 查询。

        参数:
            n: 消息数量 (必须大于0)

        返回:
            最近n条消息对象列表
        """
        if n <= 0:
            return []

        if self._is_memory_mode:
            if not self._memory_messages:
                return []
            
            start_index = max(0, len(self._memory_messages) - n)
            return self._memory_messages[start_index:]
            
        # 数据库模式
        if not self.conversation_id:
            logger.debug("get_recent_messages 调用时 conversation_id 未设置，返回空列表")
            return []
        if n <= 0:
            return []

        collection = await self._get_db_collection()
        try:
            query = collection.find({"conversation_id": self.conversation_id})
            # 按 order_index 降序获取最后 N 条
            query = query.sort("order_index", -1).limit(n)

            recent_db_messages_raw: List[Dict[str, Any]] = []
            async for doc in query:
                doc['id'] = str(doc.pop('_id', None))
                recent_db_messages_raw.append(doc)
            
            recent_messages: List['Message'] = []
            # 结果是按 order_index 降序排列的，需要反转回升序
            for doc in reversed(recent_db_messages_raw):
                try:
                    message_obj = Message.from_dict(doc)
                    recent_messages.append(message_obj)
                except Exception as e_deserialize:
                    logger.error(f"从数据库反序列化最近消息时出错 (doc id: {doc.get('id', 'N/A')}): {e_deserialize}", show_traceback=True)
            
            return recent_messages
        except Exception as e:
            logger.error(f"从 MongoDB 获取最近消息时出错 (conv_id: {self.conversation_id}): {e}", show_traceback=True)
            return []

    async def update_message_summary(self, message_id: str, summary: str) -> bool:
        """
        更新消息的摘要并持久化到数据库 (MongoDB)。

        参数:
            message_id: 要更新的消息的 ID (MongoDB _id 的字符串形式)
            summary: 摘要内容
        返回:
            是否成功更新
        """
        return await self.update_message(message_id, {"summary": summary})

    async def get_message_by_id(self, message_id: str) -> Optional['Message']:
        """
        根据 ID 获取单个消息。
        内存模式下不支持此操作。
        数据库模式下，从 MongoDB 查询。

        参数:
            message_id: 消息的 ID

        返回:
            消息对象，如果未找到则返回 None。
        """
        if self._is_memory_mode:
            logger.warning("History内存模式不支持 get_message_by_id 操作。")
            return None

        # 数据库模式
        if not self.conversation_id or not message_id:
            return None
        
        collection = await self._get_db_collection()
        try:
            doc = await collection.find_one({"_id": ObjectId(message_id), "conversation_id": self.conversation_id})
            if doc:
                doc['id'] = str(doc.pop('_id', None))
                return Message.from_dict(doc)
            return None
        except Exception as e: # Catches bson.errors.InvalidId if message_id is not a valid ObjectId string
            logger.error(f"通过 ID ({message_id}) 获取 MongoDB 消息时出错: {e}", show_traceback=True)
            return None

    async def delete_all_messages_for_conversation(self) -> bool:
        """
        删除当前会话的所有历史消息。
        内存模式下，清空内存列表。
        数据库模式下，从 MongoDB 删除。

        返回:
            bool: 是否成功执行操作。
        """
        if self._is_memory_mode:
            count = len(self._memory_messages)
            self._memory_messages = []
            logger.info(f"内存模式：为当前会话删除了 {count} 条消息。")
            return True

        # 数据库模式
        if not self.conversation_id:
            logger.warning("数据库模式无法删除所有消息：conversation_id 未设置")
            return False

        collection = await self._get_db_collection()
        if collection is None: # Should not happen if not in memory mode
            logger.error("数据库集合未初始化，无法删除所有消息。")
            return False
        try:
            result = await collection.delete_many({"conversation_id": self.conversation_id})
            logger.info(f"为对话 {self.conversation_id} 删除了 {result.deleted_count} 条消息。")
            return True # 即使没有消息被删除（deleted_count为0），操作本身也是成功的
        except Exception as e:
            logger.error(f"为对话 {self.conversation_id} 删除所有 MongoDB 消息时出错: {e}", show_traceback=True)
            return False
