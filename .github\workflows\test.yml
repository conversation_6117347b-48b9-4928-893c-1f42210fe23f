name: Run Pytest

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout the repository
      uses: actions/checkout@v4
    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@v5
      with:
        enable-cache: true
    - name: Test with pytest
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      run: uv run --frozen pytest test.py -s
