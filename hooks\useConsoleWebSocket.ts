import { useState, useEffect, useCallback, useRef } from 'react';
import type { RawBackendMessage } from '@/types/message';

const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8000/ws';

interface WebSocketHook {
  isConnected: boolean;
  lastMessage: RawBackendMessage | null;
  sendMessage: (message: object) => void; // eslint-disable-line @typescript-eslint/ban-types
}

export function useConsoleWebSocket(
  onMessageCallback: (message: RawBackendMessage) => void,
  currentConversationId?: string | null
): WebSocketHook {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<RawBackendMessage | null>(null);
  const ws = useRef<WebSocket | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 10; // 最大重连次数
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMounted = useRef(true);
  const isConnectedRef = useRef(false);
  const currentConversationIdRef = useRef<string | null | undefined>(currentConversationId);

  // 更新 conversationId ref 当 prop 变化时
  useEffect(() => {
    currentConversationIdRef.current = currentConversationId;
  }, [currentConversationId]);

  // 发送初始状态请求
  const sendInitialStateRequest = useCallback(() => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN && currentConversationIdRef.current) {
      try {
        ws.current.send(JSON.stringify({
          type: 'request_initial_state',
          conversation_id: currentConversationIdRef.current,
        }));
        console.log('发送初始状态请求:', currentConversationIdRef.current);
      } catch (error) {
        console.warn('发送初始状态请求失败:', error);
        onMessageCallback({
          type: 'frontend_warning',
          content: `发送初始状态请求失败: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }, [onMessageCallback]);

  const connect = useCallback(() => {
    // 如果组件已卸载，不要尝试连接
    if (!isMounted.current) return;
    
    // 如果存在之前的重连计时器，清除它
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // 如果已经连接，不做任何事
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    // 如果有已存在的连接，但不是OPEN状态，先关闭它
    if (ws.current) {
      try {
        ws.current.close();
      } catch (err) {
        // 忽略关闭错误
      }
    }

    try {
      // 创建新的WebSocket连接
      ws.current = new WebSocket(WEBSOCKET_URL);

      ws.current.onopen = () => {
        if (!isMounted.current) return;
        
        console.log('Console WebSocket connected');
        setIsConnected(true);
        isConnectedRef.current = true;
        reconnectAttempts.current = 0; // 重置重连计数
        
        // 连接建立后立即发送初始状态请求
        if (currentConversationIdRef.current) {
          sendInitialStateRequest();
        } else {
          console.log('未发送初始状态请求：conversationId 不存在');
        }
      };

      ws.current.onmessage = (event) => {
        if (!isMounted.current) return;
        
        try {
          const messageData = JSON.parse(event.data as string) as RawBackendMessage;
          setLastMessage(messageData);
          if (onMessageCallback) {
            onMessageCallback(messageData);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error, event.data);
          // Send a special error message to the console via callback
          onMessageCallback({
            type: 'frontend_error',
            content: `控制台WebSocket消息解析错误: ${event.data as string}`,
            timestamp: new Date().toISOString(),
          });
        }
      };

      ws.current.onerror = (error) => {
        if (!isMounted.current) return;
        
        console.error('Console WebSocket error:', error);
        // Send a special error message to the console via callback
        onMessageCallback({
          type: 'frontend_error',
          content: '控制台WebSocket连接发生错误',
          timestamp: new Date().toISOString(),
        });
      };

      ws.current.onclose = () => {
        if (!isMounted.current) return;
        
        console.log('Console WebSocket disconnected');
        setIsConnected(false);
        isConnectedRef.current = false;
        
        // 发送断开连接的消息
        onMessageCallback({
          type: 'frontend_info',
          content: '控制台WebSocket连接已断开，尝试重新连接...',
          timestamp: new Date().toISOString(),
        });

        // 实现指数退避重连逻辑
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000); // 最长等待30秒
          
          onMessageCallback({
            type: 'frontend_info',
            content: `将在 ${Math.round(timeout/1000)} 秒后尝试重新连接... (尝试 ${reconnectAttempts.current + 1}/${maxReconnectAttempts})`,
            timestamp: new Date().toISOString(),
          });

          // 确保组件仍然挂载
          if (isMounted.current) {
            reconnectTimeoutRef.current = setTimeout(() => {
              // 再次检查组件是否挂载，防止setTimeout回调时组件已卸载
              if (isMounted.current && !isConnectedRef.current) {
                reconnectAttempts.current += 1;
                connect();
              }
            }, timeout);
          }
        } else {
          onMessageCallback({
            type: 'frontend_error',
            content: `已达到最大重连次数 (${maxReconnectAttempts})，请刷新页面重试`,
            timestamp: new Date().toISOString(),
          });
        }
      };
    } catch (err) {
      console.error('创建WebSocket实例时发生错误:', err);
      onMessageCallback({
        type: 'frontend_error',
        content: `WebSocket创建失败: ${err instanceof Error ? err.message : String(err)}`,
        timestamp: new Date().toISOString(),
      });
    }
  }, [onMessageCallback, sendInitialStateRequest]);

  useEffect(() => {
    isMounted.current = true; // 组件挂载时设置标记
    connect();
    
    return () => {
      isMounted.current = false; // 组件卸载时清除标记
      // 清理重连计时器
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      // 关闭连接
      if (ws.current) {
        // 设置onclose为空函数，防止卸载时的重连逻辑
        try {
          ws.current.onclose = null;
          ws.current.close();
        } catch (e) {
          // 忽略关闭时的错误
        }
        ws.current = null;
      }
    };
  }, [connect]);

  // 当 conversationId 变化时，如果已连接则重新发送初始状态请求
  useEffect(() => {
    if (isConnectedRef.current && currentConversationId) {
      sendInitialStateRequest();
    }
  }, [currentConversationId, sendInitialStateRequest]);

  const sendMessage = useCallback((message: object) => { // eslint-disable-line @typescript-eslint/ban-types
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify(message));
      } catch (error) {
        console.warn('发送WebSocket消息时出错:', error);
        onMessageCallback({
          type: 'frontend_warning',
          content: `发送消息失败: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString(),
        });
      }
    } else {
      console.warn('WebSocket not connected. Cannot send message:', message);
      onMessageCallback({
        type: 'frontend_warning',
        content: 'WebSocket 未连接，无法发送消息',
        timestamp: new Date().toISOString(),
      });
    }
  }, [onMessageCallback]);

  return { isConnected, lastMessage, sendMessage };
} 