"""
该模块提供 MongoDB 连接和数据库实例管理功能。
主要功能包括：

1. MongoDB 连接管理：
   - 使用 Motor 作为异步 MongoDB 驱动。
   - 从配置中读取连接 URI 和数据库名称。
   - 提供异步客户端实例。

2. 数据库实例获取：
   - 提供方法以获取 MongoDB 数据库对象。
   - 提供方法以获取特定集合 (例如 history_messages)。

3. 单例模式实现：
   - 确保全局使用同一个 MongoDB 连接管理器。
"""

import motor.motor_asyncio
import config
from log import logger
import asyncio
from typing import TYPE_CHECKING, Any, List, Type, Union, cast, Optional


class MongoManager:
    """
    MongoDB 管理器单例类，负责创建和管理 MongoDB 连接。
    """
    _instance = None
    _client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None
    _db: Optional[motor.motor_asyncio.AsyncIOMotorDatabase] = None
    _init_lock = asyncio.Lock() # 锁确保初始化只发生一次
    _is_connected = False  # 添加连接状态标志
    _reconnect_task = None  # 重连任务引用

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MongoManager, cls).__new__(cls)
            # 初始化推迟到 get_instance 以便异步操作
        return cls._instance

    async def _initialize(self):
        """
        异步初始化 MongoDB 连接。
        """
        if self._client is None: # 只有在客户端未初始化时才尝试连接
            logger.info(f"正在连接到 MongoDB {config.MONGO_URI}...")
            try:
                # 设置连接超时和更高的重试次数
                self._client = motor.motor_asyncio.AsyncIOMotorClient(
                    config.MONGO_URI,
                    serverSelectionTimeoutMS=5000,  # 服务器选择超时为5秒
                    connectTimeoutMS=10000,  # 连接超时为10秒
                    socketTimeoutMS=45000,  # Socket超时为45秒
                    retryWrites=True,  # 自动重试写操作
                    maxPoolSize=50,  # 连接池大小
                    minPoolSize=10,  # 最小连接池大小
                )
                # 执行ping命令验证连接
                await self._client.admin.command('ping')
                self._db = self._client[config.MONGO_DATABASE_NAME]
                logger.success(f"成功连接到 MongoDB。数据库: {config.MONGO_DATABASE_NAME}")
                self._is_connected = True  # 设置连接状态为已连接
                await self._ensure_indexes()
                
                # 如果之前有重连任务，取消它
                if self._reconnect_task and not self._reconnect_task.done():
                    self._reconnect_task.cancel()
                    self._reconnect_task = None
                    
            except Exception as e:
                logger.error(f"连接 MongoDB 失败: {e}", show_traceback=True)
                self._client = None # 连接失败则重置
                self._db = None
                self._is_connected = False  # 设置连接状态为未连接
                
                # 而不是立即抛出异常，我们设置一个后台任务来重试连接
                self._schedule_reconnect()
                
                # 对于初始化，我们仍然需要抛出异常，因为调用者需要知道初始连接失败
                raise  # 重新抛出异常，让调用者知道初始化失败

    def _schedule_reconnect(self):
        """
        调度一个后台任务来尝试重新连接数据库。
        这确保了应用程序其他部分可以继续运行，
        同时我们异步尝试恢复数据库连接。
        """
        if self._reconnect_task is None or self._reconnect_task.done():
            self._reconnect_task = asyncio.create_task(self._reconnect_loop())

    async def _reconnect_loop(self):
        """
        在后台循环尝试重新连接到MongoDB，使用指数退避策略。
        """
        attempt = 0
        max_attempts = 20  # 设置一个合理的最大尝试次数
        while attempt < max_attempts and not self._is_connected:
            attempt += 1
            wait_time = min(2 ** attempt, 60)  # 指数退避，最多等待60秒
            logger.info(f"将在 {wait_time} 秒后尝试重新连接MongoDB (尝试 {attempt}/{max_attempts})...")
            await asyncio.sleep(wait_time)
            
            try:
                # 尝试重新连接
                self._client = motor.motor_asyncio.AsyncIOMotorClient(
                    config.MONGO_URI,
                    serverSelectionTimeoutMS=5000,
                    connectTimeoutMS=10000,
                    socketTimeoutMS=45000,
                    retryWrites=True,
                    maxPoolSize=50,
                    minPoolSize=10,
                )
                await self._client.admin.command('ping')
                self._db = self._client[config.MONGO_DATABASE_NAME]
                self._is_connected = True
                logger.success(f"成功重新连接到 MongoDB。数据库: {config.MONGO_DATABASE_NAME}")
                await self._ensure_indexes()
                return  # 成功连接，退出重连循环
                
            except Exception as e:
                logger.warning(f"尝试 {attempt} 重新连接 MongoDB 失败: {e}")
                self._client = None
                self._db = None
                self._is_connected = False
                
        if not self._is_connected:
            logger.error(f"重新连接 MongoDB 失败，已达到最大尝试次数: {max_attempts}")

    async def _ensure_indexes(self):
        """
        确保必要的索引存在于集合中。
        """
        if self._db is not None:
            try:
                history_collection = self._db.history_messages
                # 为 conversation_id 创建索引
                await history_collection.create_index("conversation_id")
                # 为 conversation_id 和 timestamp 创建复合索引
                await history_collection.create_index([("conversation_id", 1), ("timestamp", 1)])
                # 为 conversation_id 和 order_index 创建复合索引
                await history_collection.create_index([("conversation_id", 1), ("order_index", 1)])
                logger.info("已为 history_messages 集合创建索引。")
            except Exception as e:
                logger.warning(f"创建索引时出错: {e}", show_traceback=True)
                # 索引创建失败不应该阻止应用程序继续工作


    @classmethod
    async def get_instance(cls) -> 'MongoManager':
        """
        获取 MongoManager 的单例实例，并在首次调用时进行异步初始化。
        """
        # 优化：如果实例已存在且已初始化，直接返回
        if cls._instance is not None and cls._instance._client is not None:
            return cls._instance

        async with cls._init_lock: # 使用锁确保只有一个协程进行初始化
            # 再次检查，因为可能有其他协程在等待锁，或者实例可能在等待期间被创建并初始化
            if cls._instance is None: # 如果实例从未被创建
                # 调用 __new__ 来创建实例 (它会处理单例逻辑)
                # 注意：直接调用 cls() 或 super().__new__(cls) 可能绕过 __new__ 中的单例检查
                # 最安全的方式是依赖 __new__ 的隐式调用或显式管理
                # 由于 __new__ 已经处理了实例创建，我们只需要确保调用它
                # 但直接在类方法中创建实例的标准方式是调用 cls()
                # 为了与现有 __new__ 配合，我们假设 __new__ 会被调用或已创建实例
                # 但更健壮的方式是在这里创建
                instance = cls() # 调用 cls() 会触发 __new__ 和 __init__ (如果定义了)
                                 # 由于我们没有 __init__，这主要触发 __new__
                # 现在 instance 应该是 cls._instance (由 __new__ 设置)
                # 并且我们需要初始化它
                await instance._initialize()
            elif cls._instance._client is None: # 实例已创建但未初始化 (例如上次初始化失败)
                await cls._instance._initialize()

        # 确保返回的是有效实例，如果初始化失败 _initialize 会抛异常
        if cls._instance is None or cls._instance._client is None:
             # 这个情况理论上不应该发生，因为 _initialize 失败会抛出异常
             # 但为了健壮性，添加一个检查
             raise ConnectionError("MongoDB 管理器实例初始化失败。")

        return cls._instance

    async def get_db(self) -> motor.motor_asyncio.AsyncIOMotorDatabase:
        """
        获取数据库实例。如果尚未初始化，则先初始化。
        如果数据库连接已断开，尝试重新连接。
        """
        instance = await self.get_instance() # 确保已初始化
        
        # 如果未连接，尝试重连
        if not instance._is_connected or instance._db is None:
            await instance._initialize()  # 这将重新尝试连接
            
        # 如果仍然无法连接，但希望应用程序继续运行
        # 可以返回一个虚拟数据库对象或记录日志然后返回None
        # 为了保持与现有代码兼容，我们抛出一个异常
        if instance._db is None:
            logger.error("MongoDB 数据库未连接，返回虚拟对象")
            raise ConnectionError("MongoDB 数据库未初始化。")
            
        return instance._db

    async def get_history_messages_collection(self) -> motor.motor_asyncio.AsyncIOMotorCollection:
        """
        获取 history_messages 集合。
        """
        try:
            db = await self.get_db()
            return db.history_messages
        except ConnectionError as e:
            logger.error(f"获取 history_messages 集合失败: {e}")
            # 或者可以返回虚拟集合，但目前抛出异常保持与现有代码兼容
            raise

    async def close_connection(self):
        """
        关闭 MongoDB 连接。
        """
        if self._client:
            # 如果有重连任务，取消它
            if self._reconnect_task and not self._reconnect_task.done():
                self._reconnect_task.cancel()
                self._reconnect_task = None

            self._client.close()
            self._client = None
            self._db = None
            self._is_connected = False
            logger.info("MongoDB 连接已关闭。")

# 创建单例实例，方便直接导入
# 注意：直接实例化 MongoManager() 是同步的，但其连接的建立是异步的。
# 应用应该使用 await MongoManager.get_instance() 来获取并确保初始化。
# 为了模块级别的便利访问，可以定义一个异步函数来获取初始化后的管理器。
# db_manager = MongoManager() # 不能直接这样实例化然后期望它已连接

async def get_mongo_manager() -> MongoManager:
    return await MongoManager.get_instance()

# 导出以供其他模块使用
# 模块的用户应该使用 await get_mongo_manager()
# 或者，如果他们需要 db_manager 这个名字，可以这样：
# _db_manager_instance: Optional[MongoManager] = None
# async def get_initialized_db_manager():
#     global _db_manager_instance
#     if _db_manager_instance is None or _db_manager_instance._client is None:
#         _db_manager_instance = await MongoManager.get_instance()
#     return _db_manager_instance
# db_manager 将是一个异步获取的实例

# 简化：直接提供异步获取的单例访问点
class DBManagerProvider:
    _manager: Optional[MongoManager] = None
    _lock = asyncio.Lock()

    async def get_manager(self) -> MongoManager:
        if self._manager is None or self._manager._client is None:
            async with self._lock:
                if self._manager is None or self._manager._client is None:
                    self._manager = await MongoManager.get_instance()
        if self._manager is None: # 如果 get_instance 失败并返回 None (理论上它会抛异常)
            raise ConnectionError("获取 MongoDB 管理器实例失败。")
        return self._manager

db_manager_provider = DBManagerProvider()

# 为了旧代码的兼容性，可以尝试模拟 db_manager.connection()
# 但 Motor 的会话管理和事务与 Peewee 不同。
# Motor 的操作通常是每个调用一个网络请求，不需要显式的 connect/close 对。
# 客户端通常在应用启动时创建一次，在关闭时关闭一次。

# 这里不再导出 db_manager 直接作为 MongoManager 实例，
# 因为它的初始化是异步的。模块用户应使用 await db_manager_provider.get_manager()
# 或者，如果需要一个简单的、总是返回初始化实例的 db_manager，需要更复杂的异步初始化模式。

# 一个更简单的 db_manager 访问方式（如果应用总是在异步上下文中访问它）
# async def get_db():
#     manager = await db_manager_provider.get_manager()
#     return await manager.get_db()

# async def get_history_collection():
#    manager = await db_manager_provider.get_manager()
#    return await manager.get_history_messages_collection() 