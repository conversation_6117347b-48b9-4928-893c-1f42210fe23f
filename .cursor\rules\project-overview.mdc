---
description: 
globs: 
alwaysApply: true
---
# MeowAgent 项目概述

MeowAgent 是一个灵活、可扩展的智能代理（Agent）框架，专注于与大型语言模型（LLM）的集成和交互。框架设计为模块化结构，支持多种前端界面（CLI、API、Web），并提供丰富的工具库和工作流系统。

## 核心架构

```
┌─────────────────────┐      ┌───────────────┐      ┌─────────────────┐
│  用户界面           │      │  Agent        │      │  模型           │
│  - CLI [cli.py]     │◄────►│  [agent.py]   │◄────►│  [model.py]     │
│  - API [api.py]     │      │               │      │                 │
│  - TUI [tui.py]     │      └───────┬───────┘      └─────────────────┘
└─────────────────────┘              │
                                     │
                           ┌─────────▼───────────┐
                           │  工具库系统         │
                           │  [libraries/]       │
                           └─────────┬───────────┘
                                     │
                           ┌─────────▼───────────┐
                           │  持久化存储         │
                           │  [database/]        │
                           └─────────────────────┘
```

## 主要入口文件

- [agent.py](mdc:meowagent/agent.py) - Agent 核心实现
- [agent_manager.py](mdc:meowagent/agent_manager.py) - 管理多个 Agent 实例
- [cli.py](mdc:meowagent/cli.py) - 命令行界面
- [api.py](mdc:meowagent/api.py) - FastAPI Web 服务器
- [tui.py](mdc:meowagent/tui.py) - 文本用户界面

## 核心模块

- [model.py](mdc:meowagent/model.py) - LLM 模型接口
- [message.py](mdc:meowagent/message.py) - 消息类型定义
- [conversation.py](mdc:meowagent/conversation.py) - 对话会话管理
- [history.py](mdc:meowagent/history.py) - 对话历史存储
- [config.py](mdc:meowagent/config.py) - 全局配置

## 工具库与扩展

- [libraries/library.py](mdc:meowagent/libraries/library.py) - 工具库基类
- [libraries/memory.py](mdc:meowagent/libraries/memory.py) - 长期记忆管理
- [libraries/document.py](mdc:meowagent/libraries/document.py) - 文档管理与检索
- [libraries/workflow.py](mdc:meowagent/libraries/workflow.py) - 工作流执行系统
- [libraries/mcp_client.py](mdc:meowagent/libraries/mcp_client.py) - MCP 客户端

## Web 前端

Web 前端使用 Next.js 和 React 构建，位于 meowagent-web 目录下：

- [meowagent-web/app/page.tsx](mdc:meowagent/meowagent-web/app/page.tsx) - 主页面
- [meowagent-web/components/](mdc:meowagent/meowagent-web/components) - UI 组件

