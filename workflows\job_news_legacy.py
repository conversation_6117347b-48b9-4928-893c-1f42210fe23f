from typing import Dict, Any, AsyncGenerator
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "新闻检索工作流，专门用于搜索和获取最新的科技新闻资讯"
INITIAL_REGISTERS: Dict[str, Any] = {}

# --- 工作流生成器函数 ---
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    新闻检索工作流步骤生成器。
    
    通过浏览器自动化和网络请求获取实时科技新闻内容，并生成结构化的新闻摘要。

    请使用Bing检索最新的科技新闻资讯，并抓取最新的科技新闻资讯。
    """

    max_attempts = 3

    for i in range(max_attempts):
        # 步骤 1: 搜索和抓取新闻内容
        yield ExecuteStep(
            name="search_news",
            description="请使用Bing检索最新的科技新闻资讯，并抓取最新的科技新闻资讯",
            actions=[
                ActionDefinition(
                    names=["playwright"], 
                    min_calls=1, 
                    max_calls=5
                ),
            ],
        )

        # 步骤 2: 检查新闻信息收集是否充分
        is_task_complete: bool = yield ConditionStep(
            name="check_news_complete",
            description="评估新闻信息收集完成度，检查是否已经获取到足够的新闻资讯，包括新闻的时效性、相关性和完整性",
        )

        # 如果新闻信息不够充分，继续搜索
        if is_task_complete:
            break

    # 步骤 3: 生成新闻摘要和分析
    yield GenerateStep(
        name="generate_news_summary",
        description="整理并分析收集到的新闻信息，生成结构化的新闻摘要、关键要点和相关分析",
    ) 
