---
description: 
globs: 
alwaysApply: true
---
# 总体规则

总是使用中文回复用户，总是使用中文编写注释

## 代码风格

- 尽可能多且详尽的编写注释
- 代码编写简洁易懂，保持尽可能低的圈复杂度和认知复杂度，一个函数的长度不得超过40行

## 编码规则

- 不要着急开始编写代码，先尽可能多的查阅文档和上网搜索，然后一步一步地思考，权衡利弊，再开始编写代码
- 遇到疑问或有多种可能的解决方案时，不要编写代码，先问清楚用户想要什么
- 引入新功能或新依赖前，务必先征求用户的同意，未经允许不得进行大规模变更。如果没有征求到用户的同意，不得创建新文件或去用户没有提及的文件进行修改
- 每次重构/修改完成后，仔细检查是否对代码库里的其他代码构成影响，如果有，做相应调整
- README.md 描述了整个项目的架构，供你参考
- 在你给出方案时，应当具体而充实，但方案中不要包含任何具体代码，详细描述逻辑功能以及文件名和函数名即可
