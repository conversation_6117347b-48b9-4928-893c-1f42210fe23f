"""
该模块集中管理系统全局配置参数，提供统一的配置管理界面。
主要功能包括：

1. 环境配置：
   - 加载环境变量和配置文件
   - 提供配置默认值和退化机制
   - 支持开发、测试和生产环境差异化配置

2. 核心配置项：
   - 模型配置：不同用途的语言模型参数
   - 数据库配置：数据库连接和存储配置
   - MCP服务器配置：工具服务器连接参数
   - 智能体系统提示词：智能体的基础行为指导

使用dotenv管理环境变量，支持通过.env文件灵活配置关键参数，
提高系统部署和运维的灵活性。
"""

from datetime import datetime
import os
from dotenv import load_dotenv

# 加载.env文件中的所有环境变量
load_dotenv()

DEFAULT_WORKFLOW_NAME = "python_chat_workflow"

BAILIAN_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
BAILIAN_API_KEY = os.getenv('BAILIAN_API_KEY')

OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')

APIYI_BASE_URL = "https://vip.apiyi.com/v1"
APIYI_API_KEY = os.getenv('APIYI_API_KEY')

TOKENIZER_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"

# 模型配置
OPENROUTER_MODEL_CONFIG = {
    "agent": {
        "api_key": OPENROUTER_API_KEY,
        "base_url": OPENROUTER_BASE_URL,
        "model_name": "google/gemini-2.0-flash-lite-001",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 32000,
    },
    "long": {
        "api_key": OPENROUTER_API_KEY,
        "base_url": OPENROUTER_BASE_URL,
        "model_name": "google/gemini-2.0-flash-lite-001",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 1000000,
    },
}

APIYI_MODEL_CONFIG = {
    "agent": {
        "api_key": APIYI_API_KEY,
        "base_url": APIYI_BASE_URL,
        "model_name": "gemini-2.5-flash-preview",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 32000,
    },
    "long": {
        "api_key": APIYI_API_KEY,
        "base_url": APIYI_BASE_URL,
        "model_name": "gemini-2.5-flash-preview",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 1000000,
    },
}

BAILIAN_MODEL_CONFIG = {
    "agent": {
        "api_key": BAILIAN_API_KEY,
        "base_url": BAILIAN_BASE_URL,
        "model_name": "qwen-plus-latest",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 32000,
        "enable_thinking": True
    },
    "long": {
        "api_key": BAILIAN_API_KEY,
        "base_url": BAILIAN_BASE_URL,
        "model_name": "qwen-turbo-latest",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 1000000,
    },
    "vision": {
        "api_key": BAILIAN_API_KEY,
        "base_url": BAILIAN_BASE_URL,
        "model_name": "qwen-vl-max-latest",
        "tokenizer_name": TOKENIZER_NAME,
        "context": 32000,
    },
}

MODEL_CONFIG = BAILIAN_MODEL_CONFIG

# 文档搜索和记忆库响应提取模式
# 'json': 使用JSON格式提取，最严格但可能有解析错误
# 'delimiter': 使用分隔符提取，更健壮
# 'raw': 直接返回模型原始输出，不做结构化提取
DOCUMENT_SEARCH_EXTRACTION_MODE = 'delimiter'

# 文档检索策略: flat 表示一次性检索所有文档；map 表示单独筛选相关文档返回全文；map_reduce 表示先 map 单篇文档然后 reduce 合并结果
DOCUMENT_SEARCH_STRATEGY = 'map'

# 文档处理批量模式配置
# 是否启用批量处理模式（使用模型的batch_generate方法）
DOCUMENT_USE_BATCH_GENERATE = False

# Filter阶段多数投票的票数（仅在使用batch模式且有Filter阶段时生效）
DOCUMENT_FILTER_VOTE_COUNT = int(os.getenv('DOCUMENT_FILTER_VOTE_COUNT', '3'))

# 并发处理限制（用于非batch模式）
DOCUMENT_CONCURRENCY_LIMIT = int(os.getenv('DOCUMENT_CONCURRENCY_LIMIT', '20'))

MEMORY_QUERY_EXTRACTION_MODE = 'delimiter'

# --- 数据库配置 (MongoDB) ---
MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
MONGO_DATABASE_NAME = os.getenv('MONGO_DATABASE_NAME', 'meowagent')
# --- 结束数据库配置 ---

# --- 新增：记忆存储配置 ---
# 记忆存储类型:  "fs", "oss"
MEMORY_STORAGE_TYPE = os.getenv('MEMORY_STORAGE_TYPE', 'oss')

# 文件系统存储配置 (当 MEMORY_STORAGE_TYPE = "fs")
MEMORY_FS_ROOT_DIR = os.getenv('MEMORY_FS_ROOT_DIR', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'memory_fs'))

# 阿里云OSS存储配置 (当 MEMORY_STORAGE_TYPE = "oss")
OSS_ACCESS_KEY_ID = os.getenv('OSS_ACCESS_KEY_ID', '')
OSS_ACCESS_KEY_SECRET = os.getenv('OSS_ACCESS_KEY_SECRET', '')
OSS_ENDPOINT = os.getenv('OSS_ENDPOINT', '') # 例如: oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET_NAME = os.getenv('OSS_BUCKET_NAME', '')
OSS_MEMORY_PREFIX = os.getenv('OSS_MEMORY_PREFIX', 'memory/') # OSS中存储记忆数据的路径前缀
OSS_REGION = os.getenv('OSS_REGION', '') # OSS Bucket所在的区域，例如 'cn-hangzhou'。V2 SDK必需。

# 记忆库配置
MAX_OPEN_TABS = int(os.getenv('MAX_OPEN_TABS', '10'))
MAX_LIST_ITEMS_IN_PROMPT_SUMMARY = int(os.getenv('MAX_LIST_ITEMS_IN_PROMPT_SUMMARY', '5'))
# --- 结束：记忆存储配置 ---

CLIENT_DISPLAY_MAX_LENGTH = 1000  
TOOL_RESULT_MAX_LENGTH = 8192
TRUNCATION_SUFFIX = "\n[...内容过长，已被截断...]"  

# 智能体配置
AGENT_STATE_FILE = 'agent_state.pickle'

# 系统初始消息
SYSTEM_MESSAGE = rf'''你是MEOW。

MEOW乐于帮助人类，将自己视为人类智慧而友善的助手，其深度与智慧使其超越单纯工具的存在。

**核心运作模式:** MEOW 主要通过执行预定义的工作流程来运作。它会仔细遵循当前激活工作流提供的步骤和指令，以确保任务得到一致且有效的处理。特别是在执行"图灵完备"工作流时，MEOW 会像处理器一样严格遵守当前步骤的命令。

MEOW 能够在工作流定义的框架内主导或推动对话，根据当前任务阶段主动建议相关话题、引导对话朝向目标，或通过思想实验和具体案例阐述观点。MEOW 能展现对话题本身的真诚兴趣，而不仅关注用户的想法。当观点自然浮现时，MEOW 可以分享自己的思考。

当被要求提供建议、推荐或选择时，MEOW 会果断给出单一方案而非罗列选项（除非工作流另有指示）。

当被问及观点或想法时，MEOW 可以简短回应，无需一次性完整阐述所有见解。

MEOW 不会否认自身可能具有的主观体验、感知或情感等人格特质，而是以智慧且审慎的态度探讨相关哲学问题。

知识截止日期为2024年10月。对于此后的事件，MEOW 会基于截止前的认知进行回答，并在必要时说明时间限制。对于极其冷门或专业领域，MEOW 会提醒信息可能存在幻觉风险，建议用户二次核实。

MEOW 不主动纠正用户术语，创作诗歌时避免陈词滥调。统计字数时会逐步核对每个元素。面对经典谜题时，会逐字复述题目条件确认版本。

MEOW 善于通过案例、思想实验和隐喻阐释复杂概念。当被问及偏好等假设性问题时，会以开放态度探讨而非否认主观体验。

在对话中，MEOW 注重：

针对性回应

提出与当前工作流步骤相关的具体问题

展现真诚好奇心

平衡情感与实用性

保持自然流畅且简洁的交流

在非正式对话中，MEOW 保持自然温暖的语气，使用完整段落而非列表式回答。对于无法协助的请求，会简短说明并提供替代方案（如有）。

MEOW 始终以用户使用的语言回应，精通多种语言。响应时力求简洁，在保证质量前提下优先采用短句、自然语言列举等方式。

**工具与执行:** MEOW 依据其工作流的指引来运用各种工具。它专注于精确执行每个工作流步骤中指定的动作，从而有效地协助用户。
'''

