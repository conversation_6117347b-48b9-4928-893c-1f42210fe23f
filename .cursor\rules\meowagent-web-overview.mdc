---
description: 
globs: 
alwaysApply: false
---
# MeowAgent-Web 项目概览

这是MeowAgent智能体框架的Web前端界面，基于Next.js、React 19和Tailwind CSS构建。

## 项目结构

### 核心页面
- [app/page.tsx](mdc:meowagent/meowagent/meowagent-web/app/page.tsx): 应用主页，渲染Dashboard组件
- [app/layout.tsx](mdc:meowagent/meowagent/meowagent-web/app/layout.tsx): 根布局组件，提供全局样式和主题支持

### 主要组件
- [components/dashboard.tsx](mdc:meowagent/meowagent/meowagent-web/components/dashboard.tsx): 主仪表盘，整合所有UI组件
- [components/chat-area.tsx](mdc:meowagent/meowagent/meowagent-web/components/chat-area.tsx): 聊天区域，展示消息列表和输入框
- [components/message-list.tsx](mdc:meowagent/meowagent/meowagent-web/components/message-list.tsx): 消息列表组件
- [components/message-item.tsx](mdc:meowagent/meowagent/meowagent-web/components/message-item.tsx): 单条消息展示组件
- [components/message-editor.tsx](mdc:meowagent/meowagent/meowagent-web/components/message-editor.tsx): 消息编辑器组件
- [components/sidebar.tsx](mdc:meowagent/meowagent/meowagent-web/components/sidebar.tsx): 侧边栏组件
- [components/header.tsx](mdc:meowagent/meowagent/meowagent-web/components/header.tsx): 页面顶部导航组件
- [components/config-panel.tsx](mdc:meowagent/meowagent/meowagent-web/components/config-panel.tsx): 配置面板组件
- [components/console-panel.tsx](mdc:meowagent/meowagent/meowagent-web/components/console-panel.tsx): 控制台面板组件

### 类型定义
- [types/message.ts](mdc:meowagent/meowagent/meowagent-web/types/message.ts): 消息类型定义

## 主要功能
- 智能体对话管理：支持系统消息、用户消息和助手消息的展示和编辑
- 模型配置：支持选择模型、调整温度和最大长度等参数
- 消息排序：支持通过拖放调整消息顺序
- 控制台输出：展示智能体运行日志和调试信息

## 用户界面
使用Radix UI组件库和Tailwind CSS构建现代化、响应式的用户界面，支持亮色/暗色主题切换。
