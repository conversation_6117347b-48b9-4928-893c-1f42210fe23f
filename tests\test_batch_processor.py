import pytest
import pytest_asyncio
import asyncio
from typing import Dict, List, Any, Optional
import uuid

# 导入需要测试的组件
from utils.batch_processor import AsyncBatchProcessor
from message import UserMessage, SystemMessage, Message
# 导入真实的会话和模型类
from conversation import Conversation
import config

# 标记所有测试为异步测试
pytestmark = pytest.mark.asyncio

model_name = "long"

# --- 测试用例 ---


async def test_process_batch_map_only():
    processor = AsyncBatchProcessor()
    
    # 准备测试数据
    items = {
        "item1": "值1",
        "item2": "值2",
        "item3": "值3"
    }
    
    # 执行批处理 (只有 Map 阶段)
    results = await processor.process_batch(
        items, model_name,
        map_instruction="将每个值转换为大写"
    )
    
    # 验证结果
    assert isinstance(results, dict)
    assert len(results) == 3  # 应该有三个处理结果
    assert all(key in results for key in items.keys())  # 所有键应该都存在

async def test_process_batch_filter_map():
    processor = AsyncBatchProcessor()
    
    # 准备测试数据
    items = {
        "item1": "包含关键词的值1",
        "item2": "不相关的值2",
        "item3": "包含关键词的值3",
        "item4": "其他内容"
    }
    
    # 执行批处理 (Filter + Map)
    results = await processor.process_batch(
        items, model_name,
        map_instruction="描述这个项目",
        filter_instruction="只保留包含'关键词'的项目"
    )
    
    # 验证结果 - 只能验证结果是dict，具体过滤效果取决于实际LLM
    assert isinstance(results, dict)
    assert len(results) == 2

async def test_process_batch_map_reduce():
    processor = AsyncBatchProcessor()
    
    # 准备测试数据
    items = {
        "item1": "值1",
        "item2": "值2",
        "item3": "值3"
    }
    
    # 执行批处理 (Map + Reduce)
    results = await processor.process_batch(
        items, model_name,
        map_instruction="简单描述这个项目",
        reduce_instruction="总结所有项目的描述"
    )
    
    # 验证结果
    assert isinstance(results, str)  # 默认reduce处理器返回字符串

async def test_process_batch_filter_map_reduce():
    processor = AsyncBatchProcessor()
    
    # 准备测试数据
    items = {
        "item1": "包含关键词的值1",
        "item2": "不相关的值2",
        "item3": "包含关键词的值3",
        "item4": "其他内容"
    }
    
    # 执行批处理 (完整三阶段)
    results = await processor.process_batch(
        items, model_name,
        map_instruction="描述这个项目",
        filter_instruction="只保留包含'关键词'的项目",
        reduce_instruction="总结所有保留项目的描述"
    )
    
    # 验证结果
    assert isinstance(results, str)  # 最终结果应该是减缩后的字符串

async def test_process_batch_empty_items():
    processor = AsyncBatchProcessor()
    
    # 准备空测试数据
    items = {}
    
    # 执行批处理
    results = await processor.process_batch(
        items, model_name,
        map_instruction="处理项目"
    )
    
    # 验证结果 - Map 结果应该为空
    assert isinstance(results, dict)
    assert len(results) == 0

async def test_process_batch_all_filtered():
    processor = AsyncBatchProcessor()
    
    # 准备测试数据
    items = {
        "item1": "值1",
        "item2": "值2"
    }
    
    # 执行批处理 (所有项目都会被过滤)
    results = await processor.process_batch(
        items, model_name,
        map_instruction="处理项目",
        filter_instruction="只保留包含'不可能存在的词'的项目",
    )
    
    # 验证结果 - 应该是空字典
    assert isinstance(results, dict)
    assert len(results) == 0

# --- 测试简易用法 (通过自然语言指令) ---
async def test_process_batch_with_map_instruction_only():
    processor = AsyncBatchProcessor()
    items = {"a": "apple", "b": "banana"}
    map_instruction = "将水果名称转换为大写"
    results = await processor.process_batch(items, model_name, map_instruction=map_instruction)
    assert len(results) == 2
    assert isinstance(results["a"], str)
    assert isinstance(results["b"], str)
    assert results["a"] == "APPLE"
    assert results["b"] == "BANANA"

async def test_process_batch_with_filter_instruction():
    processor = AsyncBatchProcessor()
    items = {"a": "apple pie", "b": "banana split", "c": "cherry tart"}
    filter_instruction = "只保留包含 'pie' 的项目"
    map_instruction = "只保留水果名"
    results = await processor.process_batch(
        items, model_name, 
        map_instruction=map_instruction, 
        filter_instruction=filter_instruction
    )
    assert isinstance(results, dict)
    # 如果LLM正确工作并且格式正确，被过滤后应该少于等于原始数量
    assert len(results) == 1
    # 如果一个项通过了filter，它的map结果应该是字符串
    assert isinstance(list(results.values())[0], str)
    assert results["a"] == "apple"

async def test_process_batch_with_reduce_instruction():
    processor = AsyncBatchProcessor()
    items = {"a": "apple", "b": "banana"}
    map_instruction = "获取项目的值"
    reduce_instruction = "将所有项目的值合并成一个列表"
    results = await processor.process_batch(
        items, model_name, 
        map_instruction=map_instruction, 
        reduce_instruction=reduce_instruction
    )
    # 默认的reduce_response_processor返回原始LLM响应字符串
    assert isinstance(results, str)

async def test_process_batch_full_instructions():
    processor = AsyncBatchProcessor()
    items = {"a": "apple pie", "b": "banana split", "c": "cherry tart"}
    filter_instruction = "找出所有派 (pie)"
    map_instruction = "描述这个派"
    reduce_instruction = "总结所有派的描述"
    results = await processor.process_batch(
        items, model_name,
        filter_instruction=filter_instruction,
        map_instruction=map_instruction,
        reduce_instruction=reduce_instruction
    )
    assert isinstance(results, str) # 最终结果是reduce的LLM响应

# --- 测试边界情况 ---
async def test_process_batch_empty_items_with_instruction():
    processor = AsyncBatchProcessor()
    items = {}
    results = await processor.process_batch(items, model_name, map_instruction="Should not run")
    assert len(results) == 0

async def test_process_batch_all_filtered_with_instruction():
    processor = AsyncBatchProcessor()
    items = {"a": "X", "b": "Y"} 
    # 假设LLM对于这个filter指令都会返回"否"
    results = await processor.process_batch(
        items, model_name, 
        filter_instruction="No item should pass this filter",
        map_instruction="This map should not run for any item"
    )
    # 理论上应该为空，但取决于实际LLM响应
    assert isinstance(results, dict) 