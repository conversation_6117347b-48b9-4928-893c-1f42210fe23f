"""
MeowAgent 命令行界面 (CLI) 模块。

该模块提供了一个交互式的命令行界面，用户可以通过此界面与 MeowAgent 智能体进行实时对话和控制。
主要功能包括：

1.  **交互式会话管理 (`run_interactive_session`)**:
    *   启动和管理与 AgentManager 的连接，为每个用户会话创建和管理 Agent 实例。
    *   支持通过命令行参数指定初始对话ID (`--user`) 或自动生成新的对话ID。
    *   允许用户在不同对话之间切换 ("切换对话" 命令)。
    *   处理用户输入，将其发送到对应的 Agent 进行处理。
    *   优雅处理退出命令 ("退出", "exit", "quit", "q") 和 `KeyboardInterrupt` (Ctrl+C)。

2.  **消息处理与显示 (`CLIMessageHandler`)**:
    *   负责接收来自 Agent 的各类消息（如助手回复、工具调用、推理过程、错误信息等）。
    *   使用 `colorama` 库对不同类型的消息进行彩色高亮，增强可读性。
    *   支持流式输出助手回复和推理过程，提供即时反馈。
    *   清晰展示工具调用的名称、参数及执行结果。

3.  **异步通信 (`process_message_connection`, `send_user_input`)**:
    *   基于 `asyncio` 实现完全异步的操作，确保界面响应的流畅性。
    *   使用 `ConnectionPair` (通常是基于队列的 `QueueConnection`) 在 CLI 和 Agent 之间进行双向消息传递。
    *   独立任务 (`process_message_connection`) 持续监听并处理来自 Agent 的消息。
    *   通过 `asyncio.Event` (`prompt_ready`) 控制用户输入提示的显示时机，避免与消息输出冲突。

4.  **Agent 交互**:
    *   通过 `AgentManager` 获取或创建 Agent 实例。
    *   向 Agent 发送 "user_ready" 消息以启动初始工作流程。
    *   向 Agent 发送 "user_input" 消息传递用户文本。
    *   在用户退出或切换对话时，向 Agent 发送 "user_disconnect" 消息。

5.  **命令行参数解析 (`main`)**:
    *   使用 `argparse` 解析启动参数，如是否启用流式输出 (`--stream`) 和指定用户ID (`--user`)。

该 CLI 工具旨在为开发者和用户提供一个便捷的方式来测试、调试和使用 MeowAgent 框架的核心功能。
"""

import asyncio
import json
import traceback
import colorama
from colorama import Fore, Style
from log import logger
from agent_manager import AgentManager
from agent import Agent # For type hinting
from typing import Dict, Any, Optional
from connection import Connection, ConnectionPair, BaseConnection # Import BaseConnection
from datetime import datetime # 导入 datetime
import uuid
from conversation import Conversation
from message import SystemMessage, AgentSystemMessage, Message # Import Message
import config

# 初始化 colorama
colorama.init(autoreset=True)


# 实现一个CLI界面的消息处理器
class CLIMessageHandler:
    """CLI界面的消息处理器，处理从Agent接收到的消息"""
    
    def __init__(self):
        """初始化CLI消息处理器"""
        self.current_stream_content = ""  # 用于跟踪当前流式内容
        self.current_reasoning_content = ""  # 用于跟踪当前推理内容
        self.current_tool_calls: Dict[str, Dict[str, Any]] = {}  # 用于跟踪工具调用, type hint
        self.is_streaming = False  # 标志当前是否处于流式输出
        self.is_reasoning_streaming = False  # 标志当前是否正在显示推理内容
        self.should_auto_exit = False  # 标志是否应该自动退出
    
    def assistant(self, message_content: str): # type hint
        """
        输出助手回复（绿色）
        """
        print(f"\n{Fore.GREEN}助手: {message_content}{Style.RESET_ALL}")
    
    def assistant_delta_start(self):
        """
        开始流式输出（绿色）
        """
        if self.is_streaming:
            print(Style.RESET_ALL) # 如果有上一行，结束它
        
        self.is_streaming = True
        self.is_reasoning_streaming = False # 重置推理流标志
        self.current_stream_content = ""
        self.current_reasoning_content = "" # 重置推理内容缓冲区
        self.current_tool_calls = {} # 重置工具调用缓冲区
        print(f"\n{Fore.GREEN}助手: ", end="", flush=True)
    
    def assistant_delta(self, content: str): # type hint
        """
        流式输出助手回复增量（绿色）
        """
        if self.is_streaming:
            if self.is_reasoning_streaming:
                # 如果之前在流式显示推理内容，添加新行并切回助手提示
                print(f"\n{Fore.GREEN}助手: {self.current_stream_content}", end="", flush=True)
                self.is_reasoning_streaming = False
            
            self.current_stream_content += content
            print(f"{Fore.GREEN}{content}{Style.RESET_ALL}", end="", flush=True)
            print(f"{Fore.GREEN}", end="", flush=True) # 保持后续输出为绿色
    
    def reasoning_delta(self, content: str): # type hint
        """
        流式输出推理增量（蓝色）
        """
        if self.is_streaming: # 推理只发生在流式输出期间
            if not self.is_reasoning_streaming and not self.current_reasoning_content:
                print(f"\n{Fore.BLUE}推理过程: ", end="", flush=True)
                self.is_reasoning_streaming = True
            
            self.current_reasoning_content += content
            if self.is_reasoning_streaming: # 双重检查，应该为真
                print(f"{Fore.BLUE}{content}{Style.RESET_ALL}", end="", flush=True)
                print(f"{Fore.BLUE}", end="", flush=True) # 保持后续输出为蓝色
    
    def assistant_delta_done(self):
        """
        结束流式输出
        """
        if self.is_streaming:
            print(Style.RESET_ALL)  # 重置样式
            
            # 如果我们正在流式显示推理内容并且也有助手内容，如果需要，再次打印助手内容
            # 这种情况有点复杂：如果推理是*最后*流式显示的内容，那么这样做是好的。
            # 如果内容增量是最后的，那也没问题。
            # 如果先流式显示推理，然后内容，然后结束，那么没问题。
            # 如果先流式显示内容，然后推理，然后结束，那么没问题。
            # 主要问题是确保提示正确重置。
            # 使用is_reasoning_streaming的当前逻辑应该处理大多数情况，方法是在assistant_delta中重置它。
            
            self.is_streaming = False
            self.is_reasoning_streaming = False
    
    def tool_call_display(self, tool_name: str, args: Any, tool_id: Optional[str]=None): # merged display
        """
        输出工具调用信息（黄色）
        """
        id_str = f" (ID: {tool_id})" if tool_id else ""
        print(f"\n{Fore.YELLOW}[调用工具 {tool_name}{id_str}，参数：{args}]{Style.RESET_ALL}")
    
    def tool_call_start(self, tool_id: str): # type hint
        """
        开始流式输出工具调用（黄色） - 主要是初始化缓存
        """
        if self.is_streaming: # 工具调用发生在助手流中
            self.current_tool_calls[tool_id] = {"id": tool_id, "name": "", "arguments": ""} # 将args初始化为字符串
            # 不立即打印，等待tool_call_delta或完整的tool_call消息
    
    def tool_call_delta(self, tool_id: str, name: Optional[str]=None, arguments_delta: Optional[str]=None): # type hint
        """
        流式更新工具调用信息（黄色） - 更新缓存，不立即打印完整信息
        """
        if self.is_streaming and tool_id in self.current_tool_calls:
            if name:
                self.current_tool_calls[tool_id]["name"] = name
            if arguments_delta: # 假设参数作为字符串增量传递
                # 参数是作为部分JSON字符串流式传输的，所以进行追加
                current_args = self.current_tool_calls[tool_id].get("arguments", "")
                self.current_tool_calls[tool_id]["arguments"] = current_args + arguments_delta
                
            # 工具调用的实际显示可能在完整的'tool_call'消息或该ID的工具调用流完成时处理更好。
            # 暂时，增量只更新缓冲区。
            # 要在CLI中显示流式工具调用:
            # temp_name = self.current_tool_calls[tool_id]["name"]
            # temp_args_str = self.current_tool_calls[tool_id]["arguments"]
            # print(f"\r{Fore.YELLOW}[工具 {temp_name} (ID: {tool_id}) 参数: {temp_args_str}...]{Style.RESET_ALL}", end="")
            # 这将需要更复杂的行清除/更新。
    
    def tool_result(self, tool_name: str, result: Any, tool_id: Optional[str]=None): # type hint
        """
        输出工具结果信息（品红色）
        """
        id_str = f" (ID: {tool_id})" if tool_id else ""
        # 确保结果是字符串以便打印
        result_str = result if isinstance(result, str) else json.dumps(result, ensure_ascii=False)
        print(f"{Fore.MAGENTA}[工具 {tool_name}{id_str} 返回结果：{result_str}]{Style.RESET_ALL}")
    
    def handle_message(self, message_dict: Dict[str, Any]): # Renamed and type hint
        """处理从Agent接收到的消息字典"""
        msg_type = message_dict.get("type", "")
        content = message_dict.get("content", "") # 对于某些类型，内容可能很复杂
        # conversation_id = message_dict.get("conversation_id", "system") # 在处理程序中未使用
        
        if msg_type == "assistant":
            self.assistant(str(content)) # 确保内容是字符串
        elif msg_type == "assistant_delta_start":
            self.assistant_delta_start()
        elif msg_type == "assistant_delta":
            self.assistant_delta(str(content))
        elif msg_type == "reasoning_delta":
            self.reasoning_delta(str(content))
        elif msg_type == "assistant_delta_done":
            self.assistant_delta_done()
        elif msg_type == "tool_call_start": # 用于流式工具调用
            if isinstance(content, dict) and "id" in content:
                self.tool_call_start(content["id"])
        elif msg_type == "tool_call_delta": # 用于流式工具调用
            if isinstance(content, dict) and "id" in content:
                self.tool_call_delta(
                    content["id"],
                    content.get("name"),
                    content.get("arguments") # 这是arguments_delta
                )
        elif msg_type == "tool_call": # 完整的工具调用信息（非流式或流的结束）
            if isinstance(content, dict):
                tool_name = content.get("name", "unknown_tool")
                args = content.get("args", {})
                tool_id = content.get("id")
                self.tool_call_display(tool_name, args, tool_id)
        elif msg_type == "tool_result":
            if isinstance(content, dict):
                tool_name = content.get("name", "unknown_tool")
                result = content.get("result", "")
                tool_id = content.get("id")
                self.tool_result(tool_name, result, tool_id)
        elif msg_type == "reasoning": # 完整的推理块
            print(f"\n{Fore.BLUE}推理过程: {str(content)}{Style.RESET_ALL}")
        elif msg_type == "full_message_object":
            # 这是完整的Message对象序列化后的结果。如果处理了增量，可能不需要再次打印
            # 但对于调试或非流式模式，可能有用。
            # 对于CLI，通常优先处理增量。
            if isinstance(content, dict):
                # msg_obj = Message.from_dict(content) # 如果需要重新加载
                logger.debug(f"[CLI] 收到 full_message_object: {content.get('object')} ID: {content.get('id')}")
            elif isinstance(content, str):
                 logger.debug(f"[CLI] 收到 full_message_object (str): {content}")
            else:
                 logger.debug(f"[CLI] 收到 full_message_object (未知类型): {type(content)}")
            pass # 如果增量是主要的，避免重复打印
        elif msg_type == "workflow_completed":
            # 处理工作流完成消息
            if isinstance(content, dict):
                workflow_name = content.get("workflow_name", "未知工作流")
                auto_exit = content.get("auto_exit", False)
                message = content.get("message", "工作流已完成")
                
                print(f"\n{Fore.GREEN}✓ {message}{Style.RESET_ALL}")
                
                if auto_exit:
                    print(f"{Fore.YELLOW}工作流已完成，程序将自动退出...{Style.RESET_ALL}")
                    self.should_auto_exit = True
                else:
                    print(f"{Fore.CYAN}工作流已完成，您可以继续与助手对话。{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.GREEN}✓ 工作流已完成{Style.RESET_ALL}")
        elif msg_type == "error":
            logger.error(str(content))
        elif msg_type == "info":
            logger.info(str(content))
        elif msg_type == "warning":
            logger.warning(str(content))
        else:
            logger.info(f"[{msg_type}] {str(content)}")


# 在单独的任务中处理消息队列
async def process_message_connection(connection: BaseConnection, handler: CLIMessageHandler, prompt_ready_event: asyncio.Event, exit_event: asyncio.Event):
    """
    在单独的任务中处理消息连接。
    
    参数:
        connection: 连接对象 (遵循 BaseConnection protocol)
        handler: 消息处理器
        prompt_ready_event: 用于同步CLI提示符的 asyncio.Event
        exit_event: 用于通知主循环退出的 asyncio.Event
    """
    while True:
        try:
            message_dict = await connection.recv()
            prompt_ready_event.clear() # 处理前清除
            handler.handle_message(message_dict)
            
            # 检查是否需要自动退出
            if handler.should_auto_exit:
                logger.info("检测到自动退出信号，设置退出事件")
                exit_event.set()
                prompt_ready_event.set() # 确保主循环不被阻塞
                break
                
            prompt_ready_event.set() # 处理后设置
        except asyncio.CancelledError:
            logger.info("消息处理任务已取消")
            prompt_ready_event.set() # 退出时确保提示可用
            break
        except RuntimeError as e:
            if "Connection closed" in str(e) or "连接已关闭" in str(e):
                logger.info("连接已关闭，消息处理任务结束")
            else:
                logger.error(f"处理消息时发生运行时错误: {e}", show_traceback=True)
            prompt_ready_event.set()
            break
        except Exception as e:
            logger.error(f"处理消息时发生未知错误: {e}", show_traceback=True)
            prompt_ready_event.set() # 确保提示未被阻塞
            # 根据其他错误的期望行为选择 continue 或 break


async def send_user_input(connection: Connection, conversation_id: str, text: str, prompt_ready: Optional[asyncio.Event] = None, stream_mode: bool = False):
    """
    发送用户输入到连接
    
    参数:
        connection: 连接
        conversation_id: 对话ID
        text: 用户输入文本
        prompt_ready: 提示符就绪事件，如果提供则会在发送消息时清除
        stream_mode: 是否启用流式模式
    """
    if prompt_ready:
        prompt_ready.clear()
        
    await connection.send({
        "type": "user_input",
        "conversation_id": conversation_id,
        "content": text,
        "stream_mode": stream_mode
    })


async def run_interactive_session(agent_manager: AgentManager, stream_mode=False, initial_conversation_id: Optional[str]=None, initial_workflow: Optional[str]=None):
    """
    运行交互式会话
    
    参数:
        agent_manager: 智能体管理器实例
        stream_mode: 是否启用流式模式
        initial_conversation_id: 初始对话ID，如果为None则会提示输入或生成随机ID
        initial_workflow: 要使用的工作流名称，如果为None则使用默认工作流
    """
    connection_pair = ConnectionPair() # 默认使用QueueConnection
    # 对于CLI，QueueConnection通常足够且更简单。
    # client_conn, agent_conn = await connection_pair.get_connection_async() # 如果使用基于WebSocket的配对
    client_conn, agent_conn = connection_pair.get_connection() # 对于QueueConnection
    
    cli_handler = CLIMessageHandler()
    prompt_ready = asyncio.Event()
    exit_event = asyncio.Event()  # 新增：用于自动退出的事件
    prompt_ready.set() 
    
    message_task = asyncio.create_task(
        process_message_connection(client_conn, cli_handler, prompt_ready, exit_event)
    )
    
    cli_handler.handle_message({
        "type": "info",
        "content": "欢迎使用MeowAgent! 输入 \"退出\" 结束对话。",
        "conversation_id": "system"
    })
    
    if stream_mode:
        cli_handler.handle_message({
            "type": "info",
            "content": "已启用流式输出模式",
            "conversation_id": "system"
        })

    current_conversation_id = initial_conversation_id
    if not current_conversation_id:
        current_conversation_id = str(uuid.uuid4())[:8]
        cli_handler.handle_message({
            "type": "info",
            "content": f"已为您生成随机对话ID: {current_conversation_id}",
            "conversation_id": "system"
        })
    
    current_agent: Optional[Agent] = None
    
    try:
        conversation = Conversation(current_conversation_id)
        # 初始系统消息现在由 Agent._prepare_messages_for_model 在需要时处理
        # 无需在此处直接添加系统消息到对话历史中
        
        # 构造agent配置，包括指定的工作流
        agent_config = {}
        if initial_workflow:
            agent_config['default_workflow_name'] = initial_workflow
            cli_handler.handle_message({
                "type": "info",
                "content": f"将使用指定的工作流: {initial_workflow}",
                "conversation_id": "system"
            })
        
        # AgentManager.create_agent 是异步的
        current_agent = await agent_manager.create_agent(current_conversation_id, conversation, agent_conn, agent_config)
        
        cli_handler.handle_message({
            "type": "info",
            "content": f"已加载/创建对话 {current_conversation_id}",
            "conversation_id": current_conversation_id
        })
        cli_handler.handle_message({
            "type": "info",
            "content": "输入 \"切换对话\" 可以切换到其他对话",
            "conversation_id": current_conversation_id
        })
        
        # 发送用户就绪消息，启动工作流直到第一个用户输入点
        await current_agent.handle_incoming_message({
            "type": "user_ready",
            "conversation_id": current_conversation_id,
            "stream_mode": stream_mode
        })
        
        while True:
            # 检查是否需要自动退出
            if exit_event.is_set():
                cli_handler.handle_message({
                    "type": "info",
                    "content": "检测到工作流自动退出信号，正在退出...",
                    "conversation_id": current_conversation_id
                })
                # 等待一下让用户看到消息
                await asyncio.sleep(1)
                break
                
            await prompt_ready.wait()
            await asyncio.sleep(0.1) # 小延迟，防止提示与之前的输出冲突
            
            # 再次检查退出事件，因为在等待期间可能被设置
            if exit_event.is_set():
                cli_handler.handle_message({
                    "type": "info",
                    "content": "检测到工作流自动退出信号，正在退出...",
                    "conversation_id": current_conversation_id
                })
                await asyncio.sleep(1)
                break
            
            prompt_text = f'[{current_conversation_id}]> '
            try:
                # 使用 asyncio.wait_for 实现超时机制，这样可以定期检查退出事件
                user_input = await asyncio.wait_for(
                    asyncio.to_thread(input, prompt_text), 
                    timeout=0.5
                )
            except asyncio.TimeoutError:
                # 输入超时，继续循环检查退出事件
                continue
            except RuntimeError as e:
                if "cannot schedule new futures after shutdown" in str(e):
                    logger.warning("输入被中断（可能在程序退出时）。")
                    break
                raise
            
            if not user_input.strip():
                continue
                
            if user_input.lower() in ('退出', 'exit', 'quit', "q"):
                cli_handler.handle_message({
                    "type": "info",
                    "content": "再见!",
                    "conversation_id": current_conversation_id
                })
                if current_agent:
                    # Agent.handle_incoming_message 是异步的
                    await current_agent.handle_incoming_message({
                        "type": "user_disconnect", 
                        "conversation_id": current_conversation_id, 
                        "content": "用户主动退出"
                    })
                break
            
            if user_input.lower() in ('切换对话', 'switch conversation'):
                await prompt_ready.wait()
                await asyncio.sleep(0.1)
                try:
                    new_conversation_id_input = await asyncio.wait_for(
                        asyncio.to_thread(input, '请输入新的对话ID (留空则随机生成): '),
                        timeout=30  # 给用户足够时间输入对话ID
                    )
                except asyncio.TimeoutError:
                    print("输入超时，保持当前对话")
                    continue
                except RuntimeError as e:
                    if "cannot schedule new futures after shutdown" in str(e):
                         logger.warning("切换对话输入被中断。")
                         break
                    raise
                
                if current_agent: # 通知当前agent断开连接
                    await current_agent.handle_incoming_message({
                        "type": "user_disconnect", 
                        "conversation_id": current_conversation_id,
                        "content": "用户切换对话"
                    })
                    await current_agent.close() # 关闭旧的agent

                current_conversation_id = new_conversation_id_input.strip() if new_conversation_id_input.strip() else str(uuid.uuid4())[:8]
                if not new_conversation_id_input.strip():
                     cli_handler.handle_message({
                         "type": "info",
                         "content": f"已为您生成随机对话ID: {current_conversation_id}",
                         "conversation_id": "system"
                     })
                
                conversation = Conversation(current_conversation_id) # 新的对话对象
                
                # 为新对话也使用相同的工作流配置
                agent_config = {}
                if initial_workflow:
                    agent_config['default_workflow_name'] = initial_workflow
                
                current_agent = await agent_manager.create_agent(current_conversation_id, conversation, agent_conn, agent_config)
                cli_handler.handle_message({
                    "type": "info",
                    "content": f"已切换到对话 {current_conversation_id}",
                    "conversation_id": current_conversation_id
                })
                
                # 切换对话后也发送用户就绪消息，启动工作流直到第一个用户输入点
                await current_agent.handle_incoming_message({
                    "type": "user_ready",
                    "conversation_id": current_conversation_id,
                    "stream_mode": stream_mode
                })
                continue
            
            # 发送用户输入到Agent (handle_incoming_message 是异步的)
            if current_agent:
                await current_agent.handle_incoming_message({
                    "type": "user_input",
                    "conversation_id": current_conversation_id,
                    "content": user_input,
                    "stream_mode": stream_mode
                })
                prompt_ready.clear() # 等待响应处理
            else:
                cli_handler.handle_message({
                    "type": "error",
                    "content": "错误: 当前没有活动的Agent实例。",
                    "conversation_id": current_conversation_id
                })

    except KeyboardInterrupt:
        cli_handler.handle_message({
            "type": "info",
            "content": "\n再见! (用户中断)",
            "conversation_id": current_conversation_id
        })
        if current_agent and current_conversation_id: # 检查current_agent和current_conversation_id是否不为None
            await current_agent.handle_incoming_message({
                "type": "user_disconnect", 
                "conversation_id": current_conversation_id, 
                "content": "用户中断会话 (KeyboardInterrupt)"
            })
    except Exception as e_main_loop:
        error_msg = f"交互会话中发生严重错误: {traceback.format_exc()}"
        cli_handler.handle_message({
            "type": "error",
            "content": error_msg,
            "conversation_id": current_conversation_id or "system" # 如果conversation_id为空，则使用system
        })
    
    finally:
        logger.info("CLI会话结束，正在清理...")
        message_task.cancel()
        try:
            await message_task
        except asyncio.CancelledError:
            logger.info("消息处理任务成功取消")
        
        await connection_pair.close()
        
        if current_agent: # 确保agent存在后再关闭
            logger.info(f"正在关闭Agent {current_agent.conversation_id or '[NoConvID]'}...")
            await current_agent.close()
            logger.info(f"Agent {current_agent.conversation_id or '[NoConvID]'} 已关闭")


async def main():
    """主函数"""
    # 确保agent_manager只初始化一次
    agent_manager_instance = await AgentManager.get_instance()
    try:
        import argparse
        parser = argparse.ArgumentParser(description='MeowAgent CLI')
        parser.add_argument('-s', '--stream', action='store_true', default=True, help='启用流式输出模式')
        parser.add_argument('-u', '--user', type=str, help='指定对话ID', default=None)
        parser.add_argument('-w', '--workflow', type=str, help='指定要使用的工作流名称', default=None)
        args = parser.parse_args()
        
        await run_interactive_session(agent_manager_instance, args.stream, args.user, args.workflow)
        
    except Exception as e_main:
        logger.error("主程序运行出错", show_traceback=True) # 显示堆栈跟踪
    finally:
        logger.info("正在关闭 AgentManager...")
        await agent_manager_instance.close()
        logger.info("AgentManager 已关闭")


# 运行主函数
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("CLI被用户中断 (Ctrl+C)")
    except Exception as e_global:
        # 这是一个最后的全方位捕获，用于处理asyncio.run或关闭过程中不可预见的错误
        print(f"{Fore.RED}全局错误: {e_global}{Style.RESET_ALL}")
        traceback.print_exc() 