---
description: MeowAgent 的核心是智能代理（Agent）系统，由 @agent.py 和 @agent_manager.py 两个主要模块组成。
globs: 
alwaysApply: false
---
# MeowAgent 智能代理系统

MeowAgent 的核心是智能代理（Agent）系统，由 [agent.py](mdc:meowagent/agent.py) 和 [agent_manager.py](mdc:meowagent/agent_manager.py) 两个主要模块组成。

## Agent 核心

[Agent 类](mdc:meowagent/agent.py) 是整个智能代理框架的核心，负责：

- 管理与LLM的交互，包括准备上下文、处理流式输出
- 协调各种工具库的加载和使用
- 维护对话状态和历史
- 执行工作流定义的多步骤任务

核心工作流程：

1. 接收用户输入 -> `handle_incoming_message`
2. 准备模型上下文 -> `_prepare_messages_for_model`
3. 调用LLM模型 -> `_call_model`
4. 处理模型响应：
   - 纯文本响应 -> 加入对话历史
   - 工具调用 -> `_process_multiple_tool_calls` -> `_execute_single_tool`
5. 重复上述过程，直到生成最终回复

## AgentManager 管理器

[AgentManager](mdc:meowagent/agent_manager.py) 是一个单例类，负责：

- 集中管理所有Agent实例
- 共享和池化关键资源（模型、MCP客户端等）
- 预加载工具库类定义
- 管理Agent的生命周期（创建、获取、删除）

AgentManager采用异步初始化机制和资源共享策略，大大提升了系统性能和资源利用率：

```
┌─────────────────────┐
│  AgentManager       │◄───────┐
│  (单例)             │        │
└──────────┬──────────┘        │
           │                   │
           ▼                   │
┌──────────────────────┐       │
│  共享资源            │       │
│  - 语言模型实例      │       │ 创建
│  - MCP客户端         │       │ 获取
│  - 库定义与配置      │       │ 关闭
└──────────┬───────────┘       │
           │                   │
     ┌─────┴─────┐             │
     ▼           ▼             │
┌──────────┐ ┌──────────┐      │
│ Agent 1  │ │ Agent 2  │──────┘
└──────────┘ └──────────┘
```

## 关键功能

### Agent的创建与克隆

```python
# 通过AgentManager创建新Agent
agent = await agent_manager.create_agent(conversation_id)

# 快速克隆现有Agent
new_agent = agent.clone_for_new_conversation(new_conversation_id)
```

### 工具调用执行

```python
# Agent内执行工具调用
async def _execute_single_tool(self, tool_call):
    # 解析工具调用信息
    tool_name = tool_call.function.name
    
    # 查找对应的库
    library_name = tool_name.split(".")[0]
    library = self.libraries.get(library_name)
    
    # 执行工具
    result = await library.execute_tool(tool_name, tool_call.function.arguments)
    return result
```

