"""
该模块初始化数据库系统，并提供数据库模块的入口点。
主要功能包括：

1. 模块初始化：
   - 导出 MongoDB 管理器提供者 (db_manager_provider)
   - 用户应通过 `await db_manager_provider.get_manager()` 获取管理器实例。
   
2. 子模块组织：
   - manager.py: MongoDB 连接和管理 (MongoManager)
   - schema.py: (已大幅简化) 数据结构说明，无 ORM 模型
   
通过从database模块直接导入db_manager_provider，系统各部分可以
方便地访问数据库功能而无需了解具体实现细节。
"""

# 数据库模块初始化
# 此模块包含数据库连接、模型和操作相关代码

from database.manager import db_manager_provider # 导入新的 provider

# 导出 db_manager_provider 使其可以通过 from database import db_manager_provider 方式导入
__all__ = ['db_manager_provider'] 