"""
该模块提供了强大的文档管理和智能检索功能，旨在帮助智能体理解和利用大规模文档集合中的信息。
其核心是 DocumentLibrary 类，具备以下主要能力：

1.  **自动化文档加载与管理**:
    *   在初始化时，DocumentLibrary 会自动从预设的文件夹（默认为 "documents/"）扫描并加载多种常见格式的文档文件（例如：.txt, .md, .py, .js, .json, .yaml 等）。
    *   加载的文档内容被缓存在内存中，以供快速访问。

2.  **核心检索与交互工具**:
    *   `list_documents()`: 提供一个当前所有已加载文档的名称列表。
    *   `get_document_content(document_name: str)`: 允许获取指定名称文档的完整原始内容。
    *   `search_in_documents(query: str)`: 模块的核心功能。它接收用户的自然语言查询，在所有已加载的文档中进行语义搜索，并返回一个包含答案和相关文档信息的结构化响应。

3.  **灵活的文档搜索策略**:
    `search_in_documents` 方法支持通过全局配置 `config.DOCUMENT_SEARCH_STRATEGY` 切换不同的信息检索与处理策略，以适应不同场景的需求：
    *   **`flat` 策略**:
        - 将所有文档的内容合并成一个大的上下文，然后一次性提交给一个具备长上下文处理能力的大语言模型 (LLM) 进行分析和回答。
        - 此策略下，可以通过 `config.DOCUMENT_SEARCH_EXTRACTION_MODE` 进一步控制模型输出的解析方式：
            - `json`: 指示模型以严格的JSON格式返回答案和引用的源文档列表。
            - `delimiter`: 指示模型使用预定义的分隔符（如 `[ANSWER_START]`, `[SOURCE_DOCUMENTS_START]`）来清晰地标记回答内容和引用的源文档。
            - `raw`: 直接采用模型的原始文本输出作为答案，不进行特定的结构化解析。
        - 当 `json` 或 `delimiter` 模式解析失败时，系统会自动回退到 `raw` 模式，以保证可用性。
    *   **`map_reduce` 策略** (由 `_search_in_documents_map_reduce` 方法实现):
        - *Map阶段*: 系统并发地处理每一个文档。针对每个文档，调用LLM判断其与用户查询的相关性，并提取关键摘要。
        - *Reduce阶段*: 收集所有被判断为相关的文档摘要，然后再次调用LLM，将这些摘要整合起来，生成一个全面、综合的最终回答，并附带引用的源文档列表。此策略适合处理大量文档，通过分而治之避免超出单次LLM的上下文限制。
    *   **`map` 策略** (由 `_search_in_documents_map` 方法实现):
        - 系统并发地处理每一个文档，利用LLM判断其与用户查询的相关性。
        - 直接返回所有被判断为相关的文档的完整原始内容，并附带一个简短的说明，告知用户找到了哪些相关文档。此策略适用于用户需要获取原始、未经摘要的文档内容的场景。

4.  **集成长上下文语言模型**:
    模块内部实例化并使用 `OpenAIModel("long")`，专门用于处理和理解可能非常冗长的文档内容。

5.  **异步设计**:
    所有主要的工具方法（如 `get_document_content`, `list_documents`, `search_in_documents`）都设计为异步函数 (`async def`)，使其能够高效地集成到基于异步I/O的应用程序中（例如 FastAPI）。

该模块旨在通过上述功能，赋予智能体深度理解和利用大量文档知识的能力，从而在问答、信息提取、内容总结等多种任务中提供更准确、更全面的支持。
"""

import os
import traceback
import json
import re  # 导入正则表达式模块
from typing import Dict, List, Optional, Annotated, Union, Any

import json5
from libraries.library import Library, register_tool
from model import OpenAIModel
import config
from log import logger
from utils.batch_processor import AsyncBatchProcessor  # 导入批处理工具

class DocumentLibrary(Library):
    """
    文档处理工具库，支持从指定文件夹加载文档并提供整体内容给模型处理
    """
    # 定义库的提示词
    library_prompt = """**文档处理库**

这个工具库用于管理和访问文档。系统启动时已自动加载了文档文件夹中的所有文档。

**可用工具:**
- `get_document_content(document_name: str)`: 获取指定文档的全部内容。
- `list_documents()`: 列出当前已加载的所有文档。
- `search_in_documents(query: str)`: 在所有已加载文档中搜索与查询相关的内容。建议直接使用此工具检索所需信息。

**搜索策略支持:**
1. **flat策略**：一次性将所有文档内容一起发给模型分析（适用于总文档量较小的情况）
2. **map策略**：单独分析每个文档是否相关，直接返回相关文档的完整内容（适用于需要原始文档的场景）
3. **map-reduce策略**：先单独分析每个文档摘要，再综合所有相关摘要生成最终回答（适用于文档量大的情况）

**处理模式:**
- **批量处理模式**: 使用模型的批处理接口，可提高处理效率和一致性
- **多进程模式**: 使用多进程并行处理，适用于需要更高并发的场景

系统会根据配置自动选择最合适的策略和处理模式，确保高效准确的文档检索。
"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化文档库并自动加载文档
        
        参数:
            config: 配置字典，可包含批处理相关配置
        """
        # 存储加载的文档
        self.documents = {}  # 文档名 -> 文档内容
        
        # 创建OpenAI模型，使用long符号名称
        self.model = OpenAIModel("long")
        
        # 从配置中获取批处理相关参数
        # 首先导入 config 模块，然后从传入的配置字典中获取值，如果没有则使用全局配置
        import config as global_config
        
        if config:
            use_batch_generate = config.get("use_batch_generate", global_config.DOCUMENT_USE_BATCH_GENERATE)
            filter_vote_count = config.get("filter_vote_count", global_config.DOCUMENT_FILTER_VOTE_COUNT)
            concurrency_limit = config.get("concurrency_limit", global_config.DOCUMENT_CONCURRENCY_LIMIT)
        else:
            use_batch_generate = global_config.DOCUMENT_USE_BATCH_GENERATE
            filter_vote_count = global_config.DOCUMENT_FILTER_VOTE_COUNT
            concurrency_limit = global_config.DOCUMENT_CONCURRENCY_LIMIT
        
        # 存储批处理配置
        self.use_batch_generate = use_batch_generate
        self.filter_vote_count = filter_vote_count
        
        # 创建批处理器
        self.batch_processor = AsyncBatchProcessor(chunk_size=concurrency_limit)
        
        # 调用父类初始化
        super().__init__("document", "文档处理工具库", prompt=self.library_prompt, config=config)
        
        # 记录配置信息
        logger.info(f"文档库初始化完成：batch模式={'启用' if self.use_batch_generate else '禁用'}，Filter投票数={self.filter_vote_count}，并发限制={concurrency_limit}")
        
        # 自动加载documents文件夹中的文档
        # 未来可以考虑从 config 获取文件夹路径
        self._load_documents_from_folder("documents")
    
    def _load_documents_from_folder(self, folder_path):
        """
        从指定文件夹加载所有文档（内部方法）
        """
        try:
            # 确保文件夹存在
            if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                logger.warning(f"警告: 文件夹 '{folder_path}' 不存在或不是一个目录")
                return
            
            # 默认文件扩展名
            extensions = ["txt", "md", "py", "js", "html", "css", "json", "yaml", "yml"]
            
            # 加载所有文件
            loaded_count = 0
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                
                # 检查是否是文件而非目录，以及扩展名是否匹配
                if os.path.isfile(file_path):
                    file_ext = filename.split('.')[-1].lower() if '.' in filename else ""
                    if file_ext in extensions:
                        try:
                            # 尝试读取文件内容
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # 存储文档
                            self.documents[filename] = content
                            loaded_count += 1
                        except Exception as e:
                            logger.error(f"读取文件 '{filename}' 时出错: {traceback.format_exc()}")
            
            logger.info(f"文档库: 成功加载了 {loaded_count} 个文档")
        except Exception as e:
            logger.error(f"加载文档时出错: {traceback.format_exc()}")
    
    @register_tool
    async def get_document_content(
        self,
        document_name: Annotated[str, "要获取内容的文档名称"]
    ) -> Dict[str, Union[bool, str]]:
        """
        获取指定文档的内容
        """
        if not self.documents:
            return {"success": False, "error": "未找到任何文档"}
        
        if document_name not in self.documents:
            return {"success": False, "error": f"找不到文档 '{document_name}'，可用文档: {', '.join(self.documents.keys())}"}
        
        return {
            "success": True,
            "content": self.documents[document_name]
        }
    
    @register_tool
    async def list_documents(self) -> Dict[str, Union[bool, List[str], str, int]]:
        """
        列出当前已加载的所有文档
        """
        if not self.documents:
            return {"success": True, "documents": [], "count": 0, "message": "未找到任何文档"}
        
        # 返回所有文档的名称列表
        document_list = list(self.documents.keys())
        return {
            "success": True,
            "documents": document_list,
            "count": len(document_list)
        }
    
    @register_tool
    async def search_in_documents(
        self,
        query: Annotated[str, "搜索查询，可以是问题、关键词或其他任何搜索表达式"]
    ) -> Dict[str, Union[bool, str, Dict[str, str]]]:
        """
        在所有已加载文档中搜索与查询相关的内容，返回完整回答
        """
        if not self.documents:
            return {"success": False, "error": "未找到任何文档"}
        
        try:
            import config as global_config
            strategy = global_config.DOCUMENT_SEARCH_STRATEGY
            logger.info(f"文档搜索使用策略: {strategy}")

            if strategy == 'map_reduce':
                return await self._search_in_documents_map_reduce(query)
            elif strategy == 'map':
                return await self._search_in_documents_map(query)
            else:
                logger.warning(f"未知的文档搜索策略 '{strategy}' 或未配置。将默认使用 'map_reduce' 策略。")
                # 默认或回退策略，map_reduce 通常更适合多种情况
                return await self._search_in_documents_map_reduce(query)
                
        except Exception as e:
            error_msg = f"搜索文档时出错: {traceback.format_exc()}"
            logger.error(error_msg)
            return {"success": False, "error": f"搜索失败: {e}"}

    async def _search_in_documents_map(self, query: str) -> Dict[str, Any]:
        """
        使用 map 策略在所有文档中搜索与查询相关的内容，直接返回相关文档。
        此模式仅判断文档相关性并返回完整原始内容。
        """
        logger.info(f"开始 Map 策略搜索，查询: '{query[:100]}...', batch模式: {self.use_batch_generate}")
        
        # 直接使用静态方法，通过processor_kwargs传递query参数
        map_results = await self.batch_processor.process_batch(
            items=self.documents,
            model_name="long", 
            map_prompt_generator=self._map_prompt_generator_for_relevance,
            map_response_processor=self._map_response_parser_for_relevance,
            batch_generate=self.use_batch_generate,
            query=query  # 通过**processor_kwargs传递query
        )
        
        # map_results 的结构是 {doc_name: doc_content_if_relevant_else_None}
        # 过滤掉值为 None (不相关) 的条目
        relevant_docs = {
            name: content for name, content in map_results.items() if content is not None
        }
        
        if not relevant_docs:
            logger.info(f"Map 策略: 未找到与查询 '{query[:100]}...' 相关的文档。")
            return {"success": True, "answer": f"根据您的查询 '{query}'，未在任何文档中找到直接相关的内容。", "relevant_documents": {}}
        
        answer = f"根据您的查询 '{query}'，找到了 {len(relevant_docs)} 个相关文档，已返回其完整内容。"
        logger.info(f"Map 策略: 找到 {len(relevant_docs)} 个相关文档。")
        return {"success": True, "answer": answer, "relevant_documents": relevant_docs}

    async def _search_in_documents_map_reduce(self, query: str) -> Dict[str, Any]:
        """
        使用 map-reduce 策略在所有文档中搜索与查询相关的内容。
        map 阶段：对每个文档单独提取摘要（如果相关）；
        reduce 阶段：综合所有相关摘要生成最终回答，并列出引用文档。
        """
        logger.info(f"开始 Map-Reduce 策略搜索，查询: '{query[:100]}...', batch模式: {self.use_batch_generate}, Filter投票数: {self.filter_vote_count}")
        
        # 直接使用静态方法，通过processor_kwargs传递参数
        result_from_batch = await self.batch_processor.process_batch(
            items=self.documents,
            model_name="long",
            map_prompt_generator=self._map_prompt_generator_for_summary,
            map_response_processor=self._map_response_parser_for_summary, 
            reduce_prompt_generator=self._reduce_prompt_generator_for_answer,
            reduce_response_processor=self._reduce_response_parser_for_answer,
            batch_generate=self.use_batch_generate,  # 使用配置的批处理模式
            filter_n=self.filter_vote_count,  # Filter阶段多数投票（如果有Filter阶段的话）
            query=query,  # 通过**processor_kwargs传递query
            documents=self.documents  # 传递documents给reduce阶段使用
        )

        if isinstance(result_from_batch, dict):
            # 情况1: 成功，我们的 _reduce_response_parser_for_answer 返回了期望的字典
            if "answer" in result_from_batch and "relevant_documents" in result_from_batch and "success" not in result_from_batch:
                logger.info(f"Map-Reduce 策略成功完成。查询: '{query[:100]}...'")
                return {"success": True, **result_from_batch}
            
            # 情况2: batch_processor 在 reduce 阶段内部出错 (例如LLM调用失败)
            elif "error" in result_from_batch and "map_results" in result_from_batch:
                logger.error(f"Map-Reduce 在 Reduce 阶段失败: {result_from_batch['error']}. 查询: '{query[:100]}...'")
                map_summaries = result_from_batch["map_results"]
                if map_summaries: # 如果有 map 阶段的部分结果
                    relevant_doc_names = list(map_summaries.keys())
                    fallback_answer = (f"根据您的查询 '{query}'，在生成最终答案时遇到问题。"
                                       f"已从 {len(relevant_doc_names)} 个文档中提取到初步摘要。")
                    relevant_docs_content = {name: self.documents[name] for name in relevant_doc_names if name in self.documents}
                    return {"success": True, "answer": fallback_answer, "relevant_documents": relevant_docs_content, "warning": "最终答案生成失败，返回基于摘要的初步结果。"}
                else: # Reduce 失败且无 map 结果
                    return {"success": False, "error": f"Map-Reduce 失败: {result_from_batch['error']} (无Map结果)。查询: {query}"}
            
            # 情况3: Map 结果为空，Reduce 被跳过，batch_processor 返回空字典
            elif not result_from_batch: 
                logger.info(f"Map-Reduce 策略: Map阶段未产生任何摘要，查询: '{query[:100]}...'")
                return {"success": True, "answer": f"根据您的查询 '{query}'，未能从文档中提取到相关摘要信息。", "relevant_documents": {}}
            
            # 情况4: 未预期的字典结构 (可能仅包含 map 结果，如果 reduce 未正确配置或被跳过且 map 结果非空)
            else:
                # 检查它是否是 Map 阶段的结果 (字典，键是文档名，值是摘要字符串或None)
                if all(isinstance(k, str) and (isinstance(v, str) or v is None) for k, v in result_from_batch.items()):
                    logger.warning(f"Map-Reduce 策略: Reduce阶段似乎未执行或未成功返回标准格式，返回Map阶段结果。查询: '{query[:100]}...'")
                    valid_summaries = {k:v for k,v in result_from_batch.items() if v is not None}
                    if not valid_summaries:
                         return {"success": True, "answer": f"根据您的查询 '{query}'，未能从文档中提取到相关摘要信息。", "relevant_documents": {}}

                    relevant_doc_names = list(valid_summaries.keys())
                    fallback_answer = (f"根据您的查询 '{query}'，处理可能未完全完成。"
                                       f"已从 {len(relevant_doc_names)} 个文档中提取到初步摘要。")
                    relevant_docs_content = {name: self.documents[name] for name in relevant_doc_names if name in self.documents}
                    return {"success": True, "answer": fallback_answer, "relevant_documents": relevant_docs_content, "warning": "处理可能未完全完成，返回基于摘要的初步结果。"}
                
                logger.error(f"Map-Reduce 返回了未预期的字典结构: {result_from_batch}. 查询: '{query[:100]}...'")
                return {"success": False, "error": f"Map-Reduce 返回了未预期的字典结构。查询: {query}"}
        
        # 情况5: 返回非字典类型，这不应该发生
        else:
            logger.error(f"Map-Reduce 返回了非字典类型: {type(result_from_batch)}. 查询: '{query[:100]}...'")
            return {"success": False, "error": f"Map-Reduce 返回了非字典类型。查询: {query}"}

    # ----- 新增回调函数: Map策略使用 -----
    
    @staticmethod
    async def _map_prompt_generator_for_relevance(
        doc_name: str, 
        doc_content: str, 
        query: str,  # query 现在作为独立参数传入
        **kwargs 
    ):
        """
        为相关性检查生成Prompt (Map策略)
        """
        from message import SystemMessage, UserMessage
        
        system_prompt = """你是一位严谨的文档相关性判断助手。
你的任务是根据用户提供的查询，判断当前文档是否与该查询紧密相关。
请严格按照指定格式输出你的判断理由和最终决定。"""
        
        user_prompt = f"""用户查询: {query}

文档名称: {doc_name}
文档内容 (部分预览以判断相关性):
{doc_content[:2000]}{"... (内容过长，已截断)" if len(doc_content) > 2000 else ""}

请仔细评估此文档与上述"用户查询"的相关性。严格按照以下格式回答，不要包含其他任何文字：
[REASON]
(在此处简要说明你判断的理由，例如：文档讨论了查询中的核心概念A和B。)
[/REASON]
[IS_RELEVANT]
(在此处填写"是"或"否")
[/IS_RELEVANT]

例如:
用户查询: "MeowAgent框架的数据库配置信息"
文档名称: config_guide.md
文档内容: "... MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017/') ..."
你的回答应该是:
[REASON]
文档内容提到了 MONGO_URI 环境变量，这与MeowAgent的数据库配置直接相关。
[/REASON]
[IS_RELEVANT]
是
[/IS_RELEVANT]

用户查询: "最新的AI绘画模型进展"
文档名称: text_summary_algorithms.txt
文档内容: "本文主要讨论TF-IDF和BERT在文本摘要中的应用..."
你的回答应该是:
[REASON]
文档内容主要讨论文本摘要算法，与AI绘画模型不直接相关。
[/REASON]
[IS_RELEVANT]
否
[/IS_RELEVANT]
"""
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]
    
    @staticmethod
    async def _map_response_parser_for_relevance( # 将作为 map_response_processor 使用
        raw_resp: str, 
        doc_name: str, 
        doc_content_from_item: str, # 这是 item_value
        query: str,  # query 现在作为独立参数传入
        **kwargs
    ):
        """
        解析相关性检查的LLM响应 (Map策略)
        如果相关，返回文档内容；否则返回None
        """

        cleaned_response = raw_resp.strip()
        # 基本的Markdown代码块清理 (可以从 batch_processor 借鉴更复杂的)
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            lines = cleaned_response.split('\n')
            if len(lines) > 1:
                cleaned_response = '\n'.join(lines[1:-1]).strip()
            else: # 单行 ```...```
                cleaned_response = cleaned_response[3:-3].strip()
        
        match_relevant = re.search(r"\[IS_RELEVANT\](.*?)(?:\[/IS_RELEVANT\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        
        is_relevant_decision = False
        if match_relevant:
            decision_text = match_relevant.group(1).strip().lower()
            if decision_text in ["是", "yes", "true"]:
                is_relevant_decision = True
            elif decision_text in ["否", "no", "false"]:
                is_relevant_decision = False
            else:
                logger.warning(f"Map策略相关性判断: 无法从[IS_RELEVANT]标签明确解析 '{decision_text}' (文档: {doc_name}, 查询: '{query[:50]}...'). 默认为不相关。")
                is_relevant_decision = False 
        else:
            logger.warning(f"Map策略相关性判断: 未找到[IS_RELEVANT]标签 (文档: {doc_name}, 查询: '{query[:50]}...'). 默认为不相关。响应: {cleaned_response[:100]}...")
            # 可以考虑更宽松的文本推断，但为保持明确性，若无标签则视为不明确->不相关
            is_relevant_decision = False

        return doc_content_from_item if is_relevant_decision else None 

    # ----- 新增回调函数: Map-Reduce策略使用 -----
    
    @staticmethod
    async def _map_prompt_generator_for_summary(
        doc_name: str, 
        doc_content: str, 
        query: str,  # query 现在作为独立参数传入
        **kwargs 
    ):
        """
        为文档摘要生成Prompt (Map-Reduce的Map阶段)
        """
        from message import SystemMessage, UserMessage
        
        system_prompt = """你是一位专业的单文档分析助手。
你的任务是：
1. 判断当前文档是否与用户提供的查询紧密相关。
2. 如果相关，请针对用户查询，从文档中提取关键信息并生成一个简洁的摘要。
3. 严格按照指定格式输出你的判断和摘要。"""
        
        user_prompt = f"""用户查询: {query}

文档名称: {doc_name}
文档内容 (部分预览):
{doc_content[:3000]}{"... (内容过长，已截断)" if len(doc_content) > 3000 else ""}

请仔细评估此文档与上述"用户查询"的相关性。然后按以下格式返回结果，不要包含其他任何文字：

[IS_RELEVANT]
(在此处填写"是"或"否"，判断文档是否与用户查询相关)
[/IS_RELEVANT]

[SUMMARY]
(如果上面判断为"是"，请在此处提供针对用户查询的简洁摘要，突出核心信息。如果判断为"否"，请将此处留空或填写"不适用"。)
[/SUMMARY]

例如:
用户查询: "MeowAgent中如何配置记忆库的最大标签数?"
文档内容: "... MAX_OPEN_TABS = int(os.getenv('MAX_OPEN_TABS', '10')) ... 这是最大打开标签页的配置。 ..."
你的回答应该是:
[IS_RELEVANT]
是
[/IS_RELEVANT]
[SUMMARY]
MeowAgent中记忆库的最大标签数通过环境变量 MAX_OPEN_TABS 配置，默认为10。
[/SUMMARY]

用户查询: "解释一下Transformer架构的原理"
文档内容: "... MeowAgent的CLI界面通过 `click` 库实现 ..."
你的回答应该是:
[IS_RELEVANT]
否
[/IS_RELEVANT]
[SUMMARY]
不适用
[/SUMMARY]
"""
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]
    
    @staticmethod
    async def _map_response_parser_for_summary( # 将作为 map_response_processor 使用
        raw_resp: str, 
        doc_name: str, 
        doc_content_from_item: str, # item_value
        query: str,  # query 现在作为独立参数传入
        **kwargs
    ):
        """
        解析文档摘要的LLM响应 (Map-Reduce的Map阶段)
        如果相关，返回文档摘要 (str)；否则返回None
        """

        cleaned_response = raw_resp.strip()
        # Markdown清理 (简版)
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            cleaned_response = re.sub(r"^```(?:.*\n)?(.*?)(?:\n```)?$", r"\1", cleaned_response, flags=re.DOTALL).strip()

        is_relevant_decision = False
        summary_text = None

        match_relevant = re.search(r"\[IS_RELEVANT\](.*?)(?:\[/IS_RELEVANT\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        match_summary = re.search(r"\[SUMMARY\](.*?)(?:\[/SUMMARY\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)

        if match_relevant:
            decision_text = match_relevant.group(1).strip().lower()
            if decision_text in ["是", "yes", "true"]:
                is_relevant_decision = True
        # 如果没有IS_RELEVANT标签，但有SUMMARY内容，也倾向于是相关的
        elif match_summary and match_summary.group(1).strip() and match_summary.group(1).strip().lower() not in ["不适用", "na", "n/a", ""]:
            is_relevant_decision = True
            logger.debug(f"Map-Reduce(_map_response_parser_for_summary): 未找到[IS_RELEVANT]标签，但根据SUMMARY内容推断为相关。文档: {doc_name}")

        if is_relevant_decision and match_summary:
            summary_candidate = match_summary.group(1).strip()
            if summary_candidate and summary_candidate.lower() not in ["不适用", "na", "n/a"]:
                summary_text = summary_candidate
            elif not summary_candidate: # 如果相关但摘要为空，也算一种有效解析，只是摘要是空
                summary_text = "" # 空摘要
        
        if is_relevant_decision and summary_text is not None:
            logger.debug(f"Map-Reduce(_map_response_parser_for_summary): 文档 '{doc_name}' 相关，摘要长度: {len(summary_text)}. 查询: '{query[:50]}...'")
            return summary_text # 返回摘要字符串
        else:
            logger.debug(f"Map-Reduce(_map_response_parser_for_summary): 文档 '{doc_name}' 不相关或无有效摘要。查询: '{query[:50]}...'")
            return None # 不相关或无摘要则返回None，以便batch_processor过滤
    
    @staticmethod
    async def _reduce_prompt_generator_for_answer(
        map_summaries: Dict[str, str], # {doc_name: summary_string}
        query: str,  # query 现在作为独立参数传入
        **kwargs
    ):
        """
        生成Reduce阶段的Prompt，用于综合所有摘要 (Map-Reduce的Reduce阶段)
        """
        from message import SystemMessage, UserMessage
        
        if not map_summaries: # dovrebbe essere gestito da batch_processor, ma per sicurezza
            logger.warning("Map-Reduce(_reduce_prompt_generator_for_answer): map_summaries为空，不应调用此生成器。")
            return [] 
        
        system_prompt = """你是一位专业的文档综合分析和问答专家。
你的任务是基于用户提供的查询和一系列从不同文档中提取的相关摘要，生成一个全面、准确、整合的最终回答。
同时，你需要明确列出生成该回答时所参考的所有原始文档的名称。严格按照指定格式输出。"""
        
        summaries_str = ""
        for i, (doc_name, summary) in enumerate(map_summaries.items()):
            summaries_str += f"--- 摘要来源文档: {doc_name} ---\n{summary}\n\n"
            
        user_prompt = f"""用户查询: {query}

以下是从多个文档中提取的与查询相关的摘要信息:
{summaries_str}
请仔细阅读并综合以上所有摘要信息，针对"用户查询"给出一个最终的、统一的回答。
你的回答应该整合来自不同摘要的关键点，避免简单罗列。

严格按照以下格式返回结果，不要包含其他任何文字：

[ANSWER_START]
(在此处填写综合后的最终回答。)
[ANSWER_END]

[SOURCE_DOCUMENTS_START]
(在此处列出生成上述回答时实际引用的所有原始文档的名称，每行一个文档名，例如：doc1.txt)
(确保只列出对最终回答有贡献的文档名。)
[/SOURCE_DOCUMENTS_END]

例如:
用户查询: "MeowAgent的配置方式有哪些，特别是API密钥和数据库相关的?"
摘要列表:
--- 摘要来源文档: config.py.md ---
可以通过环境变量配置BAILIAN_API_KEY, OPENROUTER_API_KEY等。数据库连接字符串是MONGO_URI。
---
--- 摘要来源文档: memory_setup.txt ---
记忆存储也支持OSS，需要配置OSS_ACCESS_KEY_ID等。
---

你的回答应该是:
[ANSWER_START]
MeowAgent支持通过环境变量配置多种服务的API密钥，如BAILIAN_API_KEY和OPENROUTER_API_KEY。数据库连接信息通过MONGO_URI环境变量设置。对于记忆存储，如果使用阿里云OSS，则需要配置OSS_ACCESS_KEY_ID等相关凭证。
[ANSWER_END]
[SOURCE_DOCUMENTS_START]
config.py.md
memory_setup.txt
[/SOURCE_DOCUMENTS_END]
"""
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]
    
    @staticmethod
    async def _reduce_response_parser_for_answer( # 将作为 reduce_response_processor 使用
        raw_result: str, 
        map_summaries: Dict[str, str], # {doc_name: summary_string}
        query: str,  # query 现在作为独立参数传入
        # self 参数用于访问 self.documents
        documents: Dict[str, str],  # 传入 self.documents
        **kwargs
    ):
        """
        解析Reduce阶段的LLM响应 (Map-Reduce的Reduce阶段)
        返回包含 answer (str) 和 relevant_documents (Dict[str, str]) 的字典
        """

        cleaned_response = raw_result.strip()
        # Markdown清理 (简版)
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            cleaned_response = re.sub(r"^```(?:.*\n)?(.*?)(?:\n```)?$", r"\1", cleaned_response, flags=re.DOTALL).strip()
        
        answer = "无法从模型响应中提取最终回答。" # 默认值
        source_names = []

        answer_match = re.search(r"\[ANSWER_START\](.*?)(?:\[ANSWER_END\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        sources_match = re.search(r"\[SOURCE_DOCUMENTS_START\](.*?)(?:\[SOURCE_DOCUMENTS_END\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)

        if answer_match:
            answer_candidate = answer_match.group(1).strip()
            if answer_candidate: # 确保提取的答案不是空的
                answer = answer_candidate
        else:
            logger.warning(f"Map-Reduce(_reduce_response_parser_for_answer): 未找到[ANSWER_START]标签。查询: '{query[:50]}...' 将使用整个响应作为答案（可能不准确）。响应: {cleaned_response[:100]}")
            # 回退：如果没找到ANSWER_START，但响应看起来像直接答案（不包含其他标签对），则用整个响应
            if not (sources_match or "[IS_RELEVANT]" in cleaned_response or "[SUMMARY]" in cleaned_response):
                 if cleaned_response: answer = cleaned_response
            # 否则，answer 保持默认的错误信息
                 
        if sources_match:
            sources_str = sources_match.group(1).strip()
            source_names = [name.strip() for name in sources_str.split('\n') if name.strip() and name.strip() != '...']
        else:
            logger.warning(f"Map-Reduce(_reduce_response_parser_for_answer): 未找到[SOURCE_DOCUMENTS_START]标签。查询: '{query[:50]}...' 引用的文档列表可能不准确。将尝试从map_summaries的键推断。")
            # 回退：如果模型未提供源文档，则默认引用所有提供摘要的文档
            source_names = list(map_summaries.keys())
        
        # 从 documents 获取实际的文档内容
        relevant_docs_content = {}
        for name in source_names:
            if name in documents:
                relevant_docs_content[name] = documents[name]
            else:
                logger.warning(f"Map-Reduce(_reduce_response_parser_for_answer): 模型引用的文档 '{name}' 在加载的文档中未找到。")
        
        logger.info(f"Map-Reduce(_reduce_response_parser_for_answer): 解析完成。答案长度: {len(answer)}, 引用文档数: {len(relevant_docs_content)}. 查询: '{query[:50]}...'")
        return {
            "answer": answer,
            "relevant_documents": relevant_docs_content
        } 