"""
Model Context Protocol (MCP) 客户端库。

该模块实现了 `MCPClientLibrary` 类，它作为 MeowAgent 框架与外部 MCP 服务器之间的桥梁。
主要功能包括：
1.  **连接建立与管理**：
    -   根据提供的配置（通常来自 `mcp.json` 或类似结构），通过标准输入/输出 (stdio) 启动并连接到 MCP 服务器进程。
    -   使用 `mcp` 库中的 `stdio_client` 建立通信传输，并通过 `ClientSession` 管理与服务器的会话。
2.  **远程工具集成**：
    -   在成功连接到 MCP 服务器后，查询服务器暴露的可用工具列表。
    -   将这些远程工具动态注册到 MeowAgent 的工具系统中，使其可以像本地工具一样被智能体调用。
    -   注册的工具名会以库名作为前缀，以确保全局唯一性。
3.  **工具调用代理**：
    -   当智能体调用一个由此库注册的工具时，`MCPClientLibrary` 会将调用请求（包括工具名和参数）
        通过 MCP 会话转发给相应的 MCP 服务器执行。
    -   它接收并返回 MCP 服务器的处理结果。
4.  **生命周期管理**：
    -   通过 `AsyncExitStack` 管理异步资源（如 stdio 传输和 MCP 会话），确保在库关闭时能够正确清理和释放这些资源。
5.  **MeowAgent 框架集成**：
    -   继承自 `Library` 基类，无缝集成到 MeowAgent 的工具库体系。
    -   动态更新库的提示信息，告知大型语言模型 (LLM) 当前可用的通过 MCP 连接的工具。

该库使得 MeowAgent 能够利用 MCP 标准化协议，与实现了该协议的外部数据源、服务或应用进行交互，
极大地扩展了智能体的能力范围。
"""
import os
import asyncio
from contextlib import AsyncExitStack
from functools import partial
import traceback
from typing import Dict, Any, Optional

from mcp.client.stdio import stdio_client
from mcp import ClientSession, StdioServerParameters
from libraries.library import Library
from log import logger


class MCPClientLibrary(Library):
    """
    MCP 客户端库，根据 mcp.json 配置启动/连接 MCP 服务器，
    并将远程工具注册为本地工具，工具名称前缀为库名。
    """
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(
            name=name,
            description=f"MCP 客户端库（{name}）",
            prompt=f"已连接到 MCP 服务器 '{name}'，可用工具："
        )
        self.config = config
        self.process = None                # 子进程句柄
        self.session: Optional[ClientSession] = None  # MCP 会话
        self.exit_stack = AsyncExitStack() # 用于管理连接上下文

    async def initialize(self):
        """
        初始化客户端库：
        1. 如果配置中含 command/args，以子进程方式启动（stdio）
        2. 等待 stdio 或 SSE 接口可用
        3. 建立 ClientSession 并拉取远程工具列表
        4. 将远程工具注册到 self.tools
        """
        # 1. 仅支持 stdio 模式
        cmd = self.config.get("command")
        args = self.config.get("args", [])
        if not (cmd and args):
            raise RuntimeError(f"[{self.name}] 初始化失败：必须提供 command 和 args")
        # 将环境变量转换为 dict 以匹配类型
        server_params = StdioServerParameters(command=cmd, args=args, env=dict(os.environ))
        transport = None
        for attempt in range(3):
            try:
                transport = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(stdio_client(server_params)),
                    timeout=2
                )
                break
            except Exception as e:
                logger.warning(f"[{self.name}] 等待 stdio 可用，{attempt+1}/3 重试: {traceback.format_exc()}")
                await asyncio.sleep(1)
        if not transport:
            raise RuntimeError(f"[{self.name}] 无法建立 stdio 连接")
        self.stdio, self.write = transport
        # 3. 建立 MCP 会话
        self.session = await self.exit_stack.enter_async_context(
            ClientSession(self.stdio, self.write)
        )
        await self.session.initialize()
        # 4. 拉取远程工具并注册
        resp = await self.session.list_tools()
        registered = []
        for tool in resp.tools:
            full_name = f"{self.name}.{tool.name}"
            executor = partial(self._call_remote_tool, tool.name)
            schema = getattr(tool, 'inputSchema', {"type": "object", "properties": {}})
            self.tools[full_name] = {
                "name": full_name,
                "original_name": tool.name,
                "description": tool.description,
                "input_schema": schema,
                "execute": executor
            }
            registered.append(full_name)
        # 更新提示词
        base = self.prompt or ""
        self.update_prompt(base + " " + ", ".join(registered))
        logger.info(f"[{self.name}] 已注册远程工具: {registered}")

    async def _call_remote_tool(self, remote_name: str, **params):
        """
        执行远程工具调用，并返回结果内容
        """
        # 确保 session 已初始化
        if self.session is None:
            raise RuntimeError(f"[{self.name}] 会话未初始化")
        try:
            result = await self.session.call_tool(remote_name, params)
            return result.content
        except Exception as e:
            logger.error(f"[{self.name}] 调用远程工具 {remote_name} 失败: {e}")
            return {"success": False, "error": str(e)}

    async def close(self):
        """
        关闭 MCP 客户端库：
        - 异步关闭连接上下文（这将处理 stdio transport 和 ClientSession）
        """
        logger.info(f"[{self.name}] 正在关闭 MCP 客户端...")
        try:
            # 异步关闭 AsyncExitStack，它会按添加的反序清理上下文
            await self.exit_stack.aclose()
            logger.info(f"[{self.name}] MCP 客户端已关闭。")
        except Exception as e:
            # 记录关闭过程中可能发生的任何错误
            logger.warning(f"[{self.name}] 关闭 MCP 客户端时发生错误: {e}")
        # 移除 self.process 的相关处理，由 AsyncExitStack 管理
