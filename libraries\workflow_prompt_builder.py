import json
import logging
import os # 导入 os 用于文件路径操作
from typing import Dict, Any, Optional, List, Set, Mapping, Union, cast
from utils import dumps
from libraries.workflow_state import WorkflowState
from libraries.workflow_models import (
    WorkflowDefinition, WorkflowStep, ActionDefinition,
    ExecuteStep, ConditionStep, JumpStep, GenerateStep, NopStep, HaltStep, WorkflowOperation, UserInputStep, SwitchStep
)

logger = logging.getLogger(__name__)

class WorkflowPromptBuilder:
    """
    工作流提示词构建器。

    该模块的核心是 `WorkflowPromptBuilder` 类，其主要职责是根据当前工作流的实时状态，
    为大型语言模型 (LLM) 构建结构化、详细且具有指导性的提示词。
    此提示词旨在引导 LLM 在预定义的工作流中正确地执行操作、调用工具或与用户交互。

    主要功能和逻辑包括：

    1.  **状态感知提示生成**：
        -   接收 `WorkflowState` 对象，该对象封装了工作流的当前所有状态信息，
            包括激活的工作流名称、来源类型（YAML 或 Python）、当前步骤索引/名称、
            寄存器内容、动作组调用计数以及是否等待用户输入等。
        -   根据是否存在激活的工作流，生成不同类型的提示：
            -   若无激活工作流，提示会警告 LLM 并指导其列出和设置一个工作流。
            -   若有激活工作流，提示会包含详细的状态信息和操作指引。

    2.  **动态指令生成**：
        -   针对当前工作流步骤，根据其操作类型（如 `ExecuteStep`, `ConditionStep`,
            `UserInputStep`, `GenerateStep`, `HaltStep` 等）生成具体的操作指令。
        -   **ExecuteStep**: 详细列出可用动作组、每个组的工具、调用限制（最小/最大调用次数）、
            当前调用计数，并明确指示 LLM 何时可以调用 `next_step()` 或必须继续调用工具。
        -   **ConditionStep**: 指导 LLM 评估条件，并调用 `condition_branch(result)` 工具。
        -   **UserInputStep**: 如果工作流（特别是 Python 类型）正等待用户输入，
            会指示 LLM 向用户提问，并强调此时不应调用任何工具。
        -   **GenerateStep**: 指示 LLM 直接生成文本内容，而非调用工具。
        -   **JumpStep/NopStep**: 指导 LLM 调用 `next_step()` 以推进流程。
        -   **HaltStep**: 告知 LLM 工作流已结束，应生成总结性回复。
        -   提供操作示例，帮助 LLM 理解期望的调用格式或回复。

    3.  **工作流定义展示**：
        -   为了给 LLM 提供完整的上下文，提示词中会包含当前工作流的定义：
            -   对于 YAML 类型的工作流，会展示其完整的 JSON 结构。
            -   对于 Python 类型的工作流，会显示其源文件路径、描述，并尝试读取和展示
                Python 脚本的原始内容。

    4.  **参数化构建**：
        -   `build` 方法接受基础提示词 (`base_prompt`)、库名称 (`library_name`)，
            以及可选的工作流描述 (`workflow_description`) 和 Python 文件路径 (`python_file_path`)，
            这些参数共同构成了最终的完整提示词。

    通过这种方式，`WorkflowPromptBuilder` 模块确保 LLM 在执行工作流时，
    能够获得清晰、准确且与当前上下文紧密相关的指令，从而提高工作流执行的可靠性和准确性。
    """
    def build(
        self, \
        state: WorkflowState, \
        base_prompt: str, \
        library_name: str, \
        # 添加新参数
        workflow_description: Optional[str] = None, \
        python_file_path: Optional[str] = None\
    ) -> str:
        """
        构建工作流库的提示词，根据当前状态动态生成

        参数：
            state: 当前工作流状态对象
            base_prompt: 基础提示词（不包含状态信息）
            library_name: 库名称，用于工具名前缀
            workflow_description: 当前工作流的描述 (从 Library 获取)
            python_file_path: 如果是 Python 工作流，其文件路径 (从 Library 获取)

        返回：
            完整的库提示词
        """
        # 从 state 对象获取状态信息
        current_workflow_name = state.workflow_name
        current_step_index = state.step_index
        workflow_registers = state.registers
        action_group_call_counts = state.action_group_call_counts

        # 使用传入的描述，如果未设置工作流则为 None
        description_to_show = workflow_description if current_workflow_name else None

        if not current_workflow_name:
            # 没有当前工作流时的提示
            current_workflow_section = """

**当前工作流: 无**

**警告:** 你当前没有激活的工作流程！
**必须** 使用 `list_available_workflows()` 查看可用流程，然后**立即**使用 `set_current_workflow(workflow_name)` 设置一个工作流。
"""
            # 返回未设置工作流的提示，合并到基础提示
            return base_prompt + current_workflow_section
        else:
            # 构建当前状态信息
            status_section = f"""

--- 工作流状态 ---
工作流名称: {current_workflow_name} (类型: {state.source_type})
描述: {description_to_show or '(无描述)'}
"""

            # 添加当前执行状态信息
            if state.source_type == 'yaml':
                 # YAML 显示索引
                 status_section += f"当前步骤索引: {current_step_index if current_step_index is not None else '未初始化'}\n"
            else:
                 # Python 不显示索引
                 status_section += f"当前步骤索引: (N/A for Python)\n"

            # 添加寄存器状态 (如果存在)
            if workflow_registers:
                status_section += "寄存器状态:\n"
                for reg_name, reg_value in workflow_registers.items():
                    status_section += f"  - `{reg_name}`: {dumps(reg_value, ensure_ascii=False)}\n" # 使用dumps确保值正确显示
            else:
                status_section += "寄存器状态: (空)\n"

            # 添加当前步骤的详细指令
            current_step = state.get_current_step()

            if current_step:
                # 获取步骤名称 (Python 可能只有 name, YAML 可能只有 index)
                step_name = current_step.name
                step_index_yaml = current_step.index if state.source_type == 'yaml' else None
                step_display = step_name or (f'步骤{step_index_yaml}' if step_index_yaml is not None else '[未知步骤]')
                
                step_desc = current_step.description or '无描述'
                operation = current_step.operation

                status_section += f"\n**当前指令 ({step_display})**\n"
                status_section += f"  描述: {step_desc}\n"
                status_section += f"  操作类型: {operation}\n"

                # 添加明确的操作指令
                status_section += "  **要求:**\n"
                example_section = "" # 初始化示例部分

                # 检查是否是UserInputStep，以及是否正在等待用户输入
                if isinstance(current_step, UserInputStep):
                    # 特别处理等待用户输入的情况
                    if state.source_type == 'python' and state.is_waiting_for_user:
                        status_section += f"""    1. **当前是用户输入步骤，正在等待用户输入**
    2. **注意事项:**
       - **不要调用任何工具**，只需简短提示用户
       - **简洁地引导用户**提供与当前步骤描述相关的信息
       - 如果步骤描述中包含具体问题，可以直接将其转达给用户
       - 等待用户回应后，工作流将自动继续执行后续步骤
    3. **要求:** 直接以对话方式向用户提问，不需要额外解释工作流状态
"""
                        example_section = """    **示例回复:**
    "请描述您遇到的问题，我将为您提供帮助。"
    或
    "请提供您需要查询的信息。"
"""
                    else:
                        # 状态不一致的情况（理论上不该发生）
                        status_section += f"    1. **这是用户输入步骤，但状态不一致**\n"
                        status_section += f"    2. **请稍等**，系统将重置状态\n"
                        logger.warning(f"检测到UserInputStep但state.is_waiting_for_user={state.is_waiting_for_user}，状态不一致")

                elif isinstance(current_step, ConditionStep):
                    condition_desc = current_step.condition_description or "未指定条件"
                    # 获取跳转目标 (YAML 用索引，Python 用名称? 目前模型定义中还是索引)
                    # TODO: 确认 Python ConditionStep 是否需要不同的目标表示法
                    true_target = current_step.true_branch
                    false_target = current_step.false_branch
                    target_display_true = f"步骤 {true_target}" if state.source_type == 'yaml' else f"'{true_target}' (预期步骤名)"
                    target_display_false = f"步骤 {false_target}" if state.source_type == 'yaml' else f"'{false_target}' (预期步骤名)"
                    
                    status_section += f"    1. 评估条件: \"{condition_desc}\"\n"
                    status_section += f"    2. **必须调用:** `condition_branch(result)` 并传入布尔结果 (True/False)。\n"
                    status_section += f"       - True -> {target_display_true}\n"
                    status_section += f"       - False -> {target_display_false}\n"
                    # 添加示例
                    example_section = (
                        f"    **示例:** 如果你评估条件 \"{condition_desc}\" 的结果为 True，"
                        f"则应调用 `condition_branch(condition_result=True)`\n"
                    )

                elif isinstance(current_step, ExecuteStep):
                    # 获取步骤 ID (index for YAML, name for Python)
                    step_id = step_index_yaml if state.source_type == 'yaml' else step_name
                    if step_id is None:
                         logger.error(f"无法获取 ExecuteStep 的 ID (index/name): {current_step}")
                         status_section += "    **错误:** 无法获取步骤ID，无法显示调用计数。\n"
                    else:
                        # 获取当前步骤的动作组调用计数
                        current_group_counts = action_group_call_counts.get(step_id, {})

                        # 统计每个动作组的状态
                        group_status_lines = []
                    # 修改：处理动作组 (ActionDefinition 列表)
                    step_idx = current_step.index
                    # 获取当前步骤的动作组调用计数 (使用新的状态变量)
                    current_group_counts = action_group_call_counts.get(step_idx, {}) if action_group_call_counts else {}

                    # 统计每个动作组的状态
                    group_status_lines = []
                    all_min_calls_met_for_all_groups = True # 跟踪是否所有组都满足了 min_calls

                    # 遍历动作组 (ActionDefinition 列表)
                    for i, action_group in enumerate(current_step.actions):
                        # 获取组信息
                        group_names = action_group.names
                        min_calls = action_group.min_calls
                        max_calls = action_group.max_calls

                        # 获取该组的调用计数
                        count = current_group_counts.get(i, 0)
                        left = max_calls - count
                        need = max(0, min_calls - count)

                        # 生成状态描述
                        group_names_str = ", ".join(f"`{name}`" for name in group_names)
                        status_parts = []
                        if count >= max_calls:
                            status_parts.append(f"已调用 {count} 次，已达最大调用次数({max_calls})，不可再用")
                        else:
                            status_parts.append(f"已调用 {count} 次")
                            if need > 0:
                                status_parts.append(f"还需调用 {need} 次 (最少{min_calls}) ")
                                # 只要有一个组不满足，则整体不满足
                                all_min_calls_met_for_all_groups = False
                            else:
                                status_parts.append(f"已满足最小调用次数({min_calls}) ")
                            status_parts.append(f"还可调用 {left} 次 (最大{max_calls}) ")

                        group_status_lines.append(f"- **组 {i}** ({group_names_str}): {'，'.join(status_parts)}")

                    # 汇总动作组状态
                    if current_step.actions:
                        status_section += "    1. **本步骤可用动作组及调用要求：**\n"
                        for line in group_status_lines:
                            status_section += f"       {line}\n"
                    else:
                        status_section += "    1. **本步骤未定义任何动作组。**\n"

                    # next_step提示 (基于所有组的 all_min_calls_met_for_all_groups)
                    next_target = current_step.next
                    # 只有在定义了动作组的情况下才需要考虑 next_step (如果无动作组，通常直接前进)
                    if current_step.actions:
                        if all_min_calls_met_for_all_groups:
                            status_section += f"    2. **所有动作组的最小调用次数已满足。你现在可以选择调用 `{library_name}.next_step()` 进入步骤 {next_target}，或继续从允许的动作组中调用工具。**\n"
                        else:
                            status_section += f"    2. **请优先从需要调用的动作组中选择工具。所有动作组均满足最小调用次数后，才可调用 `{library_name}.next_step()` 进入步骤 {next_target}。**\n"
                    elif next_target is not None: # 无动作组，但有下一步
                        status_section += f"    2. **无指定动作组，必须调用 `{library_name}.next_step()` 进入步骤 {next_target}。**\n"

                    # 修改示例
                    example_section = "    **示例:**\n"
                    if current_step.actions:
                        for i, action_group in enumerate(current_step.actions):
                            count = current_group_counts.get(i, 0)
                            if count < action_group.max_calls:
                                example_tool = action_group.names[0] # 随便选一个工具名作示例
                                example_section += f"      - 从组 {i} 调用 `{example_tool}()`\n"

                        if all_min_calls_met_for_all_groups:
                            example_section += f"      - 或调用 `{library_name}.next_step()` 进入下一步\n"
                        else:
                            example_section += f"      - 当前不能调用 `{library_name}.next_step()`，请先满足所有动作组的最小调用次数\n"
                    elif next_target is not None:
                        example_section += f"      - 调用 `{library_name}.next_step()` 进入下一步\n"
                    else: # 无动作也无下一步，可能是流程终点或错误
                        example_section = "" # 无示例

                elif isinstance(current_step, JumpStep):
                    # JUMP现在统一由next_step处理（无条件）或condition_branch（有条件）
                    if current_step.next_condition:
                        condition_desc = current_step.condition_description or current_step.next_condition or '未指定条件'
                        true_target = current_step.true_branch
                        false_target = current_step.false_branch
                        status_section += f"    1. 评估条件: \"{condition_desc}\"\n"
                        status_section += f"    2. **必须调用:** `condition_branch(result)` 并传入布尔结果 (True/False)。\n"
                        status_section += f"       - True -> 步骤 {true_target}\n"
                        status_section += f"       - False -> 步骤 {false_target}\n"
                        # 添加示例
                        example_section = (
                            f"    **示例:** 如果你评估条件 \"{condition_desc}\" 的结果为 False，"
                            f"则应调用 `condition_branch(condition_result=False)`\n"
                        )
                    else: # 无条件跳转
                        next_target = current_step.next
                        status_section += f"    1. **必须调用:** `{library_name}.next_step()` 以跳转到步骤 {next_target}\n"
                        example_section = f"    **示例:** 调用 `{library_name}.next_step()`\n"

                elif isinstance(current_step, NopStep):
                    # NOP是一个简单的next_step
                    next_target = current_step.next
                    status_section += f"    1. **必须调用:** `{library_name}.next_step()` 以跳转到步骤 {next_target}\n"
                    example_section = f"    **示例:** 调用 `{library_name}.next_step()`\n"

                elif isinstance(current_step, GenerateStep):
                    # GENERATE是直接生成内容，不调用工具
                    status_section += f"    1. **生成以下内容:** {current_step.content_description}\n"
                    # 确认是否需要等待用户输入 (旧方式)
                    status_section += f"    2. **无需调用任何工具**，直接生成文本回复\n"
                    if getattr(current_step, 'wait_user', False):
                        status_section += f"    3. (使用旧API) 生成后将等待用户输入，然后自动前进\n"
                        status_section += f"    注意：此方式已不推荐使用，建议使用UserInputStep替代\n"
                    else:
                        next_target = current_step.next
                        status_section += f"    3. 生成后将自动前进到步骤 {next_target}\n"
                    example_section = f"    **示例:** 直接生成文本回复，不调用任何工具\n"

                elif isinstance(current_step, HaltStep):
                    # HALT是终止流程
                    status_section += f"    1. **工作流结束**：生成最终结论或总结\n"
                    status_section += f"    2. **无需调用任何工具**，直接生成文本回复\n"
                    example_section = f"    **示例:** 直接生成工作流程结束的最终回复，不调用任何工具\n"

                elif isinstance(current_step, SwitchStep):
                    # SWITCH是分支选择步骤
                    cases = getattr(current_step, 'cases', [])
                    if cases:
                        status_section += f"    1. **分支选择步骤**：根据当前情况选择合适的工作流分支\n"
                        status_section += f"    2. **可选分支 ({len(cases)} 个):**\n"
                        for case in cases:
                            case_name = getattr(case, 'name', '未知')
                            case_desc = getattr(case, 'description', '无描述')
                            status_section += f"       - **{case_name}**: {case_desc}\n"
                        status_section += f"    3. **必须调用:** `{library_name}.select_case_target(target_case_name='选择的分支名称')` 来选择一个分支\n"
                        
                        # 添加示例
                        first_case = cases[0]
                        case_name_example = getattr(first_case, 'name', 'example_case')
                        example_section = f"    **示例:** 如果你选择 '{getattr(first_case, 'description', '示例分支')}'，则调用 `{library_name}.select_case_target(target_case_name='{case_name_example}')`\n"
                    else:
                        status_section += f"    1. **分支选择步骤**：但未定义任何可选分支\n"
                        status_section += f"    2. **错误:** 此步骤配置有误，请联系开发人员\n"
                        example_section = ""

                else:
                    # 未知操作类型
                    status_section += f"    **警告:** 遇到未知操作类型 '{operation}'！请咨询开发人员。\n"

                # 添加示例部分
                if example_section:
                    status_section += "\n" + example_section

                # 强调必须严格执行
                status_section += "\n**再次强调:** 你 **必须** 严格按照 **当前指令** 的 **要求** 执行，并调用指定的工具函数来推进工作流！\n"

            status_section += "---"

            # --- 构建工作流定义展示部分 (根据类型调整) ---
            definition_section = ""
            if state.source_type == 'yaml' and isinstance(state.workflow_data, WorkflowDefinition):
                 # 对于 YAML，显示完整的 JSON 定义
                 try:
                     workflow_definition_json = dumps(state.workflow_data.dict(), indent=2, ensure_ascii=False)
                 except Exception as e:
                     logger.error(f"序列化 YAML 工作流定义失败: {e}")
                     workflow_definition_json = "{\"error\": \"无法序列化 YAML 工作流定义\"}"
                 definition_section = f"""

**工作流完整定义 (YAML):**
```json
{workflow_definition_json}
```
"""
            elif state.source_type == 'python':
                 # 对于 Python，显示描述和文件路径，并读取文件内容
                 workflow_file_content = "(无法读取文件内容)"
                 if python_file_path and os.path.exists(python_file_path):
                     try:
                         with open(python_file_path, 'r', encoding='utf-8') as f:
                             workflow_file_content = f.read()
                     except Exception as e:
                         logger.error(f"读取 Python 工作流文件 '{python_file_path}' 失败: {e}")
                         workflow_file_content = f"(读取文件 '{python_file_path}' 时出错)"
                 elif python_file_path:
                      workflow_file_content = f"(文件未找到: '{python_file_path}')"
                 else:
                      workflow_file_content = "(文件路径未提供)"
                 
                 definition_section = f"""

**工作流定义 (Python):**
来源文件: `{python_file_path or '(未知)'}`
描述: {description_to_show or '(无描述)'}

```python
{workflow_file_content}
```
"""
            else:
                 # 未知类型或错误
                 definition_section = "\n\n**工作流定义: (未知或错误)**\n"

            # 组合所有部分
            current_workflow_section = status_section + definition_section

        # 返回完整提示词
        return base_prompt + current_workflow_section 