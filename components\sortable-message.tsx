"use client"

import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import type { ChatMessageDisplay } from "@/types/message"
import MessageItem from "@/components/message-item"
import { useState } from "react"
import MessageEditor from "@/components/message-editor"

interface SortableMessageProps {
  message: ChatMessageDisplay
  onUpdate?: (message: ChatMessageDisplay) => void
  onDelete: (id: string) => void
}

export default function SortableMessage({ message, onUpdate, onDelete }: SortableMessageProps) {
  const [isEditing, setIsEditing] = useState(false)

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: message.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.4 : 1,
    zIndex: isDragging ? 1 : "auto",
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = (updatedMessage: ChatMessageDisplay) => {
    if (onUpdate) {
      onUpdate(updatedMessage)
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
  }

  if (isEditing) {
    return (
      <MessageEditor
        message={message}
        onSave={handleSave}
        onCancel={handleCancel}
        dragHandleProps={{ attributes, listeners }}
      />
    )
  }

  return (
    <div ref={setNodeRef} style={style}>
      <MessageItem
        message={message}
        onEdit={handleEdit}
        onDelete={() => onDelete(message.id)}
        isDragging={isDragging}
        dragHandleProps={{ attributes, listeners }}
        showDragHandle={true}
      />
    </div>
  )
}
