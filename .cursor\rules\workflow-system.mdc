---
description: MeowAgent 的工作流系统提供了引导智能体执行复杂任务的架构，支持定义一系列结构化步骤。工作流系统由多个相互协作的组件构成。
globs: 
alwaysApply: false
---
# MeowAgent 工作流系统

MeowAgent 的工作流系统提供了引导智能体执行复杂任务的架构，支持定义一系列结构化步骤。工作流系统由多个相互协作的组件构成。

## 工作流系统架构

工作流系统主要由以下几个组件组成：

1. [workflow.py](mdc:meowagent/libraries/workflow.py) - 工作流库，提供主要的用户接口和工具
2. [workflow_models.py](mdc:meowagent/libraries/workflow_models.py) - 定义工作流数据模型和步骤类型
3. [workflow_state.py](mdc:meowagent/libraries/workflow_state.py) - 管理工作流执行状态
4. [workflow_executor.py](mdc:meowagent/libraries/workflow_executor.py) - 处理步骤验证和执行逻辑
5. [workflow_loader.py](mdc:meowagent/libraries/workflow_loader.py) - 从文件加载工作流定义
6. [workflow_prompt_builder.py](mdc:meowagent/libraries/workflow_prompt_builder.py) - 构建工作流的动态提示词

## 工作流定义方式

MeowAgent支持两种定义工作流的方式：

### 1. YAML 声明式工作流

```yaml
description: "基础对话工作流"
steps:
  - index: 1
    name: "init"
    description: "初始化对话处理流程"
    operation: "NOP"
    
  - index: 2
    name: "user_input"
    description: "等待用户输入消息"
    operation: "USER_INPUT"
    
  - index: 3
    name: "search_documents"
    description: "搜索相关文档"
    operation: "EXECUTE"
    actions:
      - names: ["document.search_in_documents"]
        min_calls: 0
        max_calls: 1
```

### 2. Python 生成器工作流

```python
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """Python 实现的基础对话工作流步骤生成器"""
    
    yield NopStep(
        name="init",
        description="初始化对话处理流程",
    )
    
    while True:
        user_message: UserMessage = yield UserInputStep(
            name="get_user_message", 
            description="等待用户输入消息"
        )
        
        yield ExecuteStep(
            name="query_memories",
            description="查询相关记忆",
            actions=[
                ActionDefinition(
                    names=["memory.read_file", "memory.list_directory"],
                    min_calls=0,
                    max_calls=3
                )
            ]
        )
```

## 核心步骤类型

工作流由不同类型的步骤组成，每种类型对应不同的行为：

- **NopStep** - 无操作步骤，直接进入下一步
- **ExecuteStep** - 执行一组工具调用
- **ConditionStep** - 根据条件判断跳转到不同分支
- **JumpStep** - 跳转到工作流中的另一个步骤
- **GenerateStep** - 生成文本内容，不调用工具
- **HaltStep** - 终止当前工作流执行
- **UserInputStep** - 暂停工作流，等待用户输入

## 工作流执行过程

工作流执行的核心机制：

1. **预处理** (pre_process)：
   - 在LLM处理用户消息前执行
   - 对于Python工作流，推进生成器并获取下一步
   - 对于YAML工作流，处理用户输入步骤

2. **后处理** (post_process)：
   - 在LLM生成响应后执行
   - 验证LLM响应是否符合当前步骤要求
   - 如验证成功，可能自动前进到下一步

3. **步骤验证**：
   - 针对不同类型的步骤有特定的验证逻辑
   - 例如，对于ExecuteStep，检查工具调用是否符合动作组定义

4. **状态管理**：
   - WorkflowState跟踪当前步骤、寄存器值和工具调用计数
   - 支持通过寄存器在不同步骤间传递数据

