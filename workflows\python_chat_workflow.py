from typing import Dict, Any, AsyncGenerator
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "基础对话工作流（Python实现），指导智能体处理对话、记忆和文档"
INITIAL_REGISTERS: Dict[str, Any] = {}

# --- 工作流生成器函数 ---
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    Python 实现的基础对话工作流步骤生成器。
    """

    while True:

        user_message: UserMessage = yield UserInputStep(name="get_user_message", description="等待用户输入他们的消息。")

        # 步骤 1: 查询相关信息
        yield ExecuteStep(
            name="query_relevant_infos",
            description="查询与当前对话相关的记忆内容 (只读节点)",
            actions=[
                ActionDefinition(names=[
                        "memory.list_directory",
                        "memory.change_directory",
                        "memory.read_file",
                        "memory.open_item_to_tab",
                        "memory.close_tab",
                        "memory.view_tab",
                    ], min_calls=1, max_calls=5),
                ActionDefinition(names=["document.search_in_documents"], min_calls=1, max_calls=1)
            ],
        )

        # 步骤 2: 检查是否需要记忆
        contains_important_info: bool = yield ConditionStep(
            name="check_important_info",
            description="判断当前对话是否包含需要记忆的信息",
            condition_description="检查用户输入是否包含值得记忆的个人信息、偏好、重要事实等",
        )

        # 步骤 3 / 4: 根据条件结果执行不同操作
        if contains_important_info:
            # 步骤 3: 添加记忆
            yield ExecuteStep(
                name="add_memory",
                description="添加新记忆到记忆库",
                actions=[
                    ActionDefinition(names=[
                        "memory.list_directory",
                        "memory.change_directory",
                        "memory.create_item",
                        "memory.read_file",
                        "memory.write_file",
                        "memory.delete_item",
                        "memory.open_item_to_tab",
                        "memory.close_tab",
                        "memory.view_tab",
                    ], min_calls=1, max_calls=5),
                ],
            )

        # 步骤 4: 生成回复
        yield GenerateStep(
            name="generate_response",
            description="结合记忆和文档搜索结果生成回复给用户",
        )



