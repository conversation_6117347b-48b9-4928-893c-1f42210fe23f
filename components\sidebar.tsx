"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { PlusCircle, MessageSquare, History, Settings } from "lucide-react"

interface ConversationListItem {
  id: string;
  name: string;
}

interface SidebarProps {
  isCollapsed: boolean;
  conversationList: ConversationListItem[];
  currentConversationId: string;
  onSelectConversation: (id: string) => void;
  onCreateNewConversation: () => void;
}

// const initialConversations = [
//   { id: "1", name: "新对话", active: true },
//   { id: "2", name: "GPT-4测试", active: false },
//   { id: "3", name: "<PERSON>测试", active: false },
// ]

export default function Sidebar({ 
  isCollapsed, 
  conversationList, 
  currentConversationId, 
  onSelectConversation, 
  onCreateNewConversation 
}: SidebarProps) {
  // const [conversations, setConversations] = useState(initialConversations)

  // const setActiveConversation = (id: string) => {
  //   setConversations(
  //     conversations.map((conv) => ({
  //       ...conv,
  //       active: conv.id === id,
  //     })),
  //   )
  // }

  return (
    <div
      className={`bg-gray-50 dark:bg-gray-900 flex flex-col h-full transition-all duration-200 overflow-hidden border-r border-gray-200 dark:border-gray-800`}
    >
      <div className={`p-3 flex-shrink-0 ${isCollapsed ? 'px-2' : 'px-3'}`}>
        <Button 
          className={`w-full gap-2 ${isCollapsed ? 'justify-center' : 'justify-start'}`} 
          onClick={onCreateNewConversation} // 使用 prop
          title={isCollapsed ? "新建对话" : ""}
          variant={isCollapsed ? "outline" : "default"}
          size={isCollapsed ? "icon" : "default"}
        >
          <PlusCircle className="h-4 w-4 flex-shrink-0" />
          {!isCollapsed && <span>新建对话</span>}
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin">
        <div className={`${isCollapsed ? 'px-1' : 'px-3'} py-2`}>
          {!isCollapsed && <h3 className="text-xs font-semibold text-gray-500 mb-2 px-1">对话列表</h3>}
          {isCollapsed && <hr className="my-2 border-gray-300 dark:border-gray-700" />}
          <div className="space-y-1">
            {conversationList.map((conv) => (
              <Button
                key={conv.id}
                variant="ghost"
                title={isCollapsed ? conv.name : ""}
                className={`w-full text-left rounded-md flex items-center gap-2 ${                  isCollapsed ? 'px-2 justify-center h-9' : 'px-3 py-2'
                } ${                  conv.id === currentConversationId // 使用 currentConversationId 判断 active
                    ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200 font-medium"
                    : "text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
                onClick={() => onSelectConversation(conv.id)} // 使用 prop
              >
                <MessageSquare className="h-4 w-4 flex-shrink-0" />
                {!isCollapsed && <span className="truncate flex-1">{conv.name}</span>}
              </Button>
            ))}
          </div>
        </div>
      </div>

      <div className={`p-3 border-t border-gray-200 dark:border-gray-800 ${isCollapsed ? 'px-2' : 'px-3'}`}>
        <div className="flex flex-col space-y-1">
          <Button 
            variant="ghost" 
            className={`w-full gap-2 ${isCollapsed ? 'justify-center h-9' : 'justify-start py-2'}`}
            title={isCollapsed ? "历史记录" : ""}
          >
            <History className="h-4 w-4 flex-shrink-0" />
            {!isCollapsed && <span>历史记录</span>}
          </Button>
          <Button 
            variant="ghost" 
            className={`w-full gap-2 ${isCollapsed ? 'justify-center h-9' : 'justify-start py-2'}`}
            title={isCollapsed ? "设置" : ""}
          >
            <Settings className="h-4 w-4 flex-shrink-0" />
            {!isCollapsed && <span>设置</span>}
          </Button>
        </div>
      </div>
    </div>
  )
}
