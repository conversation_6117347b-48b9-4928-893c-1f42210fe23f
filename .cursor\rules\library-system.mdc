---
description: MeowAgent 采用可扩展的工具库架构，允许智能体调用各类功能模块。工具库系统由 @libraries/library.py 定义的基础架构提供支持。
globs: 
alwaysApply: false
---
# MeowAgent 工具库系统

MeowAgent 采用可扩展的工具库架构，允许智能体调用各类功能模块。工具库系统由 [libraries/library.py](mdc:meowagent/libraries/library.py) 定义的基础架构提供支持。

## 工具库基础架构

所有工具库都继承自 `Library` 抽象基类，该类提供：

- 工具自动发现与注册机制
- 工具元数据生成（基于函数签名和注释）
- 工具执行统一接口
- 动态提示词管理

关键设计：
1. 使用 `@register_tool` 装饰器标记可调用工具
2. 工具命名采用 "库名.工具名" 格式
3. 所有工具必须是异步函数
4. 通过 `function_schema` 自动提取参数模式

## 主要工具库

### 1. 记忆库

[memory.py](mdc:meowagent/libraries/memory.py) 提供文件系统式的长期记忆管理：

- 通过目录和文件组织记忆
- 支持创建、读取、写入、删除、搜索等操作
- 提供"标签页"机制快速访问常用内容
- 动态更新提示信息，实时反映记忆状态

```python
@register_tool
async def create_item(self, path: str, is_folder: bool = False, content: str = None) -> str:
    """创建新的文件或文件夹。如果是文件，可以提供初始内容。"""
    # 实现代码...
```

### 2. 文档库

[document.py](mdc:meowagent/libraries/document.py) 提供强大的文档管理和检索功能：

- 自动加载多种格式文档（.txt, .md, .py, .js, .json等）
- 支持语义搜索，智能回答与文档相关的查询
- 灵活的搜索策略：flat, map_reduce, map
- 集成长上下文语言模型

### 3. 工作流库

[workflow.py](mdc:meowagent/libraries/workflow.py) 实现任务引导和步骤控制：

- 支持YAML声明式和Python生成器两种工作流定义
- 提供多种操作类型：EXECUTE, CONDITION, GENERATE, JUMP等
- 内置状态管理、寄存器和动态提示词生成
- 工作流执行器（WorkflowExecutor）负责步骤验证和执行

### 4. MCP客户端库

[mcp_client.py](mdc:meowagent/libraries/mcp_client.py) 与外部工具服务器通信：

- 通过Model Context Protocol (MCP)连接外部工具
- 自动发现和注册远程工具
- 将工具调用请求转发给MCP服务器
- 管理连接生命周期

## 扩展工具库

要创建新的工具库，需要：

1. 继承 `Library` 基类
2. 使用 `@register_tool` 装饰器标记异步方法
3. 确保工具函数有明确的类型注解和文档字符串

```python
from libraries.library import Library, register_tool

class MyCustomLibrary(Library):
    """我的自定义工具库"""
    
    @register_tool
    async def my_tool(self, param1: str, param2: int = 0) -> str:
        """工具功能描述"""
        # 实现代码...
        return result
```

