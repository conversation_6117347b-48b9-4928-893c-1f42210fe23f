// export interface Message {
//   id: string
//   role: string
//   content: string
// }

// 新增：后端原始消息结构参考
export interface RawBackendMessage {
  type: string; // 后端发送的原始消息类型
  content: any; // 消息内容，可以是字符串或对象
  conversation_id?: string; // 对话ID
  timestamp?: string; // 时间戳字符串 (可能是ISO格式或自定义JSON格式)
  workflow_info?: WorkflowInfo; // 工作流信息
  // 特定于工具调用流的消息字段
  id?: string; // 消息或工具调用的 ID
  name?: string; // 例如工具名称
  arguments?: string; // 工具参数增量
}

// 新增：用于在控制台中显示工具调用的结构
export interface DisplayToolCall {
  id: string; // 工具调用ID
  name: string; // 工具名称
  args: string; // 工具参数 (累积的)
  status: 'streaming' | 'complete' | 'error'; // 统一 status 类型
}

// 修改/新增：控制台消息类型
// export interface ConsoleMessage { // This definition is superseded by the one below
//   messageId: string; // 前端生成，用于React key
//   conversationId?: string;
//   rawType: string; // 后端原始消息类型，用于逻辑判断
//   displayType: ConsoleMessageType; // 使用统一的 ConsoleMessageType
//   text?: string; // 主要的文本内容
//   data?: any; // 附加的结构化数据
//   timestamp: Date; // 时间戳对象
//   workflowInfo?: WorkflowInfo;
//   isStreaming?: boolean;
//   streamingToolCalls?: { [toolId: string]: DisplayToolCall } | null; // 允许 null
// }

// // 保留聊天消息类型 (如果已存在) // This section will be removed
// export interface Message { // This is the one we are keeping and modifying
//   id: string;
//   role: "system" | "user" | "assistant" | "tool"; // Updated role type
//   content: string;
//   tool_calls?: Array<{
//     id: string;
//     type: "function";
//     function: {
//       name:string;
//       arguments: string;
//     };
//   }>;
//   tool_call_id?: string;
//   timestamp?: string; // 后端消息时间戳，ISO格式
//   summary?: string; // 消息摘要，由后端生成
// }

// // 定义消息类型 // This section will be removed
// export interface Message {
//   id: string; // 消息的唯一ID (前端可能需要临时ID)
//   // role: string; // 移除 role
//   display_style: string; // 新增 display_style，由后端提供
//   content: string; // 消息内容
//   timestamp?: string; // 消息时间戳 (ISO 8601 格式字符串)
//   summary?: string | null; // 消息摘要 (可选)
//   tool_calls?: ToolCall[] | null; // 工具调用信息 (可选)
//   tool_call_id?: string | null; // 工具调用ID (用于ToolMessage)
//   // 可以根据需要添加其他元数据字段
//   metadata?: Record<string, any> | null;
// }

// // 定义工具调用类型 // This section will be removed
// export interface ToolCallFunction {
//   name: string;
//   arguments: string; // 参数通常是JSON字符串
// }

// export interface ToolCall { // This section will be removed
//   id: string;
//   type: 'function'; // 通常是 'function'
//   function: ToolCallFunction;
// }

// 定义控制台消息类型 (This is the primary ConsoleMessageType to keep)
export type ConsoleMessageType =
  | 'info'
  | 'warning'
  | 'error'
  | 'debug'
  | 'success'
  | 'request'
  | 'response'
  | 'user_input'
  | 'assistant_message' // 完整助手消息
  | 'assistant_stream' // AI助手回复的流式内容
  | 'reasoning_message' // AI助手完整的推理信息
  | 'reasoning_stream' // AI助手推理过程的流式内容
  | 'tool_call' // 已确认的工具调用信息
  | 'tool_call_stream' // 工具调用结构在流式形成中 (可能合并到assistant_stream的特殊渲染)
  | 'tool_result' // 工具执行结果
  | 'workflow_info' // 工作流步骤变化等信息
  | 'unknown';

export interface WorkflowInfo { // Keep this definition
  workflow_name: string;
  step_index: number | null; // YAML 是数字, Python 是 null
  step_name: string;
  step_operation: string | null;
}

// 定义前端控制台消息的结构 (This is the primary ConsoleMessage to keep)
export interface ConsoleMessage {
  messageId: string; // 前端生成的唯一ID
  conversationId?: string;
  rawType: string; // 后端原始类型
  displayType: ConsoleMessageType; // 前端用于分类显示的类型
  text?: string; // 用于显示的主要文本内容
  data?: any; // 原始数据对象 (可选, 用于调试)
  timestamp: Date; // 时间戳对象
  workflowInfo?: WorkflowInfo;
  isStreaming?: boolean; // 是否正在流式传输 (用于 assistant_stream/reasoning_stream)
  streamingToolCalls?: { [toolId: string]: DisplayToolCall } | null; // 正在流式传输的工具调用
}

// // 定义前端显示工具调用的结构 (用于流式) // This is redundant with the first DisplayToolCall
// export interface DisplayToolCall {
//   id: string;
//   name: string;
//   args: string;
//   status: 'streaming' | 'complete' | 'error';
// }

// ==================================================
// 重命名核心消息接口为 ChatMessageDisplay - This section is the primary definition for UI chat messages
// ==================================================
export interface ChatMessageDisplay { // KEEP THIS
  id: string;
  display_style: string;
  content: string;
  timestamp?: string;
  summary?: string | null | undefined;
  tool_calls?: ToolCall[] | null | undefined;      // 引用下面的 ToolCall
  tool_call_id?: string | null | undefined;
  metadata?: Record<string, any> | null | undefined;
  object?: string | null | undefined; // Optional: reflects backend Message class name
}
// ==================================================

// 定义工具调用相关类型 (供 ChatMessageDisplay 接口使用) - KEEP THESE
export interface ToolCallFunction { // KEEP THIS
  name: string;
  arguments: string;
}

export interface ToolCall { // KEEP THIS
  id: string;
  type: 'function';
  function: ToolCallFunction;
}

// OpenAI Formatted Message Types
export interface FormattedApiMessageToolCall {
  id: string;
  type: string; // e.g., "function"
  function: {
    name: string;
    arguments: string; // JSON string
  };
}

export interface FormattedApiMessage {
  role: string; // "system", "user", "assistant", "tool"
  content?: any | null;
  tool_calls?: FormattedApiMessageToolCall[];
  tool_call_id?: string; // For role="tool"
  name?: string; // For role="tool", the name of the function that was called
}
