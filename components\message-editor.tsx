"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { GripVertical, Save, X } from "lucide-react"
import type { Message } from "@/types/message"

interface MessageEditorProps {
  message: Message
  onSave: (message: Message) => void
  onCancel: () => void
  dragHandleProps?: {
    attributes?: Record<string, any>
    listeners?: Record<string, any>
  }
}

export default function MessageEditor({ message, onSave, onCancel, dragHandleProps }: MessageEditorProps) {
  const [editedContent, setEditedContent] = useState(message.content)

  const handleSave = () => {
    onSave({
      ...message,
      content: editedContent,
    })
  }

  const getStyleClass = (displayStyle: string) => {
    switch (displayStyle) {
      case "user":
        return "bg-blue-50 border-blue-200 dark:bg-blue-900 dark:border-blue-700"
      case "assistant_text":
      case "assistant_tool_call":
        return "bg-emerald-50 border-emerald-200 dark:bg-emerald-900 dark:border-emerald-700"
      case "system_compact":
      case "agent_system":
        return "bg-amber-50 border-amber-200 dark:bg-amber-900 dark:border-amber-700"
      case "tool_result":
        return "bg-purple-50 border-purple-200 dark:bg-purple-900 dark:border-purple-700"
      default:
        return "bg-gray-50 border-gray-300 dark:bg-gray-850 dark:border-gray-700"
    }
  }

  const isRightAligned = message.display_style === "user"
  const isFullWidthSystemStyle = message.display_style === "system_compact" || message.display_style === "agent_system"

  return (
    <div className={`flex ${isRightAligned ? "justify-end" : ""}`}>
      <div className={`${isFullWidthSystemStyle ? 'w-full' : 'max-w-[85%]'}`}>
        <div className={`rounded-lg border ${getStyleClass(message.display_style)} shadow-sm 
                      ${isRightAligned && !isFullWidthSystemStyle ? 'rounded-tr-none' : ''} 
                      ${!isRightAligned && !isFullWidthSystemStyle ? 'rounded-tl-none' : ''}
                    `}>
          <div className="flex items-center justify-between p-3 border-b border-gray-100 dark:border-gray-800">
            <div className="flex items-center gap-2">
              {dragHandleProps && (
                <div
                  {...(dragHandleProps.attributes || {})}
                  {...(dragHandleProps.listeners || {})}
                  className="cursor-grab p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <GripVertical className="h-4 w-4 text-gray-400" />
                </div>
              )}
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">编辑消息 ({message.display_style})</span>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className="h-8 px-3 text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 dark:hover:bg-emerald-800 dark:text-emerald-400"
              >
                <Save className="h-3.5 w-3.5 mr-1" />
                保存
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="h-8 px-3 text-gray-600 hover:text-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-gray-400"
              >
                <X className="h-3.5 w-3.5 mr-1" />
                取消
              </Button>
            </div>
          </div>
          <div className="p-3">
            <Textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="min-h-[100px] w-full border-0 focus:ring-0 p-0 text-sm resize-none bg-transparent dark:text-gray-200 placeholder:text-gray-400 dark:placeholder:text-gray-500"
              placeholder="输入消息内容..."
            />
          </div>
        </div>
      </div>
    </div>
  )
}
