"""
DocumentLibrary 单元测试

测试文档库的所有主要功能：
1. 文档加载
2. 列出文档
3. 获取文档内容
4. 文档搜索功能

本测试直接使用 documents/ 目录下的真实文档进行测试，不使用任何mock。
"""

import pytest
import pytest_asyncio
import os
import tempfile
import shutil

from libraries.document import DocumentLibrary

# 标记所有测试为异步测试
pytestmark = pytest.mark.asyncio


@pytest_asyncio.fixture
async def document_library():
    """创建一个 DocumentLibrary 实例用于测试"""
    # 使用默认配置，这会加载 documents/ 目录下的文档
    library = DocumentLibrary()
    yield library
    # 清理
    await library.close()


@pytest_asyncio.fixture
async def document_library_with_custom_docs():
    """创建一个使用自定义测试文档目录的 DocumentLibrary 实例"""
    # 创建临时测试目录
    test_docs_dir = tempfile.mkdtemp(prefix="test_docs_")
    
    try:
        # 创建测试文档
        test_doc1_content = """# 测试文档1

这是一个关于人工智能的测试文档。

## AI 技术发展

人工智能技术近年来发展迅速，特别是在自然语言处理领域。

### 大型语言模型

GPT、ChatGPT、Claude 等大型语言模型改变了人们与AI的交互方式。
这些模型能够理解和生成自然语言，在多个领域都有应用。

## 应用场景

- 客服机器人
- 内容创作
- 代码生成
- 文档分析
"""

        test_doc2_content = """# 测试文档2

这是一个关于编程技术的测试文档。

## Python 编程语言

Python 是一种高级编程语言，以其简洁易读的语法而闻名。

### Python 的特点

1. 简洁的语法
2. 强大的标准库
3. 丰富的第三方生态
4. 跨平台支持

## 常用库

- NumPy: 数值计算
- Pandas: 数据分析
- Django: Web 开发
- Flask: 轻量级 Web 框架

### 机器学习库

Python 在机器学习领域也有很多优秀的库：
- scikit-learn
- TensorFlow
- PyTorch
"""

        # 写入测试文档
        with open(os.path.join(test_docs_dir, "ai_test.md"), "w", encoding="utf-8") as f:
            f.write(test_doc1_content)
        
        with open(os.path.join(test_docs_dir, "python_test.md"), "w", encoding="utf-8") as f:
            f.write(test_doc2_content)
        
        # 创建一个非支持格式的文件（应该被忽略）
        with open(os.path.join(test_docs_dir, "ignore_me.bin"), "wb") as f:
            f.write(b"binary content")
        
        # 创建一个带有自定义文档目录的 DocumentLibrary
        # 通过直接修改实例的文档加载方法来使用测试目录
        library = DocumentLibrary()
        # 清空默认加载的文档
        library.documents.clear()
        # 重新从测试目录加载
        library._load_documents_from_folder(test_docs_dir)
            
        yield library, test_docs_dir
        
        # 清理
        await library.close()
        
    finally:
        # 删除临时目录
        shutil.rmtree(test_docs_dir, ignore_errors=True)


@pytest_asyncio.fixture
async def empty_document_library():
    """创建一个没有任何文档的 DocumentLibrary 实例"""
    # 创建空的临时目录
    empty_dir = tempfile.mkdtemp(prefix="empty_docs_")
    
    try:
        library = DocumentLibrary()
        # 清空默认加载的文档
        library.documents.clear()
        # 尝试从空目录加载（应该没有文档）
        library._load_documents_from_folder(empty_dir)
        
        yield library
        await library.close()
        
    finally:
        shutil.rmtree(empty_dir, ignore_errors=True)


class TestDocumentLibrary:
    """DocumentLibrary 测试类"""
    
    async def test_library_initialization(self, document_library):
        """测试库的初始化"""
        assert document_library.name == "document"
        assert document_library.description == "文档处理工具库"
        assert document_library.documents is not None
        assert isinstance(document_library.documents, dict)
        assert hasattr(document_library, 'model')
        assert hasattr(document_library, 'batch_processor')
        
    async def test_real_documents_loading(self, document_library):
        """测试加载真实的 documents/ 目录下的文档"""
        # 检查是否加载了 documents/ 目录下的文档
        # 根据项目结构，应该有 example1.md 和 example2.md
        assert len(document_library.documents) >= 2
        
        # 检查特定文档是否存在
        doc_names = list(document_library.documents.keys())
        assert "example1.md" in doc_names
        assert "example2.md" in doc_names
        
        # 检查文档内容不为空
        for doc_name, content in document_library.documents.items():
            assert content is not None
            assert len(content) > 0
            assert isinstance(content, str)
            
        # 检查文档内容包含预期的关键词
        example1_content = document_library.documents["example1.md"]
        assert "示例文档1" in example1_content
        assert "文档库功能" in example1_content
        
        example2_content = document_library.documents["example2.md"]
        assert "示例文档2" in example2_content
        assert "超长上下文" in example2_content
    
    async def test_custom_documents_loading(self, document_library_with_custom_docs):
        """测试加载自定义测试文档"""
        library, test_docs_dir = document_library_with_custom_docs
        
        # 应该加载了2个.md文件，忽略了.bin文件
        assert len(library.documents) == 2
        
        # 检查文档名称
        doc_names = list(library.documents.keys())
        assert "ai_test.md" in doc_names
        assert "python_test.md" in doc_names
        assert "ignore_me.bin" not in doc_names
        
        # 检查文档内容
        ai_content = library.documents["ai_test.md"]
        assert "人工智能" in ai_content
        assert "大型语言模型" in ai_content
        
        python_content = library.documents["python_test.md"]
        assert "Python" in python_content
        assert "机器学习库" in python_content
    
    async def test_list_documents_tool(self, document_library):
        """测试 list_documents 工具"""
        result = await document_library.list_documents()
        
        assert result["success"] is True
        assert "documents" in result
        assert "count" in result
        assert isinstance(result["documents"], list)
        assert result["count"] == len(result["documents"])
        assert result["count"] >= 2  # 至少应该有 example1.md 和 example2.md
        
        # 检查文档名称
        doc_names = result["documents"]
        assert "example1.md" in doc_names
        assert "example2.md" in doc_names
    
    async def test_get_document_content_tool(self, document_library):
        """测试 get_document_content 工具"""
        # 测试获取已存在的文档
        result = await document_library.get_document_content("example1.md")
        
        assert result["success"] is True
        assert "content" in result
        assert isinstance(result["content"], str)
        assert len(result["content"]) > 0
        
        # 检查内容是否包含预期的关键词
        content = result["content"]
        assert "示例文档1" in content
        assert "文档库功能" in content
        
    async def test_get_document_content_nonexistent(self, document_library):
        """测试获取不存在的文档内容"""
        result = await document_library.get_document_content("nonexistent.md")
        
        assert result["success"] is False
        assert "error" in result
        assert "找不到文档" in result["error"]
        assert "nonexistent.md" in result["error"]
        
    async def test_search_in_documents_basic(self, document_library_with_custom_docs):
        """测试基本搜索功能（可能需要真实 API 调用）"""
        library, _ = document_library_with_custom_docs
        
        # 这个测试会进行真实的 API 调用
        # 可以通过环境变量控制是否运行
        import os
        if not os.getenv("ENABLE_API_TESTS"):
            pytest.skip("跳过需要API调用的测试，设置 ENABLE_API_TESTS=1 环境变量来运行")
        
        # 执行搜索
        result = await library.search_in_documents("什么是人工智能？")
        
        # 验证结果结构
        assert result["success"] is True
        assert "answer" in result
        assert isinstance(result["answer"], str)
        assert len(result["answer"]) > 0
        
        # 由于是真实搜索，我们主要验证结构而不是具体内容
        print(f"搜索结果: {result['answer']}")
        
    async def test_search_strategy_execution(self, document_library_with_custom_docs):
        """测试不同搜索策略的执行（需要真实 API 调用）"""
        library, _ = document_library_with_custom_docs
        
        import os
        if not os.getenv("ENABLE_API_TESTS"):
            pytest.skip("跳过需要API调用的测试，设置 ENABLE_API_TESTS=1 环境变量来运行")
        
        query = "Python有什么特点？"
        
        # 测试 map 策略
        try:
            map_result = await library._search_in_documents_map(query)
            assert map_result["success"] is True
            assert "answer" in map_result
            print(f"Map策略结果: {map_result['answer']}")
        except Exception as e:
            print(f"Map策略测试失败: {e}")
        
        # 测试 map-reduce 策略
        try:
            map_reduce_result = await library._search_in_documents_map_reduce(query)
            assert map_reduce_result["success"] is True
            assert "answer" in map_reduce_result
            print(f"Map-Reduce策略结果: {map_reduce_result['answer']}")
        except Exception as e:
            print(f"Map-Reduce策略测试失败: {e}")
    
    async def test_get_library_details(self, document_library):
        """测试获取库详细信息"""
        details = await document_library.get_library_details()
        
        assert details["name"] == "document"
        assert details["description"] == "文档处理工具库"
        assert "tools" in details
        assert "tool_count" in details
        assert details["tool_count"] > 0
        
        # 检查工具列表
        tools = details["tools"]
        tool_names = [tool["name"] for tool in tools]
        
        expected_tools = [
            "document.get_document_content",
            "document.list_documents", 
            "document.search_in_documents"
        ]
        
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
    
    async def test_tool_registration(self, document_library):
        """测试工具注册功能"""
        # 检查所有预期的工具是否已注册
        tools = document_library.get_tools()
        tool_names = [tool["name"] for tool in tools]
        
        expected_tools = [
            "document.get_document_content",
            "document.list_documents",
            "document.search_in_documents"
        ]
        
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
        
        # 测试获取单个工具
        for tool_name in expected_tools:
            tool = document_library.get_tool(tool_name)
            assert tool is not None
            assert tool["name"] == tool_name
            assert "description" in tool
            assert "input_schema" in tool
    
    async def test_execute_tool_list_documents(self, document_library):
        """测试通过 execute_tool 执行 list_documents"""
        result = await document_library.execute_tool("document.list_documents", {})
        
        assert result["success"] is True
        assert "documents" in result
        assert isinstance(result["documents"], list)
        assert len(result["documents"]) >= 2
    
    async def test_execute_tool_get_document_content(self, document_library):
        """测试通过 execute_tool 执行 get_document_content"""
        # 首先获取文档列表
        list_result = await document_library.execute_tool("document.list_documents", {})
        assert list_result["success"] is True
        
        # 获取第一个文档的内容
        if list_result["documents"]:
            doc_name = list_result["documents"][0]
            result = await document_library.execute_tool(
                "document.get_document_content", 
                {"document_name": doc_name}
            )
            
            assert result["success"] is True
            assert "content" in result
            assert isinstance(result["content"], str)
            assert len(result["content"]) > 0
    
    async def test_empty_documents_folder(self, empty_document_library):
        """测试空文档文件夹的情况"""
        library = empty_document_library
        
        # 测试空文档库的行为
        list_result = await library.list_documents()
        assert list_result["success"] is True
        assert list_result["documents"] == []
        assert list_result["count"] == 0
        
        # 测试获取文档内容
        content_result = await library.get_document_content("any.md")
        assert content_result["success"] is False
        assert "未找到任何文档" in content_result["error"]
        
        # 测试搜索
        search_result = await library.search_in_documents("test query")
        assert search_result["success"] is False
        assert "未找到任何文档" in search_result["error"]
    
    async def test_nonexistent_documents_folder(self):
        """测试不存在的文档文件夹"""
        library = DocumentLibrary()
        # 清空默认加载的文档
        library.documents.clear()
        # 尝试从不存在的目录加载
        library._load_documents_from_folder("/path/that/does/not/exist")
        
        try:
            # 应该能正常初始化，但没有加载任何文档
            assert len(library.documents) == 0
            
            # 验证空库的行为
            list_result = await library.list_documents()
            assert list_result["success"] is True
            assert list_result["documents"] == []
            
        finally:
            await library.close()
    
    async def test_document_content_validation(self, document_library):
        """测试文档内容的完整性验证"""
        # 获取所有文档并验证内容质量
        list_result = await document_library.list_documents()
        assert list_result["success"] is True
        
        for doc_name in list_result["documents"]:
            content_result = await document_library.get_document_content(doc_name)
            assert content_result["success"] is True
            
            content = content_result["content"]
            # 验证内容不为空且有意义
            assert len(content.strip()) > 10  # 至少有一些实际内容
            assert not content.isspace()  # 不全是空白字符
            
            # 验证文档有基本的结构（如果是markdown）
            if doc_name.endswith('.md'):
                assert '#' in content or content.strip()  # 要么有标题，要么有内容
    
    async def test_batch_processor_configuration(self, document_library):
        """测试批处理器配置"""
        # 验证批处理器已正确初始化
        assert hasattr(document_library, 'batch_processor')
        assert document_library.batch_processor is not None
        
        # 验证配置参数
        assert hasattr(document_library, 'use_batch_generate')
        assert hasattr(document_library, 'filter_vote_count')
        assert isinstance(document_library.use_batch_generate, bool)
        assert isinstance(document_library.filter_vote_count, int)
        assert document_library.filter_vote_count > 0


class TestDocumentLibraryIntegration:
    """DocumentLibrary 集成测试"""
    
    async def test_real_search_with_actual_documents(self, document_library):
        """使用真实文档进行搜索测试（需要真实的 API 调用）"""
        # 这个测试会进行真实的 API 调用，可能会产生费用
        import os
        if not os.getenv("ENABLE_API_TESTS"):
            pytest.skip("跳过需要API调用的测试，设置 ENABLE_API_TESTS=1 环境变量来运行")
        
        # 执行真实搜索
        result = await document_library.search_in_documents("文档库有什么功能？")
        
        assert result["success"] is True
        assert "answer" in result
        assert isinstance(result["answer"], str)
        assert len(result["answer"]) > 0
        
        # 答案应该包含关于文档库功能的信息
        answer = result["answer"].lower()
        print(f"搜索答案: {result['answer']}")
        
        # 验证答案的相关性（真实搜索可能返回各种答案，这里只验证基本结构）
        assert len(answer) > 20  # 答案应该有一定的长度
    
    async def test_search_different_strategies_real(self, document_library):
        """测试不同的搜索策略（需要真实 API 调用）"""
        import os
        if not os.getenv("ENABLE_API_TESTS"):
            pytest.skip("跳过需要API调用的测试，设置 ENABLE_API_TESTS=1 环境变量来运行")
        
        query = "什么是超长上下文？"
        
        # 测试 map 策略
        map_result = await document_library._search_in_documents_map(query)
        assert map_result["success"] is True
        assert "answer" in map_result
        assert len(map_result["answer"]) > 0
        print(f"Map策略结果: {map_result['answer']}")
        
        # 测试 map-reduce 策略
        map_reduce_result = await document_library._search_in_documents_map_reduce(query)
        assert map_reduce_result["success"] is True
        assert "answer" in map_reduce_result
        assert len(map_reduce_result["answer"]) > 0
        print(f"Map-Reduce策略结果: {map_reduce_result['answer']}")
        
        # 两种策略都应该返回有意义的结果
        assert len(map_result["answer"]) > 10
        assert len(map_reduce_result["answer"]) > 10
    
    async def test_performance_with_multiple_documents(self, document_library):
        """测试多文档处理性能"""
        # 获取所有文档
        list_result = await document_library.list_documents()
        assert list_result["success"] is True
        
        document_count = len(list_result["documents"])
        print(f"测试文档数量: {document_count}")
        
        # 测试批量获取文档内容的性能
        import time
        start_time = time.time()
        
        for doc_name in list_result["documents"]:
            content_result = await document_library.get_document_content(doc_name)
            assert content_result["success"] is True
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"获取 {document_count} 个文档内容耗时: {elapsed_time:.2f} 秒")
        
        # 基本性能验证 - 每个文档的平均处理时间不应过长
        avg_time_per_doc = elapsed_time / document_count if document_count > 0 else 0
        assert avg_time_per_doc < 1.0  # 每个文档处理时间不应超过1秒 