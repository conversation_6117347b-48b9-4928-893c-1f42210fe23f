"""
该模块为 MeowAgent 框架中的分层记忆存储定义了核心抽象。

主要内容包括：

1.  `MemoryStorageBase` (抽象基类):
    定义了所有具体记忆存储后端必须实现的统一接口契约。这个接口的设计侧重于
    基于路径的节点（文件或文件夹）操作，以适应诸如文件系统或对象存储（如阿里云OSS）
    等原生支持路径寻址的存储系统。它取代了早期可能存在的基于数字ID的节点管理方式。
    该基类规范了以下操作：
    *   初始化存储后端 (`initialize`)。
    *   通过完整路径获取节点信息 (`get_node_by_path`)。
    *   获取指定父路径下的直接子节点 (`get_children_of_node`)。
    *   在指定路径创建新节点（文件或文件夹），并可选地写入初始内容 (`create_node`)。
    *   更新已存在文件节点的内容 (`update_node_content`)。
    *   读取指定路径文件的内容 (`read_file_content`)。
    *   重命名节点（文件或文件夹），名称指的是基本名称而非完整路径 (`rename_node`)。
    *   将节点移动到新的父目录下 (`move_node`)。
    *   递归删除指定路径的节点及其所有子内容 (`delete_node_recursive`)。
    *   检查指定的完整路径（文件或文件夹）是否存在 (`path_exists`)。
    *   规范化或返回给定节点路径的字符串表示 (`get_path_string_for_node`)。
    *   按名称在指定目录下（可选递归）搜索节点 (`search_nodes_by_name`)。
    *   在文件内容中搜索指定查询字符串（可选递归） (`search_file_contents`)。

2.  `NodeType` (类型别名):
    一个字典类型 (`Dict[str, Any]`)，用于定义在 `MemoryLibrary` 和存储后端之间传递的
    节点数据的标准结构。期望包含如 `name` (名称), `path` (完整路径), `node_type` ('file' 或 'folder'),
    `conversation_id` (对话ID), `updated_at` (更新时间), `created_at` (创建时间), 以及可选的
    `content` (文件内容) 等字段。

该模块的抽象设计允许 `MemoryLibrary` (记忆库) 以统一的方式与不同的持久化存储机制进行交互，
例如本地文件系统 (`memory_storage_fs.py`) 或云对象存储 (`memory_storage_oss.py`)。
开发者可以通过实现 `MemoryStorageBase` 接口来集成新的存储后端。
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any



# 定义节点数据结构 (字典形式)，用于存储后端和MemoryLibrary之间传递
# 注意：随着OSS原生实现的推进，NodeType中 'id' 和 'parent_id' 的含义对于不同存储后端可能会有差异。
# 对于OSS原生实现，'id' 可能就是其完整路径 'path'，而 'parent_id' 的概念可能被父路径取代。
NodeType = Dict[str, Any] # 建议包含: name, path (完整路径), node_type, conversation_id, updated_at, created_at, Optional[content]

class MemoryStorageBase(ABC):
    """记忆存储后端的抽象基类"""

    @abstractmethod
    async def initialize(self):
        """初始化存储后端 (例如，创建根目录，检查连接等)"""
        pass

    # get_node_by_id 和 get_node_by_path_segments 将被移除或替换
    # @abstractmethod
    # async def get_node_by_id(self, conversation_id: str, node_id: int) -> Optional[NodeType]:
    #     """通过ID获取节点"""
    #     pass

    @abstractmethod
    async def get_node_by_path(self, conversation_id: str, path: str) -> Optional[NodeType]:
        """通过完整路径获取节点信息（文件或文件夹）。路径是相对于用户根目录的。"""
        pass
    
    @abstractmethod
    async def get_children_of_node(
        self, conversation_id: str, parent_path: Optional[str] # 参数从 parent_id 变为 parent_path
    ) -> List[NodeType]:
        """获取指定父路径下的所有直接子节点。parent_path=None或'/'表示用户根目录。"""
        pass

    @abstractmethod
    async def create_node(
        self,
        conversation_id: str,
        path: str,             # 主要标识符变为 path
        node_type: str,        # 'file' 或 'folder'
        content: Optional[str] = None,
    ) -> Optional[NodeType]:
        """在指定路径创建新节点（文件或文件夹）。路径是相对于用户根目录的。"""
        pass

    @abstractmethod
    async def update_node_content(
        self, conversation_id: str, path: str, content: str # node_id 变为 path
    ) -> Optional[NodeType]:
        """更新文件节点的内容。路径必须指向一个已存在的文件。"""
        pass

    @abstractmethod
    async def read_file_content(self, conversation_id: str, path: str) -> Optional[str]:
        """读取指定路径文件的内容。路径必须指向一个已存在的文件。"""
        pass
    
    @abstractmethod
    async def rename_node(
        self, conversation_id: str, old_path: str, new_name: str # node_id 变为 old_path, new_name指新基本名称
    ) -> Optional[NodeType]:
        """重命名节点。对于文件夹，这可能涉及复杂操作。new_name是新的基本名称，不是完整路径。"""
        raise NotImplementedError("重命名节点功能尚未在所有后端普遍实现。")

    @abstractmethod
    async def move_node(
        self, conversation_id: str, source_path: str, target_parent_path: str # node_id 变为 source_path, new_parent_id 变为 target_parent_path
    ) -> Optional[NodeType]:
        """移动节点到新的父目录下。对于文件夹，这可能涉及复杂操作。"""
        raise NotImplementedError("移动节点功能尚未在所有后端普遍实现。")

    @abstractmethod
    async def delete_node_recursive(self, conversation_id: str, path: str) -> bool: # node_id 变为 path
        """递归删除指定路径的节点（文件或文件夹）及其所有子内容。"""
        pass

    # is_name_unique_in_parent 被 path_exists 替代，语义更清晰
    # @abstractmethod
    # async def is_name_unique_in_parent(
    #     self, conversation_id: str, parent_id: Optional[int], name: str
    # ) -> bool:
    #     """检查在指定父节点下，该名称是否已存在 (不区分文件或文件夹)"""
    #     pass

    @abstractmethod
    async def path_exists(self, conversation_id: str, path: str) -> bool:
        """检查指定的完整路径（文件或文件夹）是否存在。"""
        pass

    @abstractmethod
    async def get_path_string_for_node(self, conversation_id: str, path: Optional[str]) -> str: # 参数变为 path
        """
        (辅助方法) 返回或规范化给定路径字符串。对于原生路径存储，这可能只是返回传入的路径。
        如果path为None，代表根，应返回 "/"。
        """
        pass

    @abstractmethod
    async def search_nodes_by_name(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True # parent_id 变为 parent_path
    ) -> List[NodeType]:
        """
        按名称搜索节点 (模糊匹配或精确匹配)。
        parent_path: 限定在某个目录下搜索。None或'/'表示从用户根目录开始。
        recursive: 是否递归搜索子目录。
        """
        pass

    @abstractmethod
    async def search_file_contents(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True # parent_id 变为 parent_path
    ) -> List[NodeType]:
        """
        在文件内容中搜索。
        返回匹配的文件节点列表。
        """
        pass


