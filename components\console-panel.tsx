"use client"

import { /*useState,*/ useRef, useEffect, RefObject } from "react"
import { Button } from "@/components/ui/button"
import { Terminal, Trash2, Copy, Download, PanelLeftClose, PanelRightClose } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { ConsoleMessage, DisplayToolCall } from "@/types/message"

interface ConsolePanelProps {
  messages: ConsoleMessage[]
  scrollAreaRef: RefObject<HTMLDivElement | null>
  isCollapsed: boolean;
}

export default function ConsolePanel({ messages, scrollAreaRef, isCollapsed }: ConsolePanelProps) {
  const clearConsole = () => {
    console.log("Clear console requested. Parent should handle this.");
  }

  const copyConsole = () => {
    const text = messages.map((msg) => {
      let line = `[${msg.displayType}]`;
      if (msg.text) line += ` ${msg.text}`;
      if (msg.data) line += ` ${JSON.stringify(msg.data)}`;
      if (msg.streamingToolCalls) {
        line += `\n  Streaming Tools: ${Object.values(msg.streamingToolCalls).map(tc => `${tc.name}(${tc.args.substring(0,30)}...)`).join(", ")}`
      }
      return line;
    }).join("\n");
    navigator.clipboard.writeText(text);
  }

  const downloadLogs = () => {
    const text = messages.map((msg) => 
      `[${msg.timestamp.toISOString()}] [${msg.displayType}]` + 
      (msg.text ? ` ${msg.text}` : '') + 
      (msg.data ? ` DATA: ${JSON.stringify(msg.data)}` : '') +
      (msg.streamingToolCalls ? ` STREAMING_TOOLS: ${JSON.stringify(msg.streamingToolCalls)}` : '')
    ).join("\n");
    const blob = new Blob([text], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `console-logs-${new Date().toISOString().split("T")[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getMessageColor = (type: ConsoleMessage["displayType"]) => {
    switch (type) {
      case "info":
      case "workflow_info":
      case "debug":
        return "text-blue-600 dark:text-blue-400"
      case "error":
        return "text-red-600 dark:text-red-400"
      case "success":
        return "text-green-600 dark:text-green-400"
      case "request":
        return "text-purple-600 dark:text-purple-400"
      case "response":
        return "text-amber-600 dark:text-amber-400"
      case "user_input":
        return "text-cyan-600 dark:text-cyan-400"
      case "assistant_stream":
      case "assistant_message":
        return "text-emerald-600 dark:text-emerald-400"
      case "reasoning_stream":
      case "reasoning_message":
        return "text-sky-600 dark:text-sky-400"
      case "tool_call":
      case "tool_call_stream":
        return "text-yellow-600 dark:text-yellow-400"
      case "tool_result":
        return "text-fuchsia-600 dark:text-fuchsia-400"
      case "warning":
        return "text-orange-500 dark:text-orange-400"
      default:
        return "text-gray-600 dark:text-gray-400"
    }
  }

  const renderStreamingToolCalls = (calls: { [toolId: string]: DisplayToolCall } | undefined) => {
    if (!calls || Object.keys(calls).length === 0) return null;
    return (
      <div className="ml-4 mt-1 text-xs text-yellow-700 dark:text-yellow-500">
        正在构建工具调用:
        {Object.values(calls).map(tc => (
          <div key={tc.id}>- {tc.name || "(加载中...)"}({tc.args || "..."}){tc.status === 'streaming' ? '...' : ''}</div>
        ))}
      </div>
    );
  };

  return (
    <div
      className={`bg-gray-50 dark:bg-gray-900 flex flex-col h-full transition-all duration-200 overflow-hidden border-l border-gray-200 dark:border-gray-800`}
    >
      <div className={`flex items-center p-2 border-b border-gray-200 dark:border-gray-800 ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
        {!isCollapsed && (
          <>
            <div className="flex items-center gap-2">
              <Terminal className="h-4 w-4 text-blue-500" />
              <span className="font-medium text-sm">控制台</span>
            </div>
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="icon" className="h-7 w-7" title="复制日志" onClick={copyConsole}>
                <Copy className="h-3.5 w-3.5" />
              </Button>
              <Button variant="ghost" size="icon" className="h-7 w-7" title="下载日志" onClick={downloadLogs}>
                <Download className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900"
                title="清空控制台"
                onClick={clearConsole}
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            </div>
          </>
        )}
        {isCollapsed && (
          <Terminal className="h-5 w-5 text-gray-500" />
        )}
      </div>

      {!isCollapsed && (
        <ScrollArea 
          className="flex-1 p-2" 
          ref={scrollAreaRef}
          key="console-scroll-area" 
        >
          <div className="space-y-2">
            {messages.map((message) => (
              <div key={message.messageId} className="text-xs font-mono break-words">
                <div className="flex items-start gap-1">
                  <span className="text-gray-500 dark:text-gray-400 whitespace-nowrap">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })}
                  </span>
                  <span className={`font-semibold ${getMessageColor(message.displayType)}`}>
                    [{message.displayType}]{message.conversationId ? ` (${message.conversationId})` : ''}
                    {message.isStreaming ? " ..." : ""}
                  </span>
                </div>
                {(message.text || (message.displayType === 'assistant_stream' && message.isStreaming)) && (
                    <pre className="mt-1 whitespace-pre-wrap break-all bg-white dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200">
                      {message.text}
                      {message.displayType === 'assistant_stream' && message.isStreaming && !message.text && "(助手正在输入...)"}
                    </pre>
                )}
                {message.data && (
                  <details className="mt-1 text-xs">
                    <summary className="cursor-pointer text-gray-500 dark:text-gray-400">原始数据</summary>
                    <pre className="whitespace-pre-wrap break-all bg-gray-100 dark:bg-gray-850 p-2 rounded-md border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300">
                      {JSON.stringify(message.data, null, 2)}
                    </pre>
                  </details>
                )}
                {renderStreamingToolCalls(message.streamingToolCalls)}
                {message.workflowInfo && (
                  <div className="mt-1 text-xs text-purple-600 dark:text-purple-400">
                    工作流: {message.workflowInfo.workflow_name} - 步骤 {message.workflowInfo.step_index}: {message.workflowInfo.step_name} ({message.workflowInfo.step_operation})
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      )}
      {isCollapsed && (
        <div className="flex-1 flex items-center justify-center p-2">
          <p className="text-xs text-gray-400 dark:text-gray-500 transform -rotate-90 whitespace-nowrap origin-center">控制台已折叠</p>
        </div>
      )}
    </div>
  )
}
