"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react"
import Header from "@/components/header"
import Sidebar from "@/components/sidebar"
import Chat<PERSON><PERSON> from "@/components/chat-area"
import ConfigPanel from "@/components/config-panel"
import ConsolePanel from "@/components/console-panel"
import type { 
  ChatMessageDisplay, 
  ConsoleMessage, 
  RawBackendMessage, 
  // DisplayToolCall, // No longer directly used here for DB ops
  // ToolCall as MessageToolCall, // Types will be handled by API response
  // ToolCallFunction as MessageToolCallFunction // Types will be handled by API response
} from "@/types/message"
import { useConsoleWebSocket } from "@/hooks/useConsoleWebSocket"
import { API_BASE_URL } from "@/lib/config" // Removed DEFAULT_CONVERSATION_ID
import { ThemeProvider } from "@/components/theme-provider"
// import { connectToDatabase, HISTORY_COLLECTION_NAME } from "@/lib/mongodb" // REMOVE: MongoDB direct access
import { toast } from "@/hooks/use-toast"
// import { ScrollArea } from "@/components/ui/scroll-area" // Not directly used in this diff
// import { ObjectId } from "mongodb" // REMOVE: MongoDB specific type
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels" // 新增导入

interface ConversationListItem {
  id: string;
  name: string; // 前端生成的名称，例如 "对话 abc1..."
}

export default function Dashboard() {
  const [messages, setMessages] = useState<ChatMessageDisplay[]>([])
  const [isLoadingHistory, setIsLoadingHistory] = useState(true) // Initially true as we might load or create
  const [historyError, setHistoryError] = useState<string | null>(null)

  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null) // Initialize as null
  const [conversationList, setConversationList] = useState<ConversationListItem[]>([]); // Initialize as empty
  const [isSyncing, setIsSyncing] = useState(false) // Will be used for API call loading states

  const [selectedModel, setSelectedModel] = useState("gpt-4")
  const [temperature, setTemperature] = useState(0.7)
  const [maxLength, setMaxLength] = useState(2000)
  const [configPanelOpen, setConfigPanelOpen] = useState(false)

  const [consoleMessages, setConsoleMessages] = useState<ConsoleMessage[]>([])
  const [isStreamingHistoryLoad, setIsStreamingHistoryLoad] = useState(false); // 新增：用于流式加载历史的状态
  const activeConsoleStreamMessageId = useRef<string | null>(null)
  const consoleScrollAreaRef = useRef<HTMLDivElement | null>(null)
  const historyNeedsRefresh = useRef<boolean>(false)
  
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false); // 新增 Sidebar 折叠状态
  const [isConsolePanelCollapsed, setIsConsolePanelCollapsed] = useState(false); // 新增 ConsolePanel 折叠状态
  
  // 先定义handleCreateNewConversation函数
  const handleCreateNewConversation = useCallback(async (showToast = true) => {
    const newId = crypto.randomUUID().slice(0, 8);
    const newName = newId; 
    
    setConversationList(prevList => [{ id: newId, name: newName }, ...prevList].filter((v,i,a)=>a.findIndex(t=>(t.id === v.id))===i)); // Add and ensure unique
    setCurrentConversationId(newId);
    setMessages([]); 
    setIsLoadingHistory(false); 
    
    // 右侧格式化消息会在 chat-area 组件中通过 currentConversationId 变化自动清空

    if (showToast) {
      toast({ title: "新对话已创建", description: `已切换到对话 ${newName}` });
    }
    // No need to call fetchConversationList immediately, as the new ID might not be persisted yet.
    // It will appear once messages are saved under it and fetchConversationList is called again.
  }, []);
  
  // 从后端 API 加载对话列表
  const fetchConversationList = useCallback(async (initialLoad = false) => {
    try {
      const response = await fetch(`${API_BASE_URL}/conversations`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to load conversation list'}`);
      }
      const data = await response.json(); // Expects { conversation_ids: string[] }
      const fetchedIds: string[] = data.conversation_ids || [];
      
      const newConversationList = fetchedIds.map(id => ({
        id,
        name: id 
      }));
      
      setConversationList(newConversationList);

      if (initialLoad) { // Only on initial load, decide if a new conversation needs to be created
        // 无论是否有历史对话，总是创建一个新的对话
        await handleCreateNewConversation(false); // Don't trigger a toast here, it's an initial setup
      }

    } catch (error) {
      console.error("加载对话列表失败:", error);
      toast({
        title: "加载对话列表失败",
        description: `无法从API加载对话列表: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      if (initialLoad) {
        // 如果初始加载失败，也尝试创建一个新对话
        await handleCreateNewConversation(false);
      }
    }
  }, [handleCreateNewConversation]); // 添加handleCreateNewConversation作为依赖项

  // 从后端 API 加载对话历史记录
  const loadConversationHistory = useCallback(async () => {
    if (!currentConversationId) {
      console.warn("loadConversationHistory: currentConversationId is not set.");
      setMessages([]);
      setIsLoadingHistory(false);
      return;
    }
    setIsLoadingHistory(true);
    setHistoryError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to load history'}`);
      }
      const data = await response.json();
      
      // API 返回的 history 已经是 ChatMessageDisplay[] 兼容的格式
      // (后端 ApiMessage model 应该与前端 ChatMessageDisplay 匹配)
      // 后端 timestamp 是 ISO string，前端 ChatMessageDisplay.timestamp 也是 string
      const history: ChatMessageDisplay[] = data.history.map((msg: any) => ({
        ...msg,
        // Ensure timestamp is a string, API should provide it as ISO string
        timestamp: msg.timestamp || new Date().toISOString(), 
      }));

      setMessages(history);
      // toast({ title: "对话历史已加载", description: `从API加载了 ${history.length} 条消息。` });
    } catch (error) {
      console.error("加载对话历史失败:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setHistoryError(errorMessage);
      toast({
        title: "加载历史失败",
        description: `无法从API加载对话: ${errorMessage}`,
        variant: "destructive",
      });
      setMessages([]);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [currentConversationId]);

  const handleSelectConversation = useCallback((id: string) => {
    if (id !== currentConversationId) {
      setMessages([]); 
      setIsLoadingHistory(true); 
      setCurrentConversationId(id);
    }
  }, [currentConversationId]);

  // 消息列表变更处理函数 - 现在通过 API 更新
  // 这个函数由 MessageList 拖拽完成时调用，现在改名为 handleReorderMessages
  const handleReorderMessages = async (newOrderedMessages: ChatMessageDisplay[]) => {
    if (!currentConversationId) {
      toast({ title: "错误", description: "没有有效的对话ID，无法重新排序。", variant: "destructive" });
      return;
    }
    setIsSyncing(true);
    const originalMessages = [...messages]; // 保存原始消息列表以备回滚
    setMessages(newOrderedMessages); // 乐观更新UI

    const orderedMessageIds = newOrderedMessages.map(msg => msg.id);

    try {
      const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages/reorder`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ordered_message_ids: orderedMessageIds }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to reorder messages'}`);
      }
      // API 成功后，理论上UI已是最新，但可以考虑重新加载以确保一致性
      // await loadConversationHistory(); // 或者信任乐观更新
      toast({ title: "对话顺序已更新", description: "消息顺序已通过API同步到数据库。" });
    } catch (error) {
      console.error("通过API同步消息顺序失败:", error);
      setMessages(originalMessages); // 回滚乐观更新
      toast({
        title: "顺序同步失败",
        description: `无法将消息顺序更改保存到数据库: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      // 可以选择重新加载以获取服务器的权威状态
      await loadConversationHistory();
    } finally {
      setIsSyncing(false);
    }
  }

  // 新增：更新消息内容 (或其他字段) - 调用API
  const handleUpdateMessageContent = async (updatedMessage: ChatMessageDisplay) => {
    if (!currentConversationId) {
      toast({ title: "错误", description: "没有有效的对话ID，无法更新消息。", variant: "destructive" });
      return;
    }
    if (!updatedMessage || !updatedMessage.id) {
      toast({ title: "错误", description: "无效的消息数据，无法更新。", variant: "destructive" });
      return;
    }

    const originalMessages = [...messages];
    // 乐观更新UI
    setMessages(prevMessages => 
      prevMessages.map(msg => (msg.id === updatedMessage.id ? updatedMessage : msg))
    );
    setIsSyncing(true);

    // 准备要发送到API的payload，只包含允许更新的字段
    // 后端API的UpdateMessagePayload模型定义了可接受的字段
    const payload: Partial<ChatMessageDisplay> = {
      content: updatedMessage.content,
      summary: updatedMessage.summary,
      display_style: updatedMessage.display_style,
      // 如果允许更新 tool_calls 或 tool_call_id，也在这里添加
    };
    // 移除值为 undefined 的字段，API 通常期望不传递这些字段，而不是传递 null 或 undefined
    Object.keys(payload).forEach(key => payload[key as keyof typeof payload] === undefined && delete payload[key as keyof typeof payload]);


    try {
      const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages/${updatedMessage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to update message'}`);
      }
      
      const returnedUpdatedMessage = await response.json(); // API应返回更新后的消息

      // 使用API返回的权威数据更新UI，以防后端有进一步处理
      setMessages(prevMessages =>
        prevMessages.map(msg => (msg.id === returnedUpdatedMessage.id ? returnedUpdatedMessage : msg))
      );

      toast({ title: "消息已更新", description: "消息内容已通过API同步到数据库。" });
    } catch (error) {
      console.error("通过API更新消息内容失败:", error);
      setMessages(originalMessages); // 回滚乐观更新
      toast({
        title: "更新失败",
        description: `无法将消息更改保存到数据库: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      // 考虑重新加载以获取服务器的权威状态
      // await loadConversationHistory(); // 也可以选择不重新加载整个列表，而是只回滚这一个
    } finally {
      setIsSyncing(false);
    }
  };

  const parseTimestamp = (timestampStr: string | undefined): Date => {
    if (!timestampStr) return new Date();
    try {
      const d = new Date(timestampStr);
      if (isNaN(d.getTime())) { 
        return new Date();
      }
      return d;
    } catch (e) {
      return new Date();
    }
  };

  // 辅助函数 getObjectFromDisplayStyle 和 getRoleFromDisplayStyleForAPI 不再需要在此处用于数据库写入
  // 这些逻辑现在由后端处理

  const handleRawMessage = useCallback((rawMsg: RawBackendMessage) => {
    setConsoleMessages(prevMessages => {
      let newMessages = [...prevMessages];
      const timestamp = parseTimestamp(rawMsg.timestamp);
      const commonFields = {
        conversationId: rawMsg.conversation_id,
        workflowInfo: rawMsg.workflow_info,
        timestamp,
      };

      const generatedMessageId = Date.now().toString() + Math.random().toString(36).substring(2,9);
      // let messageIdToUse = rawMsg.id && !["assistant_delta_start", "assistant_delta", "reasoning_delta", "tool_call_start", "tool_call_delta"].includes(rawMsg.type) ? rawMsg.id : generatedMessageId;
      
      switch (rawMsg.type) {
        case "assistant_delta_start":
          activeConsoleStreamMessageId.current = generatedMessageId; 
          newMessages.push({
            ...commonFields,
            messageId: activeConsoleStreamMessageId.current,
            rawType: rawMsg.type,
            displayType: "assistant_stream",
            text: "",
            isStreaming: true,
            streamingToolCalls: {},
          });
          break;

        case "assistant_delta":
        case "reasoning_delta":
          if (activeConsoleStreamMessageId.current) {
            newMessages = newMessages.map(msg => {
              if (msg.messageId === activeConsoleStreamMessageId.current) {
                let newText = msg.text || "";
                let newDisplayType = msg.displayType as ConsoleMessage['displayType'];
                let currentRawType = msg.rawType;

                if (rawMsg.type === "reasoning_delta") {
                  if (currentRawType !== "reasoning_delta" && newDisplayType !== "reasoning_stream") {
                    newText += (newText.length > 0 ? "\n" : "") + "[推理过程]: ";
                  }
                  newDisplayType = "reasoning_stream";
                  currentRawType = "reasoning_delta";
                } else {
                  if (newDisplayType === "reasoning_stream" && currentRawType === "reasoning_delta") {
                    newText += "\n[助手回复]: "; 
                  }
                  newDisplayType = "assistant_stream";
                  currentRawType = "assistant_delta";
                }
                return { 
                  ...msg, 
                  text: newText + rawMsg.content, 
                  rawType: currentRawType,
                  displayType: newDisplayType,
                  isStreaming: true,
                };
              }
              return msg;
            });
          } else {
            newMessages.push({ ...commonFields, messageId: generatedMessageId, rawType: rawMsg.type, displayType: "info", text: `孤立的 ${rawMsg.type}: ${JSON.stringify(rawMsg.content)}` });
          }
          break;

        case "tool_call_start":
          if (activeConsoleStreamMessageId.current && rawMsg.id) {
            newMessages = newMessages.map(msg => {
              if (msg.messageId === activeConsoleStreamMessageId.current) {
                const toolCallId = rawMsg.id!;
                return {
                  ...msg,
                  streamingToolCalls: {
                    ...(msg.streamingToolCalls || {}),
                    [toolCallId]: { id: toolCallId, name: "", args: "", status: "streaming" },
                  },
                };
              }
              return msg;
            });
          }
          break;

        case "tool_call_delta":
          if (activeConsoleStreamMessageId.current && rawMsg.id) {
            newMessages = newMessages.map(msg => {
              if (msg.messageId === activeConsoleStreamMessageId.current && msg.streamingToolCalls) {
                const toolCallId = rawMsg.id!;
                const nameUpdate = rawMsg.name;
                const argDelta = rawMsg.arguments;
                const existingCall = msg.streamingToolCalls[toolCallId];
                if (existingCall) {
                  return {
                    ...msg,
                    streamingToolCalls: {
                      ...msg.streamingToolCalls,
                      [toolCallId]: {
                        ...existingCall,
                        name: nameUpdate || existingCall.name || "",
                        args: (existingCall.args || "") + (argDelta || ""),
                      },
                    },
                  };
                }
              }
              return msg;
            });
          }
          break;

        case "assistant_delta_done":
          if (activeConsoleStreamMessageId.current) {
            newMessages = newMessages.map(msg => 
              msg.messageId === activeConsoleStreamMessageId.current ? { ...msg, isStreaming: false } : msg
            );
            activeConsoleStreamMessageId.current = null;
            
            historyNeedsRefresh.current = true;
            setTimeout(() => {
              if (historyNeedsRefresh.current) {
                setIsStreamingHistoryLoad(true); // 标记为流式加载
                loadConversationHistory().finally(() => {
                    setIsStreamingHistoryLoad(false); // 加载结束后取消标记
                });
                historyNeedsRefresh.current = false;
              }
            }, 500); // Delay to allow backend to fully process and save
          }
          break;

        case "tool_call": // For console display only
          newMessages.push({
            ...commonFields,
            messageId: generatedMessageId,
            rawType: rawMsg.type,
            displayType: "tool_call",
            text: `[调用工具]: ${rawMsg.content?.name || '未知工具'}`,
            data: rawMsg.content,
          });
          break;

        case "tool_result": // For console display only
          newMessages.push({
            ...commonFields,
            messageId: generatedMessageId,
            rawType: rawMsg.type,
            displayType: "tool_result",
            text: `[工具结果 ${rawMsg.content?.name || ''}]: ${typeof rawMsg.content?.result === 'string' ? rawMsg.content.result : JSON.stringify(rawMsg.content?.result)}`,
            data: rawMsg.content,
          });
          break;
        
        case "reasoning": // For console display only
           newMessages.push({
            ...commonFields,
            messageId: generatedMessageId,
            rawType: rawMsg.type,
            displayType: "reasoning_message",
            text: `[推理过程]: ${rawMsg.content}`,
          });
          break;

        case "frontend_error":
        case "error":
          newMessages.push({ ...commonFields, messageId: generatedMessageId, rawType: rawMsg.type, displayType: "error", text: String(rawMsg.content) });
          break;
        case "frontend_info":
        case "info":
          newMessages.push({ ...commonFields, messageId: generatedMessageId, rawType: rawMsg.type, displayType: "info", text: String(rawMsg.content) });
          break;
        case "frontend_warning":
        case "warning":
          newMessages.push({ ...commonFields, messageId: generatedMessageId, rawType: rawMsg.type, displayType: "warning", text: String(rawMsg.content) });
          break;

        case "full_message_object": // Received when agent completes a full message
          historyNeedsRefresh.current = true;
          newMessages.push({
            ...commonFields, 
            messageId: generatedMessageId, 
            rawType: rawMsg.type, 
            displayType: "info", 
            text: `收到完整消息: ${rawMsg.content?.role || 'unknown'} - ${typeof rawMsg.content?.content === 'string' ? rawMsg.content.content.substring(0, 50) + '...' : '内容无法显示'}`,
            data: rawMsg.content
          });
          setTimeout(() => {
            if (historyNeedsRefresh.current) {
              setIsStreamingHistoryLoad(true); // 标记为流式加载
              loadConversationHistory().finally(() => {
                setIsStreamingHistoryLoad(false); // 加载结束后取消标记
              });
              historyNeedsRefresh.current = false;
            }
          }, 500);
          break;

        default:
          // console.warn("Unhandled raw message type for console:", rawMsg.type);
          const displayType = rawMsg.type === 'user_input' ? 'user_input' : 'info';
          newMessages.push({
            ...commonFields,
            messageId: generatedMessageId,
            rawType: rawMsg.type,
            displayType,
            text: `[${rawMsg.type}]: ${typeof rawMsg.content === 'string' ? rawMsg.content : JSON.stringify(rawMsg.content)}`,
            data: typeof rawMsg.content !== 'string' ? rawMsg.content : undefined,
          });
      }
      return newMessages;
    });
  }, [loadConversationHistory]); // Added loadConversationHistory to dependency array

  // 先定义handleRawMessage，然后再使用它创建WebSocket连接
  const { sendMessage: sendWsMessage, isConnected: wsConnected } = useConsoleWebSocket(handleRawMessage, currentConversationId);

  // handleAddMessage: Sends user input to backend via WebSocket, then reloads history
  const handleAddMessage = async (newMessageFromChatArea: Partial<ChatMessageDisplay>) => {
    if (!currentConversationId) {
      toast({ title: "错误", description: "没有有效的对话ID，无法发送消息。", variant: "destructive" });
      return;
    }
    if (!newMessageFromChatArea.content) {
      toast({ title: "错误", description: "消息内容不能为空。", variant: "destructive" });
      return;
    }

    // No optimistic UI update here for the main chat message list directly.
    // The list will refresh from API after agent processes and saves the message.
    // A temporary local echo could be done in ChatArea if immediate feedback is desired, separate from main `messages` state.
    setIsSyncing(true); // Indicate processing

    try {
      // 使用统一的sendWsMessage方法发送消息，移除对ws.current的直接引用
      sendWsMessage({
        type: "user_input",
        conversation_id: currentConversationId,
        content: newMessageFromChatArea.content, // Only send content and necessary fields
        stream_mode: true, // Assuming streaming is default for new inputs
      });
      toast({ title: "消息已发送给Agent", description: "等待Agent处理..." });
      
      // After sending, rely on `assistant_delta_done` or `full_message_object` 
      // in `handleRawMessage` to trigger `loadConversationHistory`.
    } catch (error) {
      console.error("发送消息给Agent失败:", error);
      toast({
        title: "发送失败",
        description: `无法将消息发送给Agent: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  }

  const handleRun = () => {
    const lastUserMessage = messages.filter(m => m.display_style === 'user').pop();
    let contentToSend: any = "执行当前工作流程"; 
    if (lastUserMessage && lastUserMessage.content) {
      contentToSend = lastUserMessage.content;
    }
    
    const messageToSend = {
      type: 'user_input',
      content: contentToSend, 
      conversation_id: currentConversationId, 
      stream_mode: true 
    };

    // 使用统一的sendWsMessage方法发送消息
    sendWsMessage(messageToSend);

    handleRawMessage({
        type: 'info',
        content: `用户触发运行。发送内容: "${typeof contentToSend === 'string' ? contentToSend.substring(0, 100) : JSON.stringify(contentToSend).substring(0, 100)}..."`,
        conversation_id: currentConversationId,
        timestamp: new Date().toISOString()
    });
  }

  useEffect(() => {
    if (currentConversationId) { // Only load history if there's a current conversation
      loadConversationHistory();
    } else {
      // If no currentConversationId, it implies we need to either pick one or create one.
      // This is handled by fetchConversationList's initialLoad logic.
      setIsLoadingHistory(false); // No history to load if no conversation is selected/exists
    }
  }, [currentConversationId, loadConversationHistory]);

  useEffect(() => {
    fetchConversationList(true); // Pass true for initial load check
    // eslint-disable-next-line react-hooks/exhaustive-deps 
  }, []); // Run only once on mount

  // 修复ScrollArea导致的无限更新问题，使用useCallback包装滚动函数
  const scrollToBottom = useCallback(() => {
    if (consoleScrollAreaRef.current) {
      const viewport = consoleScrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement;
      if (viewport) {
        viewport.scrollTop = viewport.scrollHeight;
      }
    }
  }, []);

  // 使用useEffect处理新消息到达时的滚动，避免频繁更新导致的问题
  useEffect(() => {
    // 使用requestAnimationFrame确保在下一帧渲染后滚动，减少渲染冲突
    let animationFrameId: number;
    const scrollTimeoutId = setTimeout(() => {
      animationFrameId = requestAnimationFrame(scrollToBottom);
    }, 50); // 短暂延迟确保内容已完全渲染
    
    return () => {
      clearTimeout(scrollTimeoutId);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [consoleMessages, scrollToBottom]); // 依赖于consoleMessages和scrollToBottom函数

  // 删除消息处理 - 调用API
  const handleDeleteMessage = async (messageId: string) => {
    if (!currentConversationId) {
      toast({ title: "错误", description: "没有有效的对话ID，无法删除消息。", variant: "destructive" });
      return;
    }
    
    const originalMessages = [...messages];
    // Optimistic UI update
    setMessages(prevMessages => prevMessages.filter(msg => msg.id !== messageId));
    setIsSyncing(true);

    try {
      const response = await fetch(`${API_BASE_URL}/conversations/${currentConversationId}/messages/${messageId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        // If API returns 204, response.json() will fail. Check status first.
        if (response.status === 204) { // Success case
          toast({ title: "消息已删除", description: "消息已从数据库中移除。" });
          // UI is already updated optimistically
          return; 
        }
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`API error (${response.status}): ${errorData.detail || 'Failed to delete message'}`);
      }
      // For 204 No Content, response.ok is true, but there's no body to parse.
      toast({ title: "消息已删除", description: "消息已从数据库中移除。" });
      // UI already updated
      
    } catch (error) {
      console.error("删除消息失败:", error);
      setMessages(originalMessages); // Rollback optimistic update
      toast({
        title: "删除失败",
        description: `无法从数据库删除消息: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
      // Consider reloading history to ensure consistency
      await loadConversationHistory();
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex flex-col h-screen">
        <Header onOpenConfig={() => setConfigPanelOpen(true)} />
        <div className="flex flex-1 overflow-hidden">
          <PanelGroup direction="horizontal" className="flex-1 flex overflow-hidden">
            <Panel 
              defaultSize={20} 
              minSize={10} 
              collapsible={true} 
              collapsedSize={4} 
              className="h-full !overflow-y-auto scrollbar-thin"
              onCollapse={() => setIsSidebarCollapsed(true)} // 更新状态
              onExpand={() => setIsSidebarCollapsed(false)}   // 更新状态
            >
              <Sidebar 
                isCollapsed={isSidebarCollapsed} 
                conversationList={conversationList}
                currentConversationId={currentConversationId}
                onSelectConversation={handleSelectConversation}
                onCreateNewConversation={handleCreateNewConversation}
              />
            </Panel>
            <PanelResizeHandle className="w-1.5 bg-gray-200 dark:bg-gray-800 hover:bg-blue-500 dark:hover:bg-blue-600 transition-colors duration-200 cursor-col-resize flex items-center justify-center">
              <div className="w-px h-6 bg-gray-400 dark:bg-gray-600"></div>
            </PanelResizeHandle>
            <Panel defaultSize={55} minSize={30} className="h-full flex flex-col !overflow-y-auto scrollbar-thin"> {/* ChatArea Panel */}
              {historyError ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-red-500 mb-2">加载对话历史失败</p>
                    <p className="text-gray-600 text-sm">{historyError}</p>
                    <button 
                      onClick={loadConversationHistory}
                      className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      重试
                    </button>
                  </div>
                </div>
              ) : (
                <ChatArea
                  messages={messages}
                  onMessagesChange={handleReorderMessages}
                  onAddMessage={handleAddMessage}
                  onRun={handleRun}
                  onDeleteMessage={handleDeleteMessage}
                  onUpdateMessage={handleUpdateMessageContent}
                  isSyncing={isSyncing || isStreamingHistoryLoad || isLoadingHistory}
                  currentConversationId={currentConversationId}
                />
              )}
            </Panel>
            <PanelResizeHandle className="w-1.5 bg-gray-200 dark:bg-gray-800 hover:bg-blue-500 dark:hover:bg-blue-600 transition-colors duration-200 cursor-col-resize flex items-center justify-center">
              <div className="w-px h-6 bg-gray-400 dark:bg-gray-600"></div>
            </PanelResizeHandle>
            <Panel 
              defaultSize={25} 
              minSize={10} 
              collapsible={true} 
              collapsedSize={3} 
              className="h-full !overflow-y-auto scrollbar-thin"
              onCollapse={() => setIsConsolePanelCollapsed(true)} // 更新状态
              onExpand={() => setIsConsolePanelCollapsed(false)}  // 更新状态
            >
              <ConsolePanel
                messages={consoleMessages}
                scrollAreaRef={consoleScrollAreaRef}
                isCollapsed={isConsolePanelCollapsed} 
              />
            </Panel>
          </PanelGroup>
        </div>
        <ConfigPanel
          model={selectedModel}
          onModelChange={setSelectedModel}
          temperature={temperature}
          onTemperatureChange={setTemperature}
          maxLength={maxLength}
          onMaxLengthChange={setMaxLength}
          isOpen={configPanelOpen}
          onOpenChange={setConfigPanelOpen}
        />
      </div>
    </ThemeProvider>
  )
}
