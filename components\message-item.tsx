"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { GripVertical, Trash2, Edit, ChevronDown, ChevronUp } from "lucide-react"
import type { ChatMessageDisplay } from "@/types/message"

const INITIAL_MAX_HEIGHT_PX = 120
const EXPANDED_MAX_HEIGHT_PX = 400

interface MessageItemProps {
  message: ChatMessageDisplay
  onEdit: () => void
  onDelete: () => void
  isDragging?: boolean
  dragHandleProps?: {
    attributes?: Record<string, any>
    listeners?: Record<string, any>
  }
  showDragHandle?: boolean
}

export default function MessageItem({
  message,
  onEdit,
  onDelete,
  isDragging = false,
  dragHandleProps,
  showDragHandle = false,
}: MessageItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)

  const isRightAligned = message.display_style === "user";
  const isFullWidthSystemStyle = message.display_style === "system_compact" || message.display_style === "agent_system";

  useEffect(() => {
    const currentContent = contentRef.current
    if (currentContent) {
      setTimeout(() => {
        if (currentContent.scrollHeight > currentContent.clientHeight) {
          setIsOverflowing(true)
        } else {
          setIsOverflowing(false)
        }
      }, 0)
    }
  }, [message.content, isExpanded])

  const toggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  const getStyleClass = (displayStyle: string) => {
    switch (displayStyle) {
      case "user":
        return "bg-blue-50 border-blue-200 dark:bg-blue-900 dark:border-blue-700"
      case "assistant_text":
      case "assistant_tool_call":
        return "bg-emerald-50 border-emerald-200 dark:bg-emerald-900 dark:border-emerald-700"
      case "system_compact":
      case "agent_system":
        return "bg-amber-50 border-amber-200 dark:bg-amber-900 dark:border-amber-700"
      case "tool_result":
        return "bg-purple-50 border-purple-200 dark:bg-purple-900 dark:border-purple-700"
      case "error_message":
        return "bg-red-50 border-red-200 dark:bg-red-900 dark:border-red-700"
      case "info_log":
        return "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600"
      default:
        return "bg-gray-50 border-gray-300 dark:bg-gray-850 dark:border-gray-700"
    }
  }

  const getDisplayIcon = (displayStyle: string) => {
    switch (displayStyle) {
      case "user":
        return "👤"
      case "assistant_text":
      case "assistant_tool_call":
        return "🤖"
      case "system_compact":
      case "agent_system":
        return "⚙️"
      case "tool_result":
        return "🛠️"
      case "error_message":
        return "⚠️"
      case "info_log":
        return "ℹ️"
      default:
        return "📝"
    }
  }

  const getDisplayLabel = (displayStyle: string) => {
    switch (displayStyle) {
      case "user":
        return "用户"
      case "assistant_text":
        return "助手"
      case "assistant_tool_call":
        return "助手 (工具调用)"
      case "system_compact":
        return "系统"
      case "agent_system":
        return "系统 (Agent)"
      case "tool_result":
        return "工具结果"
      case "error_message":
        return "错误"
      case "info_log":
        return "信息"
      default:
        return displayStyle
    }
  }

  const DragHandle = showDragHandle ? (
    <div
      {...(dragHandleProps?.attributes || {})}
      {...(dragHandleProps?.listeners || {})}
      className="cursor-grab p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 mt-0.5"
    >
      <GripVertical className="h-4 w-4 text-gray-400" />
    </div>
  ) : null

  const renderMessageContent = () => (
    <div className={`flex-1`}>
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-1.5">
          <span className="text-sm">{getDisplayIcon(message.display_style)}</span>
          <span className="font-medium text-sm">{getDisplayLabel(message.display_style)}</span>
        </div>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onEdit}
            title="编辑"
            className="h-6 w-6 text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <Edit className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onDelete}
            title="删除"
            className="h-6 w-6 text-red-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-800 dark:text-red-500"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
      <div 
        ref={contentRef}
        className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 text-sm relative scrollbar-thin"
        style={{
          maxHeight: isExpanded ? `${EXPANDED_MAX_HEIGHT_PX}px` : `${INITIAL_MAX_HEIGHT_PX}px`,
          overflowY: isExpanded ? "auto" : "hidden",
        }}
      >
        {message.content}
      </div>
      {(isOverflowing || isExpanded) && (
        <Button
          variant="link"
          size="sm"
          onClick={toggleExpand}
          className="mt-1 px-0 h-auto text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center text-xs"
        >
          {isExpanded ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />}
        </Button>
      )}
      {message.display_style === "assistant_tool_call" && message.tool_calls && (
        <div className="mt-2 p-2 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-xs font-semibold mb-1 text-gray-600 dark:text-gray-400">工具调用:</h4>
          {message.tool_calls.map((tc) => (
            <div key={tc.id} className="text-xs p-1.5 bg-gray-100 dark:bg-gray-800 rounded-md mb-1">
              <p><strong>ID:</strong> {tc.id}</p>
              <p><strong>函数:</strong> {tc.function.name}</p>
              <pre className="whitespace-pre-wrap text-xs bg-gray-200 dark:bg-gray-700 p-1 rounded mt-0.5">{tc.function.arguments}</pre>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className={`flex ${isRightAligned ? "justify-end" : ""}`}>
      <div className={`${isFullWidthSystemStyle ? 'w-full' : 'max-w-[85%]'}`}>
        <div
          className={`rounded-lg p-3 shadow-sm border flex items-start 
                    ${getStyleClass(message.display_style)} 
                    ${isRightAligned && !isFullWidthSystemStyle ? 'rounded-tr-none mr-2' : ''} 
                    ${!isRightAligned && !isFullWidthSystemStyle ? 'rounded-tl-none ml-2' : ''}
                    ${isDragging ? 'opacity-75 shadow-xl' : ''} 
                  `}
        >
          {!isRightAligned && showDragHandle && DragHandle} 
          {renderMessageContent()}
          {isRightAligned && showDragHandle && DragHandle}
        </div>
      </div>
    </div>
  );
}
