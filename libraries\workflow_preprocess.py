"""
工作流定义预处理模块。

该模块提供了一系列函数，用于在工作流执行之前对其定义进行规范化和增强。
主要目标是确保工作流步骤具有明确的索引、跳转关系清晰，并补充必要的默认行为，
从而简化工作流执行引擎的处理逻辑。

核心功能包括：
1.  **步骤索引分配与转换**：
    -   `preprocess_workflow` 函数会为没有明确 `index` 属性的步骤自动分配一个唯一的整数索引。
        它会基于现有步骤中的最大索引值来生成新索引，以避免冲突。
    -   同时，它会构建一个从步骤 `name` 到对应 `index` 的映射关系。
2.  **基于名称的跳转解析**：
    -   在 `preprocess_workflow` 函数中，会将步骤定义中的 `next`、`true_branch`、`false_branch` 等
        跳转字段（如果其值为字符串类型的步骤名称）转换为对应的步骤索引。
    -   如果引用的步骤名称在映射中找不到，会记录错误日志。
3.  **自动填充顺序跳转**：
    -   `auto_fill_next_steps` 函数负责为那些没有明确 `next` 跳转目标且非特殊类型（如 `CONDITION` 或 `HALT`）
        的步骤，自动设置其 `next` 属性为按索引排序的下一个步骤的索引。
        这简化了线性流程的定义。
4.  **缓存管理**：
    -   两个主要的预处理函数在修改工作流定义后，都会清除工作流对象上可能存在的 `_parsed_steps` 缓存，
        以确保后续操作能获取到最新的、经过预处理的步骤信息。

通过这些预处理步骤，原始的、可能较为松散的工作流定义被转换为一种更结构化、
更明确的内部表示，为工作流的可靠执行奠定基础。
"""
from typing import Dict, Any, List, Optional, Mapping
from log import logger
from libraries.workflow_models import WorkflowDefinition


def preprocess_workflow(workflow: WorkflowDefinition) -> WorkflowDefinition:
    """
    预处理工作流，处理步骤索引和基于名称的跳转。
    
    主要功能:
    1. 为无index的步骤分配唯一索引
    2. 构建name -> index映射
    3. 将基于名称的跳转转换为基于索引的跳转
    
    参数:
        workflow: 工作流定义对象
        
    返回:
        处理后的工作流定义
    """
    if not workflow.steps:
        return workflow
        
    logger.info(f"开始预处理工作流: {workflow.description}")
    logger.info(f"步骤总数: {len(workflow.steps)}")
        
    # 1. 为无index的步骤分配唯一索引，并构建name -> index映射
    name_to_index: Dict[str, int] = {}
    next_auto_index = 0  # 用于自动分配索引
    
    # 首先查找已有的最大索引，防止冲突
    existing_indices: List[int] = []
    for step in workflow.steps:
        if "index" in step and isinstance(step["index"], int):
            existing_indices.append(step["index"])
            next_auto_index = max(next_auto_index, step["index"] + 1)
    
    logger.info(f"现有索引: {existing_indices if existing_indices else '无'}")
    logger.info(f"下一个自动索引值: {next_auto_index}")
    
    # 处理每个步骤
    steps_without_index = 0
    
    for step in workflow.steps:
        # 如果步骤没有index，分配一个唯一的索引
        if "index" not in step:
            step["index"] = next_auto_index
            next_auto_index += 1
            steps_without_index += 1
            logger.info(f"为步骤 '{step.get('name', '未命名')}' 自动分配索引: {step['index']}")
        
        # 如果步骤有name，记录到映射中
        if "name" in step and step["name"]:
            name_to_index[step["name"]] = step["index"]
    
    logger.info(f"自动分配索引的步骤数: {steps_without_index}")
            
    # 打印映射表方便调试
    if name_to_index:
        logger.info(f"步骤名称到索引的映射: {name_to_index}")
    
    # 2. 遍历步骤，处理基于名称的跳转字段
    jump_fields = ["next", "true_branch", "false_branch"]
    converted_jumps = 0
    
    for step in workflow.steps:
        for field in jump_fields:
            if field in step and isinstance(step[field], str):
                # 如果字段值是字符串（步骤名），尝试转换为对应的索引
                step_name = step[field]
                if step_name in name_to_index:
                    old_value = step[field]
                    step[field] = name_to_index[step_name]
                    converted_jumps += 1
                    logger.info(f"步骤 '{step.get('name', step.get('index'))}' 的 {field} 从 '{old_value}' 转换为索引 {step[field]}")
                else:
                    logger.error(f"[严重错误] 步骤 '{step.get('name', step.get('index'))}' 引用了不存在的步骤名 '{step_name}'，请检查YAML定义！")
    
    logger.info(f"转换的基于名称的跳转数: {converted_jumps}")
    logger.info(f"工作流预处理完成")
    
    # 清除解析缓存，强制重新解析
    workflow._parsed_steps = None
                        
    return workflow


def auto_fill_next_steps(workflow: WorkflowDefinition) -> None:
    """
    自动为缺少next字段的步骤补全默认跳转到下一个步骤索引。
    仅对非CONDITION和HALT类型的步骤生效。
    
    参数:
        workflow: 工作流定义对象
    """
    if not workflow.steps:
        return
    
    # 构建 index -> step 的映射
    index_map: Dict[int, Dict[str, Any]] = {}
    for step in workflow.steps:
        if "index" in step:
            index_map[step["index"]] = step
    
    sorted_indices = sorted(index_map.keys())
    for idx, current_index in enumerate(sorted_indices):
        step = index_map[current_index]
        op = step.get("operation", "")
        # 跳过CONDITION和HALT
        if op in ("CONDITION", "HALT"):
            continue
        # 已有next则跳过
        if "next" in step:
            continue
        # 查找下一个索引
        if idx + 1 < len(sorted_indices):
            next_index = sorted_indices[idx + 1]
            step["next"] = next_index
            # 记录日志方便调试
            logger.debug(f"自动为步骤 {step.get('name', current_index)} 设置 next={next_index}")
    
    # 清除解析缓存，强制重新解析
    workflow._parsed_steps = None
        