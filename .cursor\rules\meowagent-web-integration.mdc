---
description: 
globs: 
alwaysApply: false
---
# MeowAgent-Web 与后端集成指南

本文档描述MeowAgent Web前端与MeowAgent后端框架的集成方式，帮助开发者理解两者之间的交互机制。

## 架构概览

MeowAgent Web前端作为可视化界面，与MeowAgent Python后端通过API进行通信：

```
┌────────────────┐      HTTP/WebSocket      ┌────────────────┐
│  MeowAgent Web │ ◄─────────────────────► │ MeowAgent API  │
│   (Next.js)    │                          │   (FastAPI)    │
└────────────────┘                          └────────────────┘
```

## 通信协议

### WebSocket连接
前端使用WebSocket实现与后端的实时双向通信，主要用于：
- 流式接收模型返回结果
- 获取实时日志和状态更新
- 发送用户指令和消息

WebSocket连接基于[api.py](mdc:meowagent/meowagent/api.py)中的FastAPI实现，前端使用标准WebSocket API或库进行连接。

### REST API
前端使用REST API进行非实时操作：
- 获取可用模型列表
- 管理智能体配置
- 上传/下载对话历史
- 用户认证和授权

## 数据交换格式

前端和后端之间使用JSON作为数据交换格式，主要数据结构包括：

### 消息格式
```typescript
interface Message {
  id: string;
  role: string; // "system" | "user" | "assistant" | "tool"
  content: string;
  // 可能包含其他元数据
}
```

与后端[message.py](mdc:meowagent/meowagent/message.py)中的消息类型对应。

### 智能体配置
```typescript
interface AgentConfig {
  model: string;
  temperature: number;
  maxLength: number;
  tools: Tool[];
  // 其他配置参数
}
```

对应后端[config.py](mdc:meowagent/meowagent/config.py)中的配置结构。

## 前端集成实现

前端需要实现以下功能与后端集成：

1. **WebSocket连接管理**
   - 建立连接
   - 心跳保持
   - 错误处理和重连
   - 消息序列化和反序列化

2. **用户界面状态同步**
   - 将后端状态反映到UI
   - 将UI操作转换为API调用

3. **智能体控制**
   - 启动/停止智能体
   - 发送消息
   - 配置工具和参数

## 开发建议

1. 创建专用的API客户端模块，封装所有与后端的通信
2. 使用TypeScript接口确保前后端数据类型一致
3. 实现错误处理和重试机制
4. 添加加载状态指示器提升用户体验
5. 考虑网络延迟和断连情况的处理
