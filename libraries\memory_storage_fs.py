"""
该模块定义了基于文件系统的记忆存储实现。
"""
import asyncio
import json
import os
import shutil # 用于递归删除
from datetime import datetime
from typing import Optional, List, Dict, Any

import config as config_module
from log import logger
from libraries.memory_storage import MemoryStorageBase, NodeType # 从主模块导入基类和类型


class FSStorage(MemoryStorageBase):
    """
    使用本地文件系统作为记忆存储后端。
    """
    
    def __init__(self):
        self.root_dir = config_module.MEMORY_FS_ROOT_DIR
        logger.info(f"文件系统存储 (FSStorage) 已初始化，根目录: {self.root_dir}")

    async def _get_user_dir(self, conversation_id: str, create: bool = False) -> str:
        """获取用户目录路径，如果需要则创建。"""
        user_dir = os.path.join(self.root_dir, conversation_id)
        if create and not os.path.exists(user_dir):
            os.makedirs(user_dir, exist_ok=True)
        return user_dir

    async def initialize(self):
        """初始化文件系统存储。确保根用户目录存在。"""
        os.makedirs(self.root_dir, exist_ok=True)
        logger.info(f"文件系统存储 (FSStorage) 初始化完成。根目录: {self.root_dir}")

    async def get_node_by_path(self, conversation_id: str, path: str) -> Optional[NodeType]:
        """通过完整路径获取节点信息（文件或文件夹）。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))
        
        if not os.path.exists(full_fs_path):
            return None
            
        name = os.path.basename(full_fs_path.rstrip('/')) # 获取基本名称
        if not name and path.strip('/') == "": name = "/" # 用户根目录的特殊情况
        if not name and full_fs_path == user_base_dir.rstrip('/'): name = "/" # 另一种根表示
        
        node_type = "folder" if os.path.isdir(full_fs_path) else "file"
        # content = None # 内容不再在此处预加载
        # if node_type == 'file':
        #     try:
        #         with open(full_fs_path, 'r', encoding='utf-8') as f:
        #             content = f.read()
        #     except Exception as e:
        #         logger.error(f"[文件系统存储] 读取文件内容 '{full_fs_path}' 时出错: {e}")
                # return None # 或者返回节点信息但内容为None/错误标记

        try:
            stat_info = os.stat(full_fs_path)
            created_at = datetime.fromtimestamp(stat_info.st_ctime).isoformat()
            updated_at = datetime.fromtimestamp(stat_info.st_mtime).isoformat()
        except Exception as e:
            logger.error(f"[文件系统存储] 获取路径 '{full_fs_path}' 的时间戳失败: {e}")
            created_at, updated_at = None, None

        return {
            "name": name,
            "path": path, # 存储相对用户根的原始请求路径
            "node_type": node_type,
            "conversation_id": conversation_id,
            "created_at": created_at,
            "updated_at": updated_at,
            "content": None # 显式将content设为None，由read_file_content按需加载
        }

    async def get_children_of_node(
        self, conversation_id: str, parent_path: Optional[str]
    ) -> List[NodeType]:
        """获取指定父路径下的所有直接子节点。"""
        results: List[NodeType] = []
        user_base_dir = await self._get_user_dir(conversation_id)
        
        current_fs_dir: str
        if parent_path is None or parent_path == "/" or parent_path == "":
            current_fs_dir = user_base_dir
            parent_path_for_children = "/" # 用于构造子节点的相对路径
        else:
            current_fs_dir = os.path.join(user_base_dir, parent_path.lstrip('/'))
            parent_path_for_children = parent_path.rstrip('/') + '/' 
            if parent_path_for_children == "//": parent_path_for_children = "/"

        if not os.path.isdir(current_fs_dir):
            logger.warning(f"[文件系统存储] 父路径 '{current_fs_dir}' (来自 '{parent_path}') 不是一个目录。")
            return []

        try:
            for item_name in os.listdir(current_fs_dir):
                child_relative_path = (parent_path_for_children + item_name).replace('//','/')
                node_data = await self.get_node_by_path(conversation_id, child_relative_path)
                if node_data:
                    results.append(node_data)
        except Exception as e:
            logger.error(f"[文件系统存储] 列出目录 '{current_fs_dir}' 时出错: {e}")
        
        results.sort(key=lambda x: (x.get('node_type', 'file') != 'folder', x.get('name', '')))
        return results

    async def create_node(
        self,
        conversation_id: str,
        path: str,             # 主要标识符变为 path
        node_type: str,        # 'file' 或 'folder'
        content: Optional[str] = None,
    ) -> Optional[NodeType]:
        """在指定路径创建新节点（文件或文件夹）。"""
        user_base_dir = await self._get_user_dir(conversation_id, create=True) # 确保用户目录存在
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))

        # 检查父目录是否存在，如果不存在则创建 (os.makedirs会处理)
        parent_dir_of_new_node = os.path.dirname(full_fs_path)
        if not os.path.exists(parent_dir_of_new_node):
            try:
                os.makedirs(parent_dir_of_new_node, exist_ok=True)
            except Exception as e_mkdir:
                 logger.error(f"[文件系统存储] 创建父目录 '{parent_dir_of_new_node}' 失败: {e_mkdir}")
                 return None
        
        if os.path.exists(full_fs_path):
            logger.warning(f"[文件系统存储] 创建节点失败：路径 '{full_fs_path}' 已存在。")
            return None # 或者根据覆盖逻辑决定

        try:
            if node_type == 'file':
                with open(full_fs_path, 'w', encoding='utf-8') as f:
                    f.write(content if content is not None else "")
            elif node_type == 'folder':
                os.makedirs(full_fs_path, exist_ok=True) # exist_ok=True 以防万一（理论上上面的检查会处理）
            else:
                logger.error(f"[文件系统存储] 无效的节点类型: {node_type}")
                return None
            
            # 创建成功后，获取节点信息返回
            return await self.get_node_by_path(conversation_id, path)
            
        except Exception as e:
            logger.error(f"[文件系统存储] 创建路径 '{full_fs_path}' (类型: {node_type}) 时出错: {e}")
            # 如果部分创建失败，可能需要清理 (例如，文件创建了但文件夹创建失败)
            if os.path.exists(full_fs_path) and node_type == 'file': # 清理错误创建的文件
                 try: os.remove(full_fs_path) 
                 except: pass
            return None

    async def update_node_content(
        self, conversation_id: str, path: str, content: str
    ) -> Optional[NodeType]:
        """更新文件节点的内容。路径必须指向一个已存在的文件。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))

        if not os.path.isfile(full_fs_path):
            logger.warning(f"[文件系统存储] 更新内容失败：路径 '{full_fs_path}' 不是一个文件或不存在。")
            return None
        
        try:
            with open(full_fs_path, 'w', encoding='utf-8') as f:
                f.write(content)
            # 更新成功后，获取节点信息返回
            return await self.get_node_by_path(conversation_id, path)
        except Exception as e:
            logger.error(f"[文件系统存储] 更新文件 '{full_fs_path}' 内容时出错: {e}")
            return None
        
    async def rename_node(
        self, conversation_id: str, old_path: str, new_name: str
    ) -> Optional[NodeType]:
        """重命名节点。FS实现使用os.rename。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        old_full_fs_path = os.path.join(user_base_dir, old_path.lstrip('/'))

        if not os.path.exists(old_full_fs_path):
            logger.warning(f"[文件系统存储] 重命名失败：旧路径 '{old_full_fs_path}' 未找到。")
            return None
        
        # 构建新路径
        parent_dir_of_old = os.path.dirname(old_full_fs_path)
        new_full_fs_path = os.path.join(parent_dir_of_old, new_name)
        
        if os.path.exists(new_full_fs_path):
            logger.warning(f"[文件系统存储] 重命名失败：新路径 '{new_full_fs_path}' 已存在。")
            return None
            
        try:
            os.rename(old_full_fs_path, new_full_fs_path)
            # 计算相对于用户根的新路径
            new_relative_path = "/" + os.path.relpath(new_full_fs_path, user_base_dir).replace('\\','/')
            return await self.get_node_by_path(conversation_id, new_relative_path)
        except Exception as e:
            logger.error(f"[文件系统存储] 重命名从 '{old_full_fs_path}' 到 '{new_full_fs_path}' 时出错: {e}")
            return None

    async def move_node(
        self, conversation_id: str, source_path: str, target_parent_path: str
    ) -> Optional[NodeType]:
        """移动节点到新的父目录下。FS实现使用shutil.move。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        source_full_fs_path = os.path.join(user_base_dir, source_path.lstrip('/'))

        if not os.path.exists(source_full_fs_path):
            logger.warning(f"[文件系统存储] 移动失败：源路径 '{source_full_fs_path}' 未找到。")
            return None

        target_parent_full_fs_path: str
        if target_parent_path == "/" or not target_parent_path:
            target_parent_full_fs_path = user_base_dir
        else:
            target_parent_full_fs_path = os.path.join(user_base_dir, target_parent_path.lstrip('/'))
        
        if not os.path.isdir(target_parent_full_fs_path):
            logger.warning(f"[文件系统存储] 移动失败：目标父路径 '{target_parent_full_fs_path}' 不是有效目录。")
            return None
            
        # 构建最终的目标路径
        source_basename = os.path.basename(source_full_fs_path.rstrip('/'))
        destination_full_fs_path = os.path.join(target_parent_full_fs_path, source_basename)

        if os.path.exists(destination_full_fs_path):
            logger.warning(f"[文件系统存储] 移动失败：目标路径 '{destination_full_fs_path}' 已存在。")
            return None
            
        try:
            shutil.move(source_full_fs_path, destination_full_fs_path)
            new_relative_path = "/" + os.path.relpath(destination_full_fs_path, user_base_dir).replace('\\','/')
            return await self.get_node_by_path(conversation_id, new_relative_path)
        except Exception as e:
            logger.error(f"[文件系统存储] 从 '{source_full_fs_path}' 移动到 '{destination_full_fs_path}' 时出错: {e}")
            return None

    async def delete_node_recursive(self, conversation_id: str, path: str) -> bool:
        """递归删除指定路径的节点（文件或文件夹）及其所有子内容。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))

        if not os.path.exists(full_fs_path):
            logger.warning(f"[文件系统存储] 删除失败：路径 '{full_fs_path}' 未找到。")
            return False
        
        try:
            if os.path.isfile(full_fs_path):
                os.remove(full_fs_path)
            elif os.path.isdir(full_fs_path):
                shutil.rmtree(full_fs_path)
            else:
                logger.warning(f"[文件系统存储] 路径 '{full_fs_path}' 既不是文件也不是目录，无法删除。")
                return False
            logger.info(f"[文件系统存储] 成功删除路径 '{full_fs_path}'")
            return True
        except Exception as e:
            logger.error(f"[文件系统存储] 删除路径 '{full_fs_path}' 时出错: {e}")
            return False

    async def path_exists(self, conversation_id: str, path: str) -> bool:
        """检查指定的完整路径（文件或文件夹）是否存在。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))
        return os.path.exists(full_fs_path)

    async def get_path_string_for_node(self, conversation_id: str, path: Optional[str]) -> str:
        """对于FS存储，直接返回或规范化传入的路径字符串。"""
        if path is None or not path.strip(): # 修改这里，处理空字符串或仅含空格的字符串
            return "/"
        p = path.strip()
        if not p.startswith('/'):
            p = '/' + p
        # 避免多个前导斜杠，除非它就是 "/"
        while p.startswith('//') and len(p) > 1:
            p = p[1:]
        logger.debug(f"[文件系统存储] get_path_string_for_node: 对于路径 '{path}', 返回规范化尝试 '{p}'")
        return p
    
    async def search_nodes_by_name(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True
    ) -> List[NodeType]:
        """按名称搜索节点。FS实现使用os.walk或os.listdir。"""
        results: List[NodeType] = []
        user_base_dir = await self._get_user_dir(conversation_id)

        search_root_fs_path: str
        base_search_relative_path: str # 用于构造结果中的相对路径

        if parent_path is None or parent_path == "/" or parent_path == "":
            search_root_fs_path = user_base_dir
            base_search_relative_path = "/"
        else:
            search_root_fs_path = os.path.join(user_base_dir, parent_path.lstrip('/'))
            base_search_relative_path = parent_path.rstrip('/') + '/'
            if base_search_relative_path == "//": base_search_relative_path = "/"

        if not os.path.isdir(search_root_fs_path):
            logger.warning(f"[文件系统存储] 搜索名称：父路径 '{search_root_fs_path}' (来自 '{parent_path}') 不是目录。")
            return []

        query_lower = query.lower()

        if recursive:
            for dirpath, dirnames, filenames in os.walk(search_root_fs_path):
                # 检查当前目录名自身
                current_dir_name = os.path.basename(dirpath)
                if query_lower in current_dir_name.lower():
                    # 避免将搜索根目录自身重复添加（如果它的名称匹配）
                    if dirpath != search_root_fs_path or (dirpath == search_root_fs_path and parent_path is not None and parent_path != "/"):
                        relative_dir_path_to_user = "/" + os.path.relpath(dirpath, user_base_dir).replace('\\','/')
                        node = await self.get_node_by_path(conversation_id, relative_dir_path_to_user)
                        if node: results.append(node)
                
                for filename in filenames:
                    if query_lower in filename.lower():
                        full_file_path = os.path.join(dirpath, filename)
                        relative_file_path_to_user = "/" + os.path.relpath(full_file_path, user_base_dir).replace('\\','/')
                        node = await self.get_node_by_path(conversation_id, relative_file_path_to_user)
                        if node: results.append(node)
                # os.walk的dirnames会被修改以控制遍历深度，但此处我们完全递归
        else: # 非递归，只检查 search_root_fs_path 下的直接子项
            try:
                for item_name in os.listdir(search_root_fs_path):
                    if query_lower in item_name.lower():
                        # 构造子项的相对路径进行get_node_by_path调用
                        child_relative_path = (base_search_relative_path + item_name).replace('//','/')
                        node = await self.get_node_by_path(conversation_id, child_relative_path)
                        if node: results.append(node)
            except Exception as e_ls:
                logger.error(f"[文件系统存储] 非递归搜索名称时列出目录 '{search_root_fs_path}' 出错: {e_ls}")
        
        # 去重并排序
        unique_results = {item['path']: item for item in results}.values()
        final_results = sorted(list(unique_results), key=lambda x: (x.get('node_type', 'file') != 'folder', x.get('name', '')))
        return final_results

    async def search_file_contents(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True
    ) -> List[NodeType]:
        """在文件内容中搜索。FS实现遍历文件并读取内容。"""
        results: List[NodeType] = []
        user_base_dir = await self._get_user_dir(conversation_id)

        search_root_fs_path: str
        if parent_path is None or parent_path == "/" or parent_path == "":
            search_root_fs_path = user_base_dir
        else:
            search_root_fs_path = os.path.join(user_base_dir, parent_path.lstrip('/'))

        if not os.path.isdir(search_root_fs_path):
            logger.warning(f"[文件系统存储] 搜索内容：父路径 '{search_root_fs_path}' (来自 '{parent_path}') 不是目录。")
            return []

        query_lower = query.lower()

        if recursive:
            for dirpath, _, filenames in os.walk(search_root_fs_path):
                for filename in filenames:
                    full_file_path = os.path.join(dirpath, filename)
                    try:
                        with open(full_file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        if query_lower in content.lower():
                            relative_file_path_to_user = "/" + os.path.relpath(full_file_path, user_base_dir).replace('\\','/')
                            node = await self.get_node_by_path(conversation_id, relative_file_path_to_user)
                            if node: results.append(node) # get_node_by_path会重新读取内容，可以优化
                    except Exception as e_read:
                        logger.debug(f"[文件系统存储] 搜索内容时读取或处理文件 '{full_file_path}' 失败: {e_read}")
        else: # 非递归
            try:
                for item_name in os.listdir(search_root_fs_path):
                    full_item_path = os.path.join(search_root_fs_path, item_name)
                    if os.path.isfile(full_item_path):
                        try:
                            with open(full_item_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            if query_lower in content.lower():
                                relative_file_path_to_user = "/" + os.path.relpath(full_item_path, user_base_dir).replace('\\','/')
                                node = await self.get_node_by_path(conversation_id, relative_file_path_to_user)
                                if node: results.append(node)
                        except Exception as e_read_nr:
                            logger.debug(f"[文件系统存储] 非递归搜索内容时读取或处理文件 '{full_item_path}' 失败: {e_read_nr}")
            except Exception as e_ls_nr:
                 logger.error(f"[文件系统存储] 非递归搜索内容时列出目录 '{search_root_fs_path}' 出错: {e_ls_nr}")

        # 排序（注意：如果get_node_by_path在结果中未包含content，则需要在此处填充）
        # 当前的get_node_by_path会读取文件内容，所以NodeType是完整的
        return sorted(results, key=lambda x: x.get('updated_at', ''), reverse=True) 

    async def read_file_content(self, conversation_id: str, path: str) -> Optional[str]:
        """读取指定路径文件的内容。"""
        user_base_dir = await self._get_user_dir(conversation_id)
        full_fs_path = os.path.join(user_base_dir, path.lstrip('/'))

        if not os.path.isfile(full_fs_path):
            logger.warning(f"[文件系统存储] 路径 '{full_fs_path}' 不是一个文件或不存在，无法读取内容。")
            return None
        
        try:
            with open(full_fs_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"[文件系统存储] 读取文件 '{full_fs_path}' 内容时出错: {e}")
            return None 