---
description:
globs:
alwaysApply: false
---
# MeowAgent-Web 组件系统

本文档详细介绍MeowAgent Web界面的组件系统，遵循组件化和可重用性原则设计。

## UI组件库

项目使用[Radix UI](https://www.radix-ui.com/)作为底层组件库，配合Tailwind CSS构建自定义UI组件。主要UI组件都位于`components/ui/`目录下。

## 核心组件详解

### 仪表盘组件
[components/dashboard.tsx](mdc:meowagent-web/components/dashboard.tsx) - 应用的主容器组件
- 状态管理：管理消息列表、模型配置和面板状态
- 布局结构：整合侧边栏、聊天区域和控制台面板
- 用户交互：处理消息添加、运行智能体和配置更改等操作

### 消息处理组件
- [components/message-list.tsx](mdc:meowagent-web/components/message-list.tsx): 消息容器，支持拖拽排序
- [components/message-item.tsx](mdc:meowagent-web/components/message-item.tsx): 单个消息项，处理不同角色的消息展示
- [components/message-editor.tsx](mdc:meowagent-web/components/message-editor.tsx): 消息编辑器，支持内容修改和角色切换
- [components/add-message-button.tsx](mdc:meowagent-web/components/add-message-button.tsx): 添加新消息的按钮组件
- [components/sortable-message.tsx](mdc:meowagent-web/components/sortable-message.tsx): 可排序消息容器，提供拖拽功能

### 交互区域组件
- [components/chat-area.tsx](mdc:meowagent-web/components/chat-area.tsx): 聊天主区域，包含消息列表和输入区
- [components/console-panel.tsx](mdc:meowagent-web/components/console-panel.tsx): 控制台面板，显示日志和执行结果
- [components/config-panel.tsx](mdc:meowagent-web/components/config-panel.tsx): 配置面板，提供模型参数设置

### 导航组件
- [components/header.tsx](mdc:meowagent-web/components/header.tsx): 顶部导航栏，提供全局操作
- [components/sidebar.tsx](mdc:meowagent-web/components/sidebar.tsx): 侧边栏，提供会话管理和导航

### 智能体控制组件
- [components/agent-dashboard.tsx](mdc:meowagent-web/components/agent-dashboard.tsx): 智能体仪表盘，展示智能体状态和控制

## 组件设计原则
1. **组件化设计**：每个组件负责单一功能，便于维护和扩展
2. **状态提升**：主要状态在dashboard组件中管理，通过props传递给子组件
3. **事件委托**：子组件通过回调函数将事件传递给父组件处理
4. **响应式布局**：使用Tailwind CSS的响应式类确保在不同设备上正常显示

## 组件交互流程
1. 用户在聊天区域输入消息或修改现有消息
2. 消息通过`onAddMessage`或`onMessagesChange`回调传递给Dashboard
3. Dashboard更新状态，重新渲染相关组件
4. 用户点击"运行"按钮，触发智能体执行
5. 执行结果显示在控制台和聊天区域
