---
description:
globs:
alwaysApply: false
---
# MeowAgent-Web 开发指南

本文档提供MeowAgent Web界面的开发指南，包括项目设置、开发规范和最佳实践。

## 项目基础

- **框架**: Next.js 15 (App Router)
- **UI库**: Radix UI + Tailwind CSS
- **状态管理**: React Hooks
- **部署**: Vercel

## 开发环境设置

1. 安装依赖:
   ```bash
   pnpm install
   ```

2. 启动开发服务器:
   ```bash
   pnpm dev
   ```

3. 构建生产版本:
   ```bash
   pnpm build
   ```

## 项目配置文件

- [package.json](mdc:meowagent-web/package.json): 项目依赖和脚本
- [next.config.mjs](mdc:meowagent-web/next.config.mjs): Next.js配置
- [tailwind.config.ts](mdc:meowagent-web/tailwind.config.ts): Tailwind CSS配置
- [tsconfig.json](mdc:meowagent-web/tsconfig.json): TypeScript配置

## 代码规范与最佳实践

### 目录结构
- `app/`: 页面路由和布局
- `components/`: 可重用组件
- `hooks/`: 自定义React Hooks
- `lib/`: 工具函数和通用库
- `public/`: 静态资源
- `styles/`: 全局样式
- `types/`: TypeScript类型定义

### 组件开发规范
1. 使用函数组件和React Hooks
2. 组件文件使用PascalCase命名，如`MessageItem.tsx`
3. 为所有props定义TypeScript接口
4. 使用Tailwind CSS进行样式设计
5. 实现响应式设计，确保移动端兼容性

### 状态管理
- 局部状态使用`useState`和`useContext`
- 复杂状态考虑使用状态管理库

### 性能优化
- 使用`memo`避免不必要的重渲染
- 通过`useCallback`和`useMemo`优化性能
- 实现懒加载和代码分割

## 与MeowAgent后端集成

Web前端通过API与MeowAgent后端通信：
1. WebSocket连接实现实时通信
2. RESTful API获取智能体状态和配置
3. 前端将用户输入转换为API请求发送到后端

## 测试
- 使用Jest进行单元测试
- 使用React Testing Library测试组件

## 部署流程
项目自动部署到Vercel，每次提交到main分支都会触发构建和部署。
