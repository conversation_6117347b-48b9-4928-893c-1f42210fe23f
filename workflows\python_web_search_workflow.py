from typing import Dict, Any, AsyncGenerator
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "基础网络搜索工作流（Python实现），通过浏览器和网络请求获取信息"
INITIAL_REGISTERS: Dict[str, Any] = {}

# --- 工作流生成器函数 ---
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    """
    Python 实现的基础网络搜索工作流步骤生成器。
    处理用户请求，通过网络搜索获取信息，并生成回复。
    """

    while True:
        # 等待用户输入搜索需求
        user_message: UserMessage = yield UserInputStep(
            description="等待用户输入需要搜索的内容或问题"
        )

        # 步骤 1: 使用浏览器和网络工具获取信息
        yield ExecuteStep(
            description="通过使用浏览器和网络请求，从网络中获取信息",
            actions=[
                ActionDefinition(
                    names=["playwright"], 
                    min_calls=1, 
                    max_calls=1
                ),
                ActionDefinition(
                    names=["fetch.fetch"], 
                    min_calls=1, 
                    max_calls=1
                )
            ],
        )

        # 步骤 2: 检查任务是否完成
        is_task_complete: bool = yield ConditionStep(
            description="检查任务是否完成，是否已经获取到足够的信息来回答用户的问题",
        )

        # 如果任务未完成，继续搜索
        if not is_task_complete:
            # 返回到浏览器使用步骤，继续搜索更多信息
            yield ExecuteStep(
                description="继续使用浏览器获取更多信息",
                actions=[
                    ActionDefinition(
                        names=["playwright"], 
                        min_calls=1, 
                        max_calls=3
                    ),
                    ActionDefinition(
                        names=["fetch.fetch"], 
                        min_calls=1, 
                        max_calls=3
                    )
                ],
            )

        # 步骤 3: 生成回复
        yield GenerateStep(
            description="结合网络搜索结果生成回复给用户",
        ) 