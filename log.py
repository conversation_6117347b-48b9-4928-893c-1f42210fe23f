"""
该模块提供了一个轻量级的日志记录功能，旨在通过控制台输出不同级别的、带有颜色标记的日志信息，
以增强开发和调试过程中的可读性与信息区分度。

主要功能与实现：

1.  `Logger` 类:
    *   核心日志记录器类，封装了所有日志输出逻辑。
    *   提供了五种预定义的日志级别方法：
        *   `info(message)`: 输出一般信息，默认为青色。
        *   `success(message)`: 输出成功操作信息，默认为绿色。
        *   `warning(message)`: 输出警告信息，默认为黄色，并自动添加"警告:"前缀。
        *   `error(message, show_traceback=False, exc_info=False)`: 输出错误信息，默认为红色，并自动添加"错误:"前缀。可选参数 `show_traceback` 或 `exc_info` (两者效果相同) 用于在打印错误消息后显示当前的异常堆栈跟踪。
        *   `debug(message)`: 输出调试信息，默认为蓝色，并自动添加"调试:"前缀。
    *   使用 `colorama` 库 (具体为 `Fore` 和 `Style`) 来实现跨平台的彩色终端输出。
    *   `colorama.init(autoreset=True)` 在模块加载时被调用，确保颜色设置在每次打印后自动重置，避免颜色污染后续的终端输出。

2.  全局日志实例 `logger`:
    *   在模块级别创建了一个 `Logger` 类的单例实例，名为 `logger`。
    *   该实例可以直接从其他模块导入并使用 (例如 `from log import logger`)，提供了一个便捷的全局访问点来进行日志记录。

使用方法:
开发者可以在项目的任何部分导入 `logger` 对象，并调用其相应的方法来记录不同类型的事件和信息。
例如: `logger.info("系统初始化完成")` 或 `logger.error("关键操作失败", show_traceback=True)`。

依赖:
-   `colorama`: 用于实现终端文本的彩色输出。
-   `traceback`: 用于在 `error` 方法中打印异常的堆栈信息。
"""

import colorama
from colorama import Fore, Style
import traceback

# 初始化 colorama
colorama.init(autoreset=True)

class Logger:
    """
    简单的日志类，提供带颜色的控制台输出
    """
    def __init__(self):
        pass
    
    def info(self, message):
        """
        输出一般信息（青色）
        """
        print(f"{Fore.CYAN}{message}{Style.RESET_ALL}")
    
    def success(self, message):
        """
        输出成功信息（绿色）
        """
        print(f"{Fore.GREEN}{message}{Style.RESET_ALL}")
    
    def warning(self, message):
        """
        输出警告信息（黄色）
        """
        print(f"{Fore.YELLOW}警告: {message}{Style.RESET_ALL}")
    
    def critical(self, message, show_traceback=False):
        """
        输出严重错误信息（洋红色）
        
        参数:
            message: 严重错误消息
            show_traceback: 是否显示堆栈跟踪
        """
        print(f"{Fore.MAGENTA}严重: {message}{Style.RESET_ALL}")
        if show_traceback:
            traceback.print_exc()
    
    def error(self, message, show_traceback=False):
        """
        输出错误信息（红色）
        
        参数:
            message: 错误消息
            show_traceback: 是否显示堆栈跟踪
        """
        print(f"{Fore.RED}错误: {message}{Style.RESET_ALL}")
        if show_traceback:
            traceback.print_exc()
    
    def debug(self, message):
        """
        输出调试信息（蓝色）
        """
        print(f"{Fore.BLUE}调试: {message}{Style.RESET_ALL}")

# 创建单例实例
logger = Logger() 