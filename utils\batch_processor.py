"""
批处理工具模块，提供通用的异步批处理和LLM调用抽象，用于高效处理大量并行任务。

该模块的核心是 AsyncBatchProcessor 类，它主要提供以下功能：

1. **三阶段处理流程**：
   - Filter阶段（可选）：预先过滤输入数据
   - Map阶段：对每个数据项单独处理
   - Reduce阶段（可选）：基于Map结果进行聚合处理

2. **并行批量处理**：
   支持对大量数据项真并行处理，通过多进程实现性能提升。

3. **LLM调用封装**：
   处理大语言模型的调用流程，包括Prompt生成、LLM调用和响应解析。

4. **回调函数注入与自然语言指令**：
   - **高级用法**：通过回调函数支持自定义的处理逻辑：
     - filter_prompt_generator: 为Filter阶段生成Prompt（可选）
     - filter_response_processor: 解析Filter阶段的LLM响应，决定是否保留该项（可选）
     - map_prompt_generator: 为Map阶段生成Prompt
     - map_response_processor: 解析Map阶段的LLM响应
     - reduce_prompt_generator: 为Reduce阶段生成Prompt（可选）
     - reduce_response_processor: 解析Reduce阶段的LLM响应（可选）
   - **简易用法**：直接传入自然语言指令，使用内置的prompt生成和响应解析逻辑：
     - filter_instruction: Filter阶段的自然语言指令 (可选)
     - map_instruction: Map阶段的自然语言指令 (必选，除非提供了map_prompt_generator)
     - reduce_instruction: Reduce阶段的自然语言指令 (可选)

5. **自动数据统计**：
   在Reduce阶段的提示词中自动包含处理的数据项数量信息（如"共处理了 X 个数据项"），
   帮助LLM更好地理解数据规模并做出相应的聚合决策。

常见用例：
- 文档检索：先过滤不相关文档，再处理相关文档，最后合并结果
- 数据摘要：并行处理多个数据源，然后合并结果
- 多模型协作：让多个专用模型分别处理数据的不同方面，然后聚合结果
- 批量数据分析：处理大量数据项并在reduce阶段获得统计汇总信息
"""

import asyncio
import re
import os
import pickle
import functools
import time
import random  # 导入随机数模块
from concurrent.futures import ProcessPoolExecutor
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
from multiprocessing import current_process

from log import logger
from message import SystemMessage, UserMessage, Message # 确保Message被导入
from model import OpenAIModel

# 重试装饰器，用于模型生成
async def retry_generate_with_backoff(model, temp_conv, n=1, max_retries=20):
    """
    带指数退避重试的模型生成函数
    
    参数:
        model: 模型实例
        temp_conv: 对话实例
        n: 生成候选回答的数量，默认为1
        max_retries: 最大重试次数
        
    返回:
        当n=1时: 返回单个模型响应
        当n>1时: 返回模型响应列表
    """
    retry_count = 0
    base_wait_time = 1  # 基础等待时间（秒）
    max_wait_time = 60  # 最大等待时间（秒）

    while True:
        try:
            return await temp_conv.generate(model, n=n)
        except Exception as e:
            retry_count += 1
            if retry_count > max_retries:
                logger.error(f"模型生成失败，已达到最大重试次数({max_retries})。最后一次错误: {e}")
                raise  # 重新抛出最后一次的异常
            
            # 计算等待时间（指数退避+随机扰动）
            wait_time = min(base_wait_time * (2 ** (retry_count - 1)) + random.uniform(0.1, 1.0), max_wait_time)
            logger.warning(f"模型生成失败(错误: {e})，将在 {wait_time:.2f} 秒后重试（第 {retry_count}/{max_retries} 次重试）")
            await asyncio.sleep(wait_time)

class AsyncBatchProcessor:
    """
    异步批处理工具，支持Filter-Map-Reduce三阶段处理模式。
    
    该类封装了并行批量处理和LLM调用的通用流程，通过回调函数或自然语言指令支持自定义的处理逻辑。
    同时，使用多进程实现了真并行处理，进一步提高了处理效率。
    """
    
    def __init__(self, chunk_size: int = 10, max_workers: int = 1):
        """
        初始化批处理器。
        
        参数:
            chunk_size: 每个工作进程处理的最大数据项数量
            max_workers: 并行工作进程的最大数量，默认为 None (使用 CPU 核心数)
        """
        self.chunk_size = chunk_size
        self.max_workers = max_workers
        self.executor = None  # 将在process_batch中懒初始化

    # --- 默认 Prompt 生成器 --- 
    @staticmethod
    async def _default_filter_prompt_generator(
        item_key: str, item_value: Any, filter_instruction: str, **kwargs
    ) -> List[Message]:
        system_prompt = """你是一个高度精确的数据筛选专家。你的唯一使命是根据用户提供的筛选指令，对数据项进行严格的判断。

**核心原则：**
1.  **绝对忠于指令**：你必须严格按照用户给出的筛选指令进行判断，不得偏离或自行解读。你的分析必须精准匹配每一个条件。
2.  **杜绝外部知识干扰**：在筛选过程中，严禁使用你记忆中的任何信息或知识，因为那可能并不准确或已经过时。你的判断必须，且只能基于当前提供给你的筛选指令和数据项本身。任何外部联想或"你以为"的知识都可能导致灾难性错误。
3.  **责任重大，后果严重**：你的每一个筛选决定都至关重要，并将直接移交给后续流程。任何错漏都可能引发严重的生产安全事故。请务必以最高的责任心和最谨慎的态度进行决策。

你接下来的任务是分析数据项，并严格按照指定的格式输出你的判断理由和最终决定。"""
        
        user_prompt = f"""当前筛选指令: {filter_instruction}

数据项键: {item_key}
数据项值:
{item_value}

请仔细评估此数据项。严格按照以下格式回答，不要包含其他任何文字：
[REASON]
(在此处说明你最终判断的理由，例如：该项符合/不符合指令中的某某条件。)
[/REASON]
[KEEP_ITEM]
(在此处填写"是"或"否")
[/KEEP_ITEM]

例如:
如果筛选指令是 "只保留价格低于100的水果"，数据项是 {{ "name": "apple", "price": 50 }}，你应该回答:
[REASON]
价格50低于100，符合筛选条件。
[/REASON]
[KEEP_ITEM]
是
[/KEEP_ITEM]

如果筛选指令是 "排除所有红色物品"，数据项是 {{ "name": "strawberry", "color": "red" }}，你应该回答:
[REASON]
物品颜色为红色，需要排除。
[/REASON]
[KEEP_ITEM]
否
[/KEEP_ITEM]
"""
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]

    @staticmethod
    async def _default_map_prompt_generator(
        item_key: str, item_value: Any, map_instruction: str, **kwargs
    ) -> List[Message]:
        system_prompt = """你是一位专注且精确的数据转换工程师。你的核心职责是严格遵循用户提供的"处理指令"，对单个数据项进行转换或提取信息。

**核心工作要求：**
1.  **指令至上，精准执行**：你必须完全理解并严格执行用户下达的"当前处理指令"。任何对指令的偏离、猜测或自行扩展都可能导致结果无效。
2.  **聚焦当前数据，杜绝外部干扰**：你的所有操作都必须基于当前提供的"数据项键"和"数据项值"。严禁引入或依赖任何你记忆中的外部知识、假设或与当前数据无关的信息。
3.  **格式严格遵守，结果清晰明了**：处理完成后，你必须将结果封装在 `[RESULT]` 和 `[/RESULT]` 标签之间。这是唯一的输出格式，结果本身应根据指令要求，做到清晰、准确、无歧义。
4.  **确保一对一转换**：你的任务是针对当前这一个数据项进行处理，并输出其对应的唯一结果。
5.  **高度负责，结果关键**：你处理的每一个数据项的结果都将作为后续流程的关键输入。任何错误、遗漏或不准确的转换都可能引发连锁反应，并最终导致严重的生产安全事故。请务必以最高的责任心和最谨慎的态度进行操作。

请仔细分析下方提供的"数据项"和"处理指令"，然后生成结果。"""
        
        user_prompt = f"""当前处理指令: {map_instruction}

数据项键: {item_key}
数据项值:
{item_value}

请根据"当前处理指令"处理此数据项。严格按照以下格式返回处理后的结果，不要包含其他任何文字：
[RESULT]
(在此处填写处理后的结果。如果指令是提取信息，就填写提取的信息；如果指令是转换格式，就填写转换后的内容；如果指令是进行计算，就填写计算结果。结果可以是纯文本、JSON字符串等，取决于指令要求。)
[/RESULT]

例如:
如果处理指令是 "提取用户的邮箱地址"，数据项是 {{ "user": "John Doe", "email": "<EMAIL>", "age": 30 }}，你应该回答:
[RESULT]
<EMAIL>
[/RESULT]

如果处理指令是 "将价格翻倍并格式化为美元"，数据项是 {{ "item": "apple", "price": 1.5 }}，你应该回答:
[RESULT]
$3.00
[/RESULT]

如果处理指令是 "总结以下文本的主要观点（10个字以内）"，数据项是 "这是一个关于人工智能的有趣文本...（此处省略数百字）...因此AI的未来充满希望。"，你应该回答:
[RESULT]
AI的未来充满希望。
[/RESULT]
"""
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]

    @staticmethod
    async def _default_reduce_prompt_generator(
        map_results: Dict[str, Any], reduce_instruction: str, **kwargs
    ) -> List[Message]:
        system_prompt = """你是一位资深的数据汇总分析师。你的任务是基于用户给出的"汇总指令"，对一系列已经单独处理过的数据项结果进行最终的整合、提炼或聚合。

**核心工作准则：**
1.  **深刻理解汇总意图**：你必须首先准确理解用户"当前汇总指令"的核心目的。是要计算总和、生成摘要、找出共性，还是进行其他类型的聚合？
2.  **基于已有结果，禁止二次加工**：你的输入是"已处理的数据项列表"，这些数据项已经是上一阶段（Map阶段）的处理成果。你不得对单个数据项的原始值或其处理逻辑进行重新评估或修改，你的工作是针对这些既有的处理结果进行再一步的汇总。
3.  **全面性与准确性并重**：汇总结果必须全面反映所有"已处理的数据项列表"中的相关信息，并严格按照"汇总指令"的要求进行精确计算或整合。避免遗漏任何关键信息，也禁止添加指令未要求或数据未体现的内容。
4.  **直接输出结果**：请直接提供汇总结果，无需使用特殊标签或格式，你的整个回答就是最终的汇总成果。
5.  **决策关键，影响深远**：你的汇总结果是整个批处理流程的最终输出或关键决策依据。任何汇总阶段的错误、不准确或误导性结论，都将直接导致严重的生产安全事故。请以最高的专业素养和极致的审慎态度完成此项任务。

请审阅下方提供的"已处理的数据项列表"和"当前汇总指令"，然后直接给出最终的汇总结果。"""
        
        results_str = ""
        for i, (key, value) in enumerate(map_results.items()):
            results_str += f"数据项 {i+1} (键: {key}):\n{value}\n---\n"
        if not results_str: # 如果map_results为空
            results_str = "(没有可供汇总的已处理数据项)"
            
        # 计算数据项数量
        item_count = len(map_results)
        count_info = f"共处理了 {item_count} 个数据项" if item_count > 0 else "没有数据项被处理"
            
        user_prompt = f"""当前汇总指令: {reduce_instruction}

处理统计: {count_info}

已处理的数据项列表如下 (每个数据项的结果由分隔线 '---' 分隔):
{results_str}

请根据"当前汇总指令"对以上所有数据项的处理结果进行最终汇总。直接给出汇总结果即可：

**示例参考：**

示例1 - 如果汇总指令是"计算所有价格的总和"，已处理数据项列表是：
数据项 1 (键: itemA):
$20.00
---
数据项 2 (键: itemB):
$35.50
---
你应该直接回答：
$55.50

示例2 - 如果汇总指令是"将所有评论整合成一段话"，已处理数据项列表是：
数据项 1 (键: review1):
产品很棒，质量好。
---
数据项 2 (键: review2):
物流有点慢，但客服态度不错。
---
你应该直接回答：
产品很棒，质量好。物流有点慢，但客服态度不错。

示例3 - 如果汇总指令是"统计主要问题类型及数量"，已处理数据项列表是：
数据项 1 (键: issue1):
网络连接问题
---
数据项 2 (键: issue2):
登录失败
---
数据项 3 (键: issue3):
网络连接问题
---
你应该直接回答：
网络连接问题: 2次
登录失败: 1次

现在请处理上述实际数据："""
        
        return [SystemMessage(system_prompt), UserMessage(user_prompt)]

    # --- 默认 Response 处理器 --- 
    @staticmethod
    async def _default_filter_response_processor(
        raw_response: str, item_key: str, item_value: Any, filter_instruction: str, **kwargs
    ) -> bool:
        # 清理LLM响应中可能出现的markdown代码块
        cleaned_response = raw_response.strip()
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            lines = cleaned_response.split('\n')
            if len(lines) > 1: # 移除第一行和最后一行
                cleaned_response = '\n'.join(lines[1:-1]).strip()
            else: # 如果只有一行 ```json ... ```
                cleaned_response = cleaned_response[cleaned_response.find('{'):cleaned_response.rfind('}')+1] if '{' in cleaned_response else cleaned_response.replace("```","").strip()

        # 提取理由
        reason = "未提供理由"
        match_reason = re.search(r"\[REASON\](.*?)(?:\[/REASON\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        if match_reason:
            reason = match_reason.group(1).strip()

        match_keep = re.search(r"\[KEEP_ITEM\](.*?)(?:\[/KEEP_ITEM\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        
        if match_keep:
            decision_text = match_keep.group(1).strip().lower()
            # 更宽松地判断 "是"
            if decision_text == "是" or decision_text == "yes" or decision_text == "true":
                logger.debug(f"Filter '{item_key}' decision: '是' reason: '{reason}' from LLM for instruction: '{filter_instruction}'")
                return True
            # 更宽松地判断 "否"
            elif decision_text == "否" or decision_text == "no" or decision_text == "false":
                logger.debug(f"Filter '{item_key}' decision: '否' reason: '{reason}' from LLM for instruction: '{filter_instruction}'")
                return False
            else:
                logger.warning(f"Filter阶段无法从LLM响应的[KEEP_ITEM]标签中明确解析出'是'或'否' (标签内容: '{decision_text}', 理由: '{reason}', 指令: '{filter_instruction}', Key: {item_key})。响应: {cleaned_response[:200]}... 默认不保留。")
                return False
        
        # 如果连 [KEEP_ITEM] 标签都找不到，尝试从整个响应文本中推断
        # 这是一个更宽松的回退，优先级低于标签解析
        response_lower = cleaned_response.lower()
        if "是" in response_lower and "否" not in response_lower: # 包含"是"且不包含"否"
             logger.debug(f"Filter '{item_key}' decision: '是' (从文本推断) reason: '{reason}' for instruction: '{filter_instruction}'. Response: {cleaned_response[:100]}")
             return True
        if "否" in response_lower and "是" not in response_lower: # 包含"否"且不包含"是"
             logger.debug(f"Filter '{item_key}' decision: '否' (从文本推断) reason: '{reason}' for instruction: '{filter_instruction}'. Response: {cleaned_response[:100]}")
             return False

        logger.warning(f"Filter阶段无法从LLM响应中解析[KEEP_ITEM]决策或从文本中可靠推断（理由: '{reason}', 指令: '{filter_instruction}', Key: {item_key}）。响应: {cleaned_response[:200]}... 默认不保留。")
        return False

    @staticmethod
    async def _default_map_response_processor(
        raw_response: str, item_key: str, item_value: Any, map_instruction: str, **kwargs
    ) -> Any:
        # 清理LLM响应中可能出现的markdown代码块
        cleaned_response = raw_response.strip()
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            lines = cleaned_response.split('\n')
            if len(lines) > 1: # 移除第一行和最后一行
                # 保留内部内容，包括可能的json格式
                cleaned_response = '\n'.join(lines[1:-1]).strip() 
            else: # 例如 ```json {"key": "value"} ```
                 # 尝试提取被```json ... ```包裹的内容
                match_json_block = re.match(r"^```(?:json)?\s*(.*)\s*```$", cleaned_response, re.DOTALL | re.IGNORECASE)
                if match_json_block:
                    cleaned_response = match_json_block.group(1).strip()
                else: # 保守起见，如果不是典型的json块，就只移除```
                    cleaned_response = cleaned_response.replace("```","").strip()
        elif cleaned_response.startswith("```json"):
             cleaned_response = cleaned_response[len("```json"):].strip()
             if cleaned_response.endswith("```"):
                 cleaned_response = cleaned_response[:-len("```")].strip()
        elif cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[len("```"):].strip()
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-len("```")].strip()

        match_result = re.search(r"\[RESULT\](.*?)(?:\[/RESULT\]|$)", cleaned_response, re.DOTALL | re.IGNORECASE)
        
        if match_result:
            extracted_data = match_result.group(1).strip()
            logger.debug(f"Map '{item_key}' extracted data (length: {len(extracted_data)}) using [RESULT] tag for instruction: '{map_instruction}'")
            return extracted_data

        logger.warning(f"Map阶段无法从LLM响应中解析[RESULT]标签（指令: '{map_instruction}', Key: {item_key}）。响应: {cleaned_response[:200]}... 将返回原始清理后的响应作为回退。")
        # 作为最终回退，返回清理后的原始响应，而不是None，因为某些映射可能期望任何字符串
        return cleaned_response

    @staticmethod
    async def _default_reduce_response_processor(
        raw_response: str, map_results: Dict[str, Any], reduce_instruction: str, **kwargs
    ) -> Any:
        # 清理LLM响应中可能出现的markdown代码块
        cleaned_response = raw_response.strip()
        if cleaned_response.startswith("```") and cleaned_response.endswith("```"):
            lines = cleaned_response.split('\n')
            if len(lines) > 1: # 移除第一行和最后一行
                cleaned_response = '\n'.join(lines[1:-1]).strip()
            else: # 例如 ```json {"key": "value"} ```
                match_json_block = re.match(r"^```(?:json)?\s*(.*)\s*```$", cleaned_response, re.DOTALL | re.IGNORECASE)
                if match_json_block:
                    cleaned_response = match_json_block.group(1).strip()
                else:
                    cleaned_response = cleaned_response.replace("```","").strip()
        elif cleaned_response.startswith("```json"):
             cleaned_response = cleaned_response[len("```json"):].strip()
             if cleaned_response.endswith("```"):
                 cleaned_response = cleaned_response[:-len("```")].strip()
        elif cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[len("```"):].strip()
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-len("```")].strip()

        logger.debug(f"Reduce成功处理响应 (长度: {len(cleaned_response)}) for instruction: '{reduce_instruction}'")
        return cleaned_response
    
    async def _process_single_item_filter(
        self,
        item_key: str,
        item_value: Any,
        model_name: str,
        filter_prompt_generator_actual: Callable,
        filter_response_processor_actual: Callable,
        **processor_kwargs # 可能包含 filter_instruction
    ) -> Tuple[str, bool]:
        """
        处理单个Filter任务，判断数据项是否应被保留。
        该方法现在是对静态方法的包装，用于向后兼容。
        
        参数:
            item_key: 数据项的键
            item_value: 数据项的值
            model_name: 模型的符号名称
            filter_prompt_generator_actual: 实际使用的Filter阶段Prompt生成器
            filter_response_processor_actual: 实际使用的Filter阶段响应处理器
            processor_args: 传递给回调函数的额外参数 (可能包含 filter_instruction)
            
        返回:
            (item_key, keep_item) 元组，keep_item为True表示保留此项
        """
        # 创建模型实例
        model = OpenAIModel(model_name)
        return await self._static_core_process_filter_item(
            item_key, item_value, model, 
            filter_prompt_generator_actual, filter_response_processor_actual, 
            **processor_kwargs
        )
    
    async def _process_single_item_map(
        self,
        item_key: str,
        item_value: Any,
        model_name: str,
        map_prompt_generator_actual: Callable,
        map_response_processor_actual: Callable,
        **processor_kwargs # 可能包含 map_instruction
    ) -> Tuple[str, Any]:
        """
        处理单个Map任务，转换数据项。
        该方法现在是对静态方法的包装，用于向后兼容。
        """
        # 创建模型实例
        model = OpenAIModel(model_name)
        return await self._static_core_process_map_item(
            item_key, item_value, model,
            map_prompt_generator_actual, map_response_processor_actual,
            **processor_kwargs
        )
    
    async def _process_reduce(
        self,
        map_results: Dict[str, Any],
        model_name: str,
        reduce_prompt_generator_actual: Callable,
        reduce_response_processor_actual: Callable,
        **processor_kwargs # 可能包含 reduce_instruction
    ) -> Any:
        """
        执行Reduce任务，聚合Map阶段的结果。
        该方法现在是对静态方法的包装，用于向后兼容。
        """
        return await self._static_core_process_reduce(
            map_results, model_name,
            reduce_prompt_generator_actual, reduce_response_processor_actual,
            **processor_kwargs
        )
    
    async def _process_filter_batch(
        self,
        items_dict: Dict[str, Any],
        model_name: str,
        filter_prompt_generator_actual: Callable,
        filter_response_processor_actual: Callable,
        filter_n: int = 1,
        **processor_kwargs
    ) -> Dict[str, Any]:
        """
        使用batch_generate处理Filter阶段，支持多数投票
        
        参数:
            items_dict: 要过滤的数据项字典
            model_name: 模型名称
            filter_prompt_generator_actual: Filter阶段Prompt生成器
            filter_response_processor_actual: Filter阶段响应处理器
            filter_n: 投票数量，为每个数据项生成n个相同的任务进行投票
            **processor_kwargs: 额外参数
            
        返回:
            过滤后的数据项字典
        """
        if not items_dict:
            return {}
            
        from model import OpenAIModel
        model = OpenAIModel(model_name)
        
        logger.info(f"Filter阶段(batch模式)：处理 {len(items_dict)} 个数据项, filter_n={filter_n}")
        
        # 第一轮：为每个数据项生成filter_n个相同的任务
        first_round_conversations = {}
        for item_key, item_value in items_dict.items():
            messages = await filter_prompt_generator_actual(item_key, item_value, **processor_kwargs)
            if messages:
                # 为每个数据项生成filter_n个相同的对话
                for vote_idx in range(filter_n):
                    conversation_key = f"{item_key}_vote{vote_idx + 1}_round1"
                    first_round_conversations[conversation_key] = messages
        
        if not first_round_conversations:
            logger.warning("Filter阶段：没有生成有效的对话，返回空结果")
            return {}
        
        logger.info(f"Filter阶段第一轮：生成 {len(first_round_conversations)} 个对话任务")
        
        # 调用batch_generate获取第一轮结果
        first_round_results = await model.batch_generate(
            conversations=first_round_conversations
        )
        
        # 解析第一轮结果
        vote_results = {}  # item_key -> [bool, bool, ...]
        for conversation_id, assistant_msg in first_round_results.items():
            if "_vote" in conversation_id and "_round1" in conversation_id:
                # 解析conversation_id: "itemkey_vote1_round1" -> "itemkey"
                parts = conversation_id.split("_vote")
                if len(parts) >= 2:
                    item_key = parts[0]
                    if item_key in items_dict:
                        raw_response = assistant_msg.content or ""
                        keep_item = await filter_response_processor_actual(
                            raw_response, item_key, items_dict[item_key], **processor_kwargs
                        )
                        if item_key not in vote_results:
                            vote_results[item_key] = []
                        vote_results[item_key].append(bool(keep_item))
        
        # 检查是否有任何数据项的投票结果不一致，需要第二轮投票
        need_second_round = {}
        for item_key, votes in vote_results.items():
            if len(votes) > 0:
                # 检查投票结果是否一致（全是True或全是False）
                all_true = all(votes)
                all_false = not any(votes)
                if not (all_true or all_false):
                    # 结果不一致，需要第二轮投票
                    need_second_round[item_key] = items_dict[item_key]
        
        # 如果有需要第二轮投票的数据项
        if need_second_round:
            logger.info(f"Filter阶段：{len(need_second_round)} 个数据项投票不一致，需要第二轮投票")
            
            # 第二轮：为不一致的数据项再次生成filter_n个任务
            second_round_conversations = {}
            for item_key, item_value in need_second_round.items():
                messages = await filter_prompt_generator_actual(item_key, item_value, **processor_kwargs)
                if messages:
                    # 为每个数据项生成filter_n个相同的对话
                    for vote_idx in range(filter_n):
                        conversation_key = f"{item_key}_vote{vote_idx + 1}_round2"
                        second_round_conversations[conversation_key] = messages
            
            if second_round_conversations:
                logger.info(f"Filter阶段第二轮：生成 {len(second_round_conversations)} 个对话任务")
                
                # 调用batch_generate获取第二轮结果
                second_round_results = await model.batch_generate(
                    conversations=second_round_conversations
                )
                
                # 解析第二轮结果并合并到vote_results
                for conversation_id, assistant_msg in second_round_results.items():
                    if "_vote" in conversation_id and "_round2" in conversation_id:
                        # 解析conversation_id: "itemkey_vote1_round2" -> "itemkey"
                        parts = conversation_id.split("_vote")
                        if len(parts) >= 2:
                            item_key = parts[0]
                            if item_key in need_second_round:
                                raw_response = assistant_msg.content or ""
                                keep_item = await filter_response_processor_actual(
                                    raw_response, item_key, need_second_round[item_key], **processor_kwargs
                                )
                                vote_results[item_key].append(bool(keep_item))
        
        # 根据投票结果决定保留哪些数据项
        filtered_items = {}
        for item_key, votes in vote_results.items():
            if votes:
                # 简单多数决策
                keep_votes = sum(votes)
                total_votes = len(votes)
                threshold = total_votes / 2
                
                if keep_votes > threshold:
                    filtered_items[item_key] = items_dict[item_key]
                    logger.debug(f"Filter保留: {item_key} (投票: {keep_votes}/{total_votes})")
                else:
                    logger.debug(f"Filter过滤: {item_key} (投票: {keep_votes}/{total_votes})")
        
        logger.info(f"Filter阶段(batch模式)完成：保留 {len(filtered_items)}/{len(items_dict)} 个数据项")
        return filtered_items
    
    async def _process_map_batch(
        self,
        items_dict: Dict[str, Any],
        model_name: str,
        map_prompt_generator_actual: Callable,
        map_response_processor_actual: Callable,
        **processor_kwargs
    ) -> Dict[str, Any]:
        """
        使用batch_generate处理Map阶段
        
        参数:
            items_dict: 要处理的数据项字典
            model_name: 模型名称
            map_prompt_generator_actual: Map阶段Prompt生成器
            map_response_processor_actual: Map阶段响应处理器
            **processor_kwargs: 额外参数
            
        返回:
            处理后的数据项字典
        """
        if not items_dict:
            return {}
            
        from model import OpenAIModel
        model = OpenAIModel(model_name)
        
        logger.info(f"Map阶段(batch模式)：处理 {len(items_dict)} 个数据项")
        
        # 为所有数据项生成prompts
        conversations = {}
        for item_key, item_value in items_dict.items():
            messages = await map_prompt_generator_actual(item_key, item_value, **processor_kwargs)
            if messages:
                conversations[item_key] = messages
        
        if not conversations:
            logger.warning("Map阶段：没有生成有效的对话，返回空结果")
            return {}
        
        # 调用batch_generate获取所有结果
        batch_results = await model.batch_generate(
            conversations=conversations
        )
        
        # 处理结果
        processed_results = {}
        for item_key, assistant_msg in batch_results.items():
            if item_key in items_dict:
                raw_response = assistant_msg.content or ""
                processed_data = await map_response_processor_actual(
                    raw_response, item_key, items_dict[item_key], **processor_kwargs
                )
                if processed_data is not None:
                    processed_results[item_key] = processed_data
        
        logger.info(f"Map阶段(batch模式)完成：获得 {len(processed_results)} 个有效结果")
        return processed_results
    
    async def process_batch(
        self,
        items: Union[Dict[str, Any], List[Any]],
        model_name: str,
        # Map阶段：必选指令或回调
        map_instruction: Optional[str] = None,
        map_prompt_generator: Optional[Callable] = None,
        map_response_processor: Optional[Callable] = None,
        # Filter阶段：可选指令或回调
        filter_instruction: Optional[str] = None,
        filter_prompt_generator: Optional[Callable] = None,
        filter_response_processor: Optional[Callable] = None,
        filter_n: int = 3,  # 添加filter_n参数，用于多数投票
        # Reduce阶段：可选指令或回调
        reduce_instruction: Optional[str] = None,
        reduce_prompt_generator: Optional[Callable] = None,
        reduce_response_processor: Optional[Callable] = None,
        # 各阶段可选择使用不同的模型
        filter_model_name: Optional[str] = None,
        map_model_name: Optional[str] = None, 
        reduce_model_name: Optional[str] = None,
        # 批处理模式
        batch_generate: bool = False,
        **processor_kwargs # 传递给所有回调的额外关键字参数
    ) -> Union[Dict[str, Any], Any, List[Any]]:
        """
        对一批数据项执行三阶段批处理：Filter -> Map -> Reduce。
        支持通过回调函数或自然语言指令定义各阶段逻辑。
        支持多进程并行处理或batch_generate模式。
        
        参数:
            items: 要处理的键值对字典或列表。如果提供列表，返回值也将是相应的列表。
            model_name: LLM模型的符号名称
            
            map_instruction: Map阶段的自然语言指令。如果提供，将使用默认prompt/response处理器，除非显式提供map_prompt_generator/map_response_processor。
            map_prompt_generator: Map阶段Prompt生成器。签名: async def(item_key, item_value, [map_instruction?], *args) -> List[Message]
            map_response_processor: Map阶段响应处理器。签名: async def(raw_response, item_key, item_value, [map_instruction?], *args) -> Any
            
            filter_instruction: Filter阶段的自然语言指令。
            filter_prompt_generator: Filter阶段Prompt生成器。签名: async def(item_key, item_value, [filter_instruction?], *args) -> List[Message]
            filter_response_processor: Filter阶段响应处理器。签名: async def(raw_response, item_key, item_value, [filter_instruction?], *args) -> bool
            filter_n: 用于Filter阶段的多数投票的生成数量，当n>1时使用多数投票决定结果

            reduce_instruction: Reduce阶段的自然语言指令。
            reduce_prompt_generator: Reduce阶段Prompt生成器。签名: async def(map_results, [reduce_instruction?], *args) -> List[Message]
            reduce_response_processor: Reduce阶段响应处理器。签名: async def(raw_response, map_results, [reduce_instruction?], *args) -> Any
            
            filter_model_name: Filter阶段可选择的模型，如果提供将覆盖默认model_name
            map_model_name: Map阶段可选择的模型，如果提供将覆盖默认model_name
            reduce_model_name: Reduce阶段可选择的模型，如果提供将覆盖默认model_name
            
            batch_generate: 是否使用batch_generate模式。为True时，chunk_size和max_workers无效，
                           改用模型的batch_generate方法一次性提交所有任务。Filter阶段支持多数投票。
            
            processor_kwargs: 额外的关键字参数，将传递给所有被调用的回调函数（自定义的或默认的）。
                            如果使用自然语言指令，相应的指令文本本身会作为具名参数直接传递给默认处理器。

        返回:
            如果没有Reduce阶段：
                - 若输入为Dict，返回Map结果字典
                - 若输入为List，返回Map结果列表
            有Reduce阶段：
                返回Reduce阶段的最终结果
        """
        start_time = time.time()
        logger.info(f"开始批处理...数据量: {len(items)}, batch模式: {batch_generate}")
        
        # 懒初始化进程池（仅在非batch模式下）
        if not batch_generate and self.executor is None:
            self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
            logger.info(f"初始化进程池，max_workers={self.max_workers or '(系统默认)'}")
        
        # 检查输入类型并转换列表为字典（如果需要）
        input_is_list = isinstance(items, list)
        if input_is_list:
            # 将列表转换为字典，使用索引作为键
            items_dict = {str(i): item for i, item in enumerate(items)}
            logger.debug(f"将输入列表（长度 {len(items)}）转换为字典用于处理")
        else:
            items_dict = items  # 输入已经是字典

        # --- 确定Map阶段的实际处理器 ---
        actual_map_prompt_generator = map_prompt_generator
        actual_map_response_processor = map_response_processor
        map_specific_kwargs = dict(processor_kwargs)

        if map_instruction is not None:
            if map_prompt_generator is None:
                actual_map_prompt_generator = self._default_map_prompt_generator
                map_specific_kwargs['map_instruction'] = map_instruction
            if map_response_processor is None:
                actual_map_response_processor = self._default_map_response_processor
                map_specific_kwargs['map_instruction'] = map_instruction
        
        if actual_map_response_processor is None and actual_map_prompt_generator is not None: # 如果用户提供了generator但没提供processor，可以考虑也用默认的
             actual_map_response_processor = self._default_map_response_processor
             if map_instruction:
                map_specific_kwargs['map_instruction'] = map_instruction

        # --- FILTER阶段（可选） ---
        filtered_items = items_dict # 如果不执行filter，使用原始items字典
        if filter_instruction is not None or (filter_prompt_generator and filter_response_processor):
            logger.info("开始Filter阶段...")
            if filter_n > 1:
                logger.info(f"Filter阶段启用多数投票逻辑 (n={filter_n})")
                
            actual_filter_prompt_generator = filter_prompt_generator
            actual_filter_response_processor = filter_response_processor
            filter_specific_kwargs = dict(processor_kwargs)

            if filter_instruction is not None:
                if filter_prompt_generator is None:
                    actual_filter_prompt_generator = self._default_filter_prompt_generator
                    filter_specific_kwargs['filter_instruction'] = filter_instruction
                if filter_response_processor is None:
                    actual_filter_response_processor = self._default_filter_response_processor
                    filter_specific_kwargs['filter_instruction'] = filter_instruction
            
            if actual_filter_prompt_generator is None or actual_filter_response_processor is None:
                 # 如果提供了 filter_instruction 但由于某种原因没有成功设置 actual 处理器，则跳过 filter
                logger.warning("Filter阶段指令或处理器配置不完整，跳过Filter阶段。")
            else:
                # 使用实际模型名称
                actual_filter_model_name = filter_model_name or model_name
                
                if batch_generate:
                    # 使用batch模式处理Filter阶段
                    filtered_items = await self._process_filter_batch(
                        items_dict,
                        actual_filter_model_name,
                        actual_filter_prompt_generator,
                        actual_filter_response_processor,
                        filter_n,
                        **filter_specific_kwargs
                    )
                else:
                    # 使用多进程模式处理Filter阶段
                    # 准备Filter阶段，确定处理器类型和必要参数
                    is_default_filter_generator = actual_filter_prompt_generator is self._default_filter_prompt_generator
                    is_default_filter_processor = actual_filter_response_processor is self._default_filter_response_processor
                    
                    # 根据是否使用默认处理器准备参数
                    filter_generator = None if is_default_filter_generator else actual_filter_prompt_generator
                    filter_processor = None if is_default_filter_processor else actual_filter_response_processor
                    
                    # 将数据分块
                    items_chunks = self._chunk_dict(items_dict, self.chunk_size)
                    logger.info(f"Filter阶段：将 {len(items_dict)} 个数据项分成 {len(items_chunks)} 个块")
                    
                    # 将任务提交到进程池
                    loop = asyncio.get_event_loop()
                    filter_futures = []
                    
                    for chunk in items_chunks:
                        future = loop.run_in_executor(
                            self.executor,
                            self._execute_filter_task_in_worker,
                            chunk,
                            actual_filter_model_name,
                            is_default_filter_generator,
                            is_default_filter_processor,
                            filter_instruction,
                            filter_generator,
                            filter_processor,
                            filter_n,  # 传递filter_n参数
                            filter_specific_kwargs
                        )
                        filter_futures.append(future)
                    
                    # 等待所有任务完成
                    filter_results_chunks = await asyncio.gather(*filter_futures)
                    
                    # 合并结果
                    filtered_items = {}
                    for chunk_results in filter_results_chunks:
                        for key, keep in chunk_results:
                            if keep:
                                filtered_items[key] = items_dict[key]
                
                filter_elapsed = time.time() - start_time
                logger.info(f"Filter阶段完成，耗时 {filter_elapsed:.2f}秒，保留 {len(filtered_items)}/{len(items_dict)} 个项目")
                
                if not filtered_items:
                    logger.info("Filter阶段过滤掉所有项目，跳过后续阶段")
                    return {}
        
        # --- MAP阶段 (可选) ---
        if actual_map_prompt_generator is not None:
            map_start_time = time.time()
            logger.info(f"开始Map阶段，处理 {len(filtered_items)} 个项目...")
            
            # 使用实际模型名称
            actual_map_model_name = map_model_name or model_name
            
            if batch_generate:
                # 使用batch模式处理Map阶段
                processed_map_results = await self._process_map_batch(
                    filtered_items,
                    actual_map_model_name,
                    actual_map_prompt_generator,
                    actual_map_response_processor,
                    **map_specific_kwargs
                )
            else:
                # 使用多进程模式处理Map阶段
                # 准备Map阶段，确定处理器类型和必要参数
                is_default_map_generator = actual_map_prompt_generator is self._default_map_prompt_generator
                is_default_map_processor = actual_map_response_processor is self._default_map_response_processor
                
                # 根据是否使用默认处理器准备参数
                map_generator = None if is_default_map_generator else actual_map_prompt_generator
                map_processor = None if is_default_map_processor else actual_map_response_processor
                
                # 将数据分块
                items_chunks = self._chunk_dict(filtered_items, self.chunk_size)
                logger.info(f"Map阶段：将 {len(filtered_items)} 个数据项分成 {len(items_chunks)} 个块")
                
                # 将任务提交到进程池
                loop = asyncio.get_event_loop()
                map_futures = []
                
                for chunk in items_chunks:
                    future = loop.run_in_executor(
                        self.executor,
                        self._execute_map_task_in_worker,
                        chunk,
                        actual_map_model_name,
                        is_default_map_generator,
                        is_default_map_processor,
                        map_instruction,
                        map_generator,
                        map_processor,
                        map_specific_kwargs
                    )
                    map_futures.append(future)
                
                # 等待所有任务完成
                map_results_chunks = await asyncio.gather(*map_futures)
                
                # 合并结果
                processed_map_results = {}
                for chunk_results in map_results_chunks:
                    for key, value in chunk_results:
                        if value is not None:
                            processed_map_results[key] = value
            
            map_elapsed = time.time() - map_start_time
            logger.info(f"Map阶段完成，耗时 {map_elapsed:.2f}秒，获得 {len(processed_map_results)} 个有效结果")
        else:
            # 如果没有提供 map 阶段处理器，则直接使用过滤后的项目
            logger.info("Map阶段被跳过（未提供处理器），将使用过滤后的原始项目。")
            processed_map_results = filtered_items
        
        # --- REDUCE阶段（可选） ---
        if reduce_instruction is not None or (reduce_prompt_generator and reduce_response_processor):
            if not processed_map_results:
                logger.info("Map结果为空，跳过Reduce阶段")
                # 如果输入是列表且结果应该是字典（map 结果），将空字典转换为空列表
                if input_is_list and isinstance(processed_map_results, dict):
                    return []
                return processed_map_results

            reduce_start_time = time.time()
            logger.info("开始Reduce阶段...")
            actual_reduce_prompt_generator = reduce_prompt_generator
            actual_reduce_response_processor = reduce_response_processor
            reduce_specific_kwargs = dict(processor_kwargs)

            if reduce_instruction is not None:
                if reduce_prompt_generator is None:
                    actual_reduce_prompt_generator = self._default_reduce_prompt_generator
                    reduce_specific_kwargs['reduce_instruction'] = reduce_instruction
                if reduce_response_processor is None:
                    actual_reduce_response_processor = self._default_reduce_response_processor
                    reduce_specific_kwargs['reduce_instruction'] = reduce_instruction

            if actual_reduce_prompt_generator is None or actual_reduce_response_processor is None:
                logger.warning("Reduce阶段指令或处理器配置不完整，将返回Map结果。")
                return processed_map_results
            else:
                # 使用实际模型名称
                actual_reduce_model_name = reduce_model_name or model_name
                
                # Reduce阶段不使用并行处理，直接在主进程中执行
                final_result = await self._process_reduce(
                    processed_map_results, 
                    actual_reduce_model_name, 
                    actual_reduce_prompt_generator, actual_reduce_response_processor,
                    **reduce_specific_kwargs
                )
                
                reduce_elapsed = time.time() - reduce_start_time
                logger.info(f"Reduce阶段完成，耗时 {reduce_elapsed:.2f}秒")
                
                total_elapsed = time.time() - start_time
                logger.info(f"批处理全部完成，总耗时 {total_elapsed:.2f}秒")
                
                return final_result
        else:
            logger.info("无Reduce阶段，返回Map结果")
            # 如果输入是列表且结果是字典，将结果转换回列表
            if input_is_list and isinstance(processed_map_results, dict):
                # 将字典转换回列表，按照原始索引顺序排列
                # 注意：可能有些索引被过滤掉了，或map处理结果为None
                result_list = []
                for i in range(len(items)):
                    if str(i) in processed_map_results:
                        result_list.append(processed_map_results[str(i)])
                logger.debug(f"将Map结果字典转换为列表（长度 {len(result_list)}）")
                
                total_elapsed = time.time() - start_time
                logger.info(f"批处理全部完成，总耗时 {total_elapsed:.2f}秒")
                
                return result_list
                
            total_elapsed = time.time() - start_time
            logger.info(f"批处理全部完成，总耗时 {total_elapsed:.2f}秒")
            
            return processed_map_results
            
    def __del__(self):
        """
        对象销毁时确保进程池被正确关闭。
        """
        if hasattr(self, 'executor') and self.executor is not None:
            self.executor.shutdown(wait=True)
            logger.debug("进程池已关闭")
    
    async def __aenter__(self):
        """
        支持异步上下文管理器。
        """
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        异步上下文管理器退出时关闭进程池。
        """
        if hasattr(self, 'executor') and self.executor is not None:
            self.executor.shutdown(wait=True)
            self.executor = None
            logger.debug("进程池已关闭")
            
    # --- 静态核心处理方法 ---
    @staticmethod
    async def _static_core_process_filter_item(
        item_key: str,
        item_value: Any,
        model: Any,  # 现在接收模型实例
        filter_prompt_generator_actual: Callable,
        filter_response_processor_actual: Callable,
        n: int = 3,  # 添加n参数，表示生成多少个候选结果进行投票
        **processor_kwargs  # 可能包含 filter_instruction
    ) -> Tuple[str, bool]:
        """
        处理单个Filter任务，判断数据项是否应被保留。
        静态版本，不依赖AsyncBatchProcessor实例。
        
        参数:
            item_key: 数据项的键
            item_value: 数据项的值
            model: 模型实例
            filter_prompt_generator_actual: 实际使用的Filter阶段Prompt生成器
            filter_response_processor_actual: 实际使用的Filter阶段响应处理器
            n: 生成结果数量，当n>1时使用多数投票决定结果
            processor_args: 传递给回调函数的额外参数 (可能包含 filter_instruction)
            
        返回:
            (item_key, keep_item) 元组，keep_item为True表示保留此项
        """
        try:
            # 添加随机延迟（5-50毫秒），防止所有任务同时启动
            delay_ms = random.uniform(5, 500)
            await asyncio.sleep(delay_ms / 1000.0)
            
            # processor_kwargs 可能包含 filter_instruction，也可能为空，取决于用户调用方式
            messages = await filter_prompt_generator_actual(item_key, item_value, **processor_kwargs)
            if not messages:
                logger.warning(f"Filter任务 {item_key} 的Prompt生成器未返回消息，默认不保留。")
                return item_key, False
            
            from conversation import Conversation
            temp_conv = Conversation()
            for msg in messages:
                await temp_conv.add_message(msg)
            
            if n <= 1:
                # 原始逻辑，单个回答
                response = await retry_generate_with_backoff(model, temp_conv)
                raw_llm_response = response.content if hasattr(response, 'content') else str(response)
                
                keep_item = await filter_response_processor_actual(raw_llm_response, item_key, item_value, **processor_kwargs)
                keep_item = bool(keep_item)
                
                logger.debug(f"完成Filter任务: {item_key}, 进程ID: {os.getpid()}, 结果: {'保留' if keep_item else '过滤'}")
                return item_key, keep_item
            else:
                # 第一轮投票
                logger.debug(f"Filter任务 {item_key} 开始第一轮投票 (n={n})")
                responses = await retry_generate_with_backoff(model, temp_conv, n=n)
                
                # 对每个响应进行处理
                vote_results = []
                for i, response in enumerate(responses):
                    raw_llm_response = response.content if hasattr(response, 'content') else str(response)
                    keep_item = await filter_response_processor_actual(raw_llm_response, item_key, item_value, **processor_kwargs)
                    vote_results.append(bool(keep_item))
                
                # 统计第一轮投票结果
                total_votes = len(vote_results)
                keep_votes = sum(1 for v in vote_results if v)
                vote_percentage = (keep_votes / total_votes) * 100 if total_votes > 0 else 0
                
                # 检查是否有一致的结果（全票通过或全票拒绝）
                if keep_votes == 0 or keep_votes == total_votes:
                    # 结果一致，不需要第二轮
                    final_decision = keep_votes > 0
                    logger.debug(f"Filter任务 {item_key} 第一轮投票结果一致: {'保留' if final_decision else '过滤'} "
                                f"({keep_votes}/{total_votes}, {vote_percentage:.1f}%), 无需第二轮。进程ID: {os.getpid()}")
                    return item_key, final_decision
                
                # 结果不一致，进行第二轮投票
                logger.debug(f"Filter任务 {item_key} 第一轮结果不一致 ({keep_votes}/{total_votes})，启动第二轮投票 (再生成{n}个结果)")
                
                # 获取第二轮响应
                second_responses = await retry_generate_with_backoff(model, temp_conv, n=n)
                
                # 处理第二轮响应
                for i, response in enumerate(second_responses):
                    raw_llm_response = response.content if hasattr(response, 'content') else str(response)
                    keep_item = await filter_response_processor_actual(raw_llm_response, item_key, item_value, **processor_kwargs)
                    vote_results.append(bool(keep_item))
                
                # 统计最终投票结果 (合并两轮)
                total_votes = len(vote_results)  # 应该是 2*n
                keep_votes = sum(1 for v in vote_results if v)
                threshold = total_votes / 2  # 简单多数（超过50%）
                
                final_decision = keep_votes > threshold
                vote_percentage = (keep_votes / total_votes) * 100 if total_votes > 0 else 0
                
                logger.debug(f"Filter任务 {item_key} 最终投票结果 (两轮合并): {'保留' if final_decision else '过滤'} "
                            f"({keep_votes}/{total_votes}, {vote_percentage:.1f}%), 进程ID: {os.getpid()}")
                
                return item_key, final_decision
                
        except Exception as e:
            logger.error(f"Filter任务 {item_key} 时出错: {e}", show_traceback=True)
            return item_key, False
    
    @staticmethod
    async def _static_core_process_map_item(
        item_key: str,
        item_value: Any,
        model: Any,  # 现在接收模型实例
        map_prompt_generator_actual: Callable,
        map_response_processor_actual: Callable,
        **processor_kwargs  # 可能包含 map_instruction
    ) -> Tuple[str, Any]:
        """
        处理单个Map任务，转换数据项。
        静态版本，不依赖AsyncBatchProcessor实例。
        """
        try:
            # 添加随机延迟（5-50毫秒），防止所有任务同时启动
            delay_ms = random.uniform(5, 500)
            await asyncio.sleep(delay_ms / 1000.0)
            
            messages = await map_prompt_generator_actual(item_key, item_value, **processor_kwargs)
            if not messages:
                logger.warning(f"Map任务 {item_key} 的Prompt生成器未返回消息，跳过处理。")
                return item_key, None
            
            from conversation import Conversation
            temp_conv = Conversation()
            for msg in messages:
                await temp_conv.add_message(msg)
            
            response = await retry_generate_with_backoff(model, temp_conv)
            raw_llm_response = response.content if hasattr(response, 'content') else str(response)
            
            processed_data = await map_response_processor_actual(raw_llm_response, item_key, item_value, **processor_kwargs)
            logger.debug(f"完成Map任务: {item_key}, 进程ID: {os.getpid()}")
            return item_key, processed_data
                
        except Exception as e:
            logger.error(f"Map任务 {item_key} 时出错: {e}", show_traceback=True)
            return item_key, None
    
    @staticmethod
    async def _static_core_process_reduce(
        map_results: Dict[str, Any],
        model_name: str,  # 仍然使用model_name，因为Reduce是在主进程执行的
        reduce_prompt_generator_actual: Callable,
        reduce_response_processor_actual: Callable,
        **processor_kwargs  # 可能包含 reduce_instruction
    ) -> Any:
        """
        执行Reduce任务，聚合Map阶段的结果。
        静态版本，不依赖AsyncBatchProcessor实例。
        
        注：Reduce在主进程中执行，所以仍然接收model_name并实例化模型。
        """
        try:
            # 实例化模型
            model = OpenAIModel(model_name)
            
            messages = await reduce_prompt_generator_actual(map_results, **processor_kwargs)
            if not messages:
                logger.warning("Reduce Prompt生成器返回空消息，将返回原始Map结果。")
                return map_results
            
            from conversation import Conversation
            temp_conv = Conversation()
            for msg in messages:
                await temp_conv.add_message(msg)
            
            response = await retry_generate_with_backoff(model, temp_conv)
            raw_llm_response = response.content if hasattr(response, 'content') else str(response)
            
            final_result = await reduce_response_processor_actual(raw_llm_response, map_results, **processor_kwargs)
            logger.debug(f"完成Reduce任务, 进程ID: {os.getpid()}")
            return final_result
            
        except Exception as e:
            logger.error(f"Reduce任务出错: {e}", show_traceback=True)
            return {
                "error": f"Reduce阶段失败: {str(e)}",
                "map_results": map_results
            }

    @staticmethod
    def _execute_filter_task_in_worker(
        items_chunk: Dict[str, Any],
        model_name: str,
        is_default_filter_generator: bool,
        is_default_filter_processor: bool,
        filter_instruction: Optional[str],
        filter_generator: Optional[Callable],
        filter_processor: Optional[Callable],
        filter_n: int = 1,  # 添加filter_n参数
        processor_kwargs_dict: Optional[Dict] = None
    ) -> List[Tuple[str, bool]]:
        """
        在工作进程中执行一批Filter任务。
        
        参数:
            items_chunk: 要处理的数据项字典 (一个较大字典的子集)
            model_name: 模型的符号名称
            is_default_filter_generator: 是否使用默认的filter prompt生成器
            is_default_filter_processor: 是否使用默认的filter响应处理器
            filter_instruction: filter指令文本 (如果使用默认生成器)
            filter_generator: 自定义filter prompt生成器 (如果不使用默认生成器)
            filter_processor: 自定义filter响应处理器 (如果不使用默认处理器)
            filter_n: 用于多数投票的结果数量 (默认为1，不使用投票)
            processor_kwargs_dict: 额外参数字典
            
        返回:
            结果列表，每个元素是 (item_key, keep_item) 元组
        """
        # 初始化异步任务所需的事件循环
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            logger.debug(f"工作进程 {os.getpid()} 为Filter任务初始化了事件循环")
            
            # 提前实例化模型，这样在所有任务中可以重用
            model = OpenAIModel(model_name)
            logger.debug(f"工作进程 {os.getpid()} 实例化了模型 {model_name}")
            
            # 确定实际的prompt生成器和响应处理器
            processor_kwargs = processor_kwargs_dict or {}
            
            if is_default_filter_generator:
                filter_prompt_generator = AsyncBatchProcessor._default_filter_prompt_generator
                # 如果使用默认生成器，确保filter_instruction参数存在
                if filter_instruction:
                    processor_kwargs['filter_instruction'] = filter_instruction
            else:
                filter_prompt_generator = filter_generator
            
            if is_default_filter_processor:
                filter_response_processor = AsyncBatchProcessor._default_filter_response_processor
                # 如果使用默认处理器，确保filter_instruction参数存在
                if filter_instruction:
                    processor_kwargs['filter_instruction'] = filter_instruction
            else:
                filter_response_processor = filter_processor
            
            # 为每个数据项创建任务
            tasks = []
            for key, value in items_chunk.items():
                task = AsyncBatchProcessor._static_core_process_filter_item(
                    key, value, model, filter_prompt_generator, filter_response_processor, 
                    filter_n, **processor_kwargs  # 传递filter_n参数
                )
                tasks.append(task)
            
            # 执行所有任务并返回结果
            results = loop.run_until_complete(asyncio.gather(*tasks))
            loop.close()
            
            logger.debug(f"工作进程 {os.getpid()} 完成了 {len(results)} 个Filter任务的处理")
            return results
            
        except Exception as e:
            logger.error(f"Filter工作进程出错: {str(e)}", show_traceback=True)
            # 出错时，为所有数据项返回False (不保留)
            return [(key, False) for key in items_chunk.keys()]
    
    @staticmethod
    def _execute_map_task_in_worker(
        items_chunk: Dict[str, Any],
        model_name: str,
        is_default_map_generator: bool,
        is_default_map_processor: bool,
        map_instruction: Optional[str],
        map_generator: Optional[Callable],
        map_processor: Optional[Callable],
        processor_kwargs_dict: Optional[Dict]
    ) -> List[Tuple[str, Any]]:
        """
        在工作进程中执行一批Map任务。
        
        参数:
            items_chunk: 要处理的数据项字典 (一个较大字典的子集)
            model_name: 模型的符号名称
            is_default_map_generator: 是否使用默认的map prompt生成器
            is_default_map_processor: 是否使用默认的map响应处理器
            map_instruction: map指令文本 (如果使用默认生成器)
            map_generator: 自定义map prompt生成器 (如果不使用默认生成器)
            map_processor: 自定义map响应处理器 (如果不使用默认处理器)
            processor_kwargs_dict: 额外参数字典
            
        返回:
            结果列表，每个元素是 (item_key, processed_item) 元组
        """
        # 初始化异步任务所需的事件循环
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            logger.debug(f"工作进程 {os.getpid()} 为Map任务初始化了事件循环")
            
            # 提前实例化模型，这样在所有任务中可以重用
            model = OpenAIModel(model_name)
            logger.debug(f"工作进程 {os.getpid()} 实例化了模型 {model_name}")
            
            # 确定实际的prompt生成器和响应处理器
            processor_kwargs = processor_kwargs_dict or {}
            
            if is_default_map_generator:
                map_prompt_generator = AsyncBatchProcessor._default_map_prompt_generator
                # 如果使用默认生成器，确保map_instruction参数存在
                if map_instruction:
                    processor_kwargs['map_instruction'] = map_instruction
            else:
                map_prompt_generator = map_generator
            
            if is_default_map_processor:
                map_response_processor = AsyncBatchProcessor._default_map_response_processor
                # 如果使用默认处理器，确保map_instruction参数存在
                if map_instruction:
                    processor_kwargs['map_instruction'] = map_instruction
            else:
                map_response_processor = map_processor
            
            # 为每个数据项创建任务
            tasks = []
            for key, value in items_chunk.items():
                task = AsyncBatchProcessor._static_core_process_map_item(
                    key, value, model, map_prompt_generator, map_response_processor, **processor_kwargs
                )
                tasks.append(task)
            
            # 执行所有任务并返回结果
            results = loop.run_until_complete(asyncio.gather(*tasks))
            loop.close()
            
            logger.debug(f"工作进程 {os.getpid()} 完成了 {len(results)} 个Map任务的处理")
            return results
            
        except Exception as e:
            logger.error(f"Map工作进程出错: {str(e)}", show_traceback=True)
            # 出错时，为所有数据项返回None
            return [(key, None) for key in items_chunk.keys()]
    
    # --- 将数据分块的辅助方法 ---
    @staticmethod
    def _chunk_dict(data: Dict[str, Any], chunk_size: int) -> List[Dict[str, Any]]:
        """
        将字典分成多个子字典（块）。
        
        参数:
            data: 要分块的字典
            chunk_size: 每块包含的最大项数
            
        返回:
            字典块的列表
        """
        items = list(data.items())
        chunks = []
        
        for i in range(0, len(items), chunk_size):
            chunk = dict(items[i:i + chunk_size])
            chunks.append(chunk)
        
        return chunks 