---
description: MeowAgent 的消息系统由 @message.py 定义，提供了结构化的消息类型和统一的序列化/反序列化机制，是智能体与用户、工具交互的基础。
globs: 
alwaysApply: false
---
# MeowAgent 消息系统

MeowAgent 的消息系统由 [message.py](mdc:meowagent/message.py) 定义，提供了结构化的消息类型和统一的序列化/反序列化机制，是智能体与用户、工具交互的基础。

## 消息类型体系

消息类型继承体系如下：

```
Message (抽象基类)
├── UserMessage
├── AssistantMessage
├── SystemMessage
│   └── AgentSystemMessage
└── ToolMessage
```

每种消息类型有特定的角色和属性，遵循与 OpenAI 兼容的消息格式：

### `Message` 抽象基类

```python
class Message(ABC):
    """消息抽象基类"""
    
    def __init__(
        self,
        content: Any,
        timestamp: Optional[datetime] = None,
        id: Optional[str] = None,
        summary: Optional[str] = None,
        display_style: Optional[str] = None,
        **kwargs
    ):
        # 基础属性...
    
    @abstractmethod
    def get_role(self) -> str:
        """返回消息角色（如"user", "assistant", "system", "tool"）"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """将消息序列化为字典，移除None值字段"""
        # 序列化逻辑...
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """工厂方法，从字典反序列化消息对象"""
        # 反序列化逻辑...
```

### 具体消息类型

- **`UserMessage`**：表示用户输入的消息
  - `content`: 用户输入的文本
  - `role`: 固定为 "user"
  - `display_style`: 固定为 "user"

- **`AssistantMessage`**：表示智能体的回复
  - `content`: 文本回复（如果只有工具调用可为None）
  - `tool_calls`: 可选的工具调用列表
  - `role`: 固定为 "assistant"
  - `display_style`: 根据内容为 "assistant_text" 或 "assistant_tool_call"

- **`SystemMessage`**：表示系统指令
  - `content`: 系统指令文本
  - `role`: 固定为 "system"
  - `display_style`: 根据内容为 "system_compact" 或 "system_hidden"

- **`AgentSystemMessage`**：结构化系统消息（继承自SystemMessage）
  - `base_prompt`: 核心系统提示
  - `library_prompts`: 来自工具库的提示列表
  - `few_shot_examples`: few-shot示例列表（不持久化）
  - `display_style`: 固定为 "agent_system"

- **`ToolMessage`**：表示工具执行结果
  - `content`: 工具执行的文本结果
  - `tool_call_id`: 关联的工具调用ID
  - `role`: 固定为 "tool"
  - `display_style`: 固定为 "tool_result"

## 工具调用数据结构

工具调用使用辅助类表示：

```python
@dataclass
class ToolCallFunction:
    """表示工具调用中的函数"""
    name: str
    arguments: str

@dataclass
class ToolCall:
    """表示模型请求的工具调用"""
    id: str
    function: ToolCallFunction
```

## 消息处理流程

消息在 MeowAgent 中的生命周期：

1. **创建**：根据不同来源创建不同类型的消息对象
   ```python
   # 创建用户消息
   user_message = UserMessage(content="Hello world")
   
   # 创建助手纯文本消息
   assistant_message = AssistantMessage(content="I'm MeowAgent")
   
   # 创建带工具调用的助手消息
   assistant_message = AssistantMessage(
       content=None,
       tool_calls=[
           ToolCall(
               id="call_abc123",
               function=ToolCallFunction(
                   name="memory.read_file",
                   arguments='{"path": "/notes.txt"}'
               )
           )
       ]
   )
   ```

2. **存储**：通过 `History` 类将消息持久化到数据库
   ```python
   await history.add_message(message)
   ```

3. **序列化/反序列化**：
   ```python
   # 序列化为字典
   message_dict = message.to_dict()
   
   # 反序列化为消息对象
   message = Message.from_dict(message_dict)
   ```

4. **格式化**：通过 `RendererPipeline` 将消息转换为特定格式
   ```python
   # 转换为OpenAI API格式
   openai_messages = renderer_pipeline.render(messages)
   ```

## 消息渲染

[renderer.py](mdc:meowagent/renderer.py) 定义了消息渲染系统，基于责任链模式：

```
RendererPipeline
├── ReorderRenderer - 处理AgentSystemMessage
├── ExcludeSystemRenderer - 选择性移除SystemMessage
├── BasicRenderer - 转换为OpenAI API格式
└── TrimRenderer - 根据token限制裁剪消息
```

预定义的渲染管道：
- `DEFAULT_RENDERER_PIPELINE`: 用于模型API输入
- `MEMORY_PROMPT_PIPELINE`: 用于记忆系统的字符串格式

