"""
该模块为智能体框架定义了标准化的消息结构，用于表示对话中的各种交互。
它围绕一个抽象基类 `Message` 构建，并提供了多个具体的子类来封装不同类型的通信内容，
如用户输入、助手回复、系统指令和工具交互。

核心组件与功能:

1.  `ToolCallFunction` 和 `ToolCall`:
    *   辅助类，分别用于表示大型语言模型 (LLM) 请求的工具调用中的函数名称、参数，以及整个工具调用的ID和函数详情。
    *   这些结构用于 `AssistantMessage` 中，以表示模型希望执行的工具。

2.  `Message` (抽象基类):
    *   所有具体消息类型的父类，定义了通用属性和方法。
    *   通用属性包括:
        *   `content`: 消息的主要内容 (类型为 `Any`，具体子类中可能更具体)。
        *   `timestamp`: 消息创建的时间戳 (`datetime`)。
        *   `id`: 消息的唯一标识符 (可选字符串, 例如 MongoDB `_id`)。
        *   `summary`: 消息内容的简要概括 (可选字符串)。
        *   `display_style`: 用于UI展示的风格提示 (字符串, 如 "user", "assistant_text")。
        *   `object`: 明确记录消息的类名 (例如 "UserMessage")，用于精确的序列化和反序列化。
    *   抽象方法 `get_role()`: 返回消息的角色 (如 "user", "assistant", "system", "tool")，主要用于与外部LLM API (如OpenAI) 的格式兼容。
    *   `to_dict()`: 将消息对象序列化为一个字典，移除值为 `None` 的字段，以便存储或传输。
    *   `from_dict(cls, data)` (类方法): 工厂方法，用于从字典反序列化创建相应的 `Message` 子类实例。它依赖 `object` 字段来确定目标类型，并能处理时间戳的多种格式。

3.  具体消息子类 (均继承自 `Message`):
    *   `AssistantMessage`:
        *   表示来自助手 (LLM) 的回复。
        *   `content`: 文本回复 (可选，如果只有工具调用则为 `None`)。
        *   `tool_calls`: 一个可选的 `ToolCall` 对象列表，表示助手请求执行的工具。
        *   `display_style` 根据是否有 `tool_calls` 自动设为 "assistant_tool_call" 或 "assistant_text"。
    *   `UserMessage`:
        *   表示来自用户的输入。
        *   `content`: 用户输入的文本 (通常不为 `None`)。
        *   `display_style` 固定为 "user"。
    *   `SystemMessage`:
        *   表示系统级别的指令或信息。
        *   `content`: 系统消息的文本。
        *   `display_style` 根据内容是否为空设为 "system_compact" 或 "system_hidden"。
    *   `ToolMessage`:
        *   表示工具执行后的结果。
        *   `content`: 工具执行返回的文本结果。
        *   `tool_call_id`: 必须提供，关联到触发此工具的 `AssistantMessage` 中的 `ToolCall` ID。
        *   `display_style` 固定为 "tool_result"。
    *   `AgentSystemMessage` (继承自 `SystemMessage`):
        *   一种结构化的系统消息，用于承载更复杂的系统提示信息，包括基础提示、库提示和 few-shot 示例。
        *   `base_prompt`: 核心的系统提示文本 (也存储在父类的 `content` 字段中)。
        *   `library_prompts`: 一个字符串列表，包含来自不同工具库的提示。
        *   `few_shot_examples`: 一个 `Message` 对象列表的列表，用于提供对话示例 (此字段不进行持久化)。
        *   `display_style` 固定为 "agent_system"。
        *   在序列化 (`to_dict`) 时，它会存储 `base_prompt` 和 `library_prompts`，并用一个布尔标志 `has_few_shot_examples` 表示是否存在 `few_shot_examples`。

4.  `MESSAGE_TYPE_MAP` (字典):
    *   一个映射表，将消息类名的字符串 (如 "UserMessage") 映射到实际的类定义 (如 `UserMessage`)。
    *   供 `Message.from_dict()` 方法在反序列化时查找正确的构造函数。

该模块通过这些精心设计的类，确保了对话历史的结构化、可序列化和可扩展性，
为智能体与 LLM 之间的交互以及对话数据的持久化提供了坚实的基础。
它也考虑了与外部服务 (如 OpenAI API) 的兼容性，同时为前端UI展示提供了必要的元数据。
"""

from datetime import datetime
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union, TypeVar, Type
import uuid
import json
from log import logger

# 类型变量
M = TypeVar('M', bound='Message')

# 工具调用相关类
class ToolCallFunction:
    """工具调用的函数信息"""
    def __init__(self, name: str, arguments: str):
        self.name = name
        self.arguments = arguments

class ToolCall:
    """工具调用信息"""
    def __init__(self, id: str, function: ToolCallFunction):
        self.id = id
        self.function = function

# 抽象消息类
class Message(ABC):
    def __init__(self, content: Optional[Any] = None):
        self.content: Optional[Any] = content
        self.timestamp: datetime = datetime.now()
        self.id: Optional[str] = None # MongoDB _id (string)
        self.summary: Optional[str] = None
        self.display_style: str = "unknown" # 用于UI展示，可能需要重新考虑
        self.object: str = self.__class__.__name__ # 新增: 明确类名

    def get_content(self) -> Optional[Any]:
        return self.content
    
    def get_timestamp(self) -> datetime:
        return self.timestamp
    
    @abstractmethod
    def get_role(self) -> str:
        """获取消息的角色 ('user', 'assistant', 'system', 'tool')，主要用于与外部API兼容"""
        pass

    def set_id(self, new_id: str):
        """设置消息的ID (通常由数据库层调用)"""
        self.id = new_id

    def to_dict(self) -> Dict[str, Any]:
        """将消息对象序列化为字典"""
        msg_dict = {
            "object": self.object,
            "role": self.get_role(), # 保留 role 用于API兼容性
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "id": self.id,
            "summary": self.summary,
            "display_style": self.display_style,
        }
        # 特定子类需要在其 to_dict 中添加额外字段 (如 tool_calls, tool_call_id)
        # 移除这里的通用 tool_call/tool_calls 处理，移到子类
        return {k: v for k, v in msg_dict.items() if v is not None}

    @classmethod
    def from_dict(cls: Type[M], data: Dict[str, Any]) -> M:
        """
        从字典反序列化为具体的消息子类对象。
        这是一个工厂方法，应通过 Message.from_dict 调用。
        """
        object_type_str = data.get("object")
        target_cls = MESSAGE_TYPE_MAP.get(object_type_str) # type: ignore

        if not target_cls:
            logger.warning(f"未知的消息 object 类型 '{object_type_str}'，将回退到 SystemMessage")
            target_cls = SystemMessage # 或者可以选择抛出错误

        # 提取通用字段
        content = data.get("content")
        message_id = data.get("id")
        summary = data.get("summary")
        timestamp_str = data.get("timestamp")
        display_style = data.get("display_style", "unknown") # 默认值

        timestamp = datetime.now() # 默认值
        if timestamp_str:
            # 检查timestamp_str是否已经是datetime对象
            if isinstance(timestamp_str, datetime):
                timestamp = timestamp_str
            else:
                try:
                    # 优先尝试 ISO 格式 (通常是 to_dict() 输出的格式)
                    timestamp = datetime.fromisoformat(timestamp_str)
                    logger.debug(f"成功使用 ISO 格式解析时间戳: {timestamp_str}")
                except (ValueError, TypeError) as e:
                    logger.debug(f"ISO格式解析失败: {e}, 尝试其他格式...")
                    # 尝试多种常见的时间戳格式
                    formats = [
                        '%Y-%m-%d %H:%M:%S.%f',  # 带微秒格式
                        '%Y-%m-%d %H:%M:%S',     # 不带微秒格式
                        '%Y-%m-%dT%H:%M:%S.%f',  # ISO带T格式
                        '%Y-%m-%dT%H:%M:%S',     # ISO不带微秒格式
                        '%Y/%m/%d %H:%M:%S.%f',  # 斜杠分隔日期格式
                        '%Y/%m/%d %H:%M:%S'      # 斜杠分隔不带微秒格式
                    ]
                    
                    parsed = False
                    for fmt in formats:
                        try:
                            timestamp = datetime.strptime(timestamp_str, fmt)
                            logger.debug(f"成功使用格式 '{fmt}' 解析时间戳: {timestamp_str}")
                            parsed = True
                            break
                        except (ValueError, TypeError) as e:
                            logger.debug(f"格式 '{fmt}' 解析失败: {e}")
                            continue
                    
                    if not parsed:
                        # 尝试手动解析
                        try:
                            # 截断微秒部分至6位以内
                            parts = timestamp_str.split('.')
                            if len(parts) == 2:
                                main_part = parts[0]
                                micro_part = parts[1][:6].ljust(6, '0')  # 确保微秒部分恰好6位
                                adjusted_timestamp_str = f"{main_part}.{micro_part}"
                                logger.debug(f"尝试调整后的时间戳字符串: {adjusted_timestamp_str}")
                                timestamp = datetime.strptime(adjusted_timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                                logger.debug(f"手动调整微秒成功解析时间戳: {timestamp_str}")
                                parsed = True
                        except Exception as e:
                            logger.debug(f"手动解析时间戳失败: {e}")
                    
                    if not parsed:
                        logger.warning(f"无法将字符串 '{timestamp_str}' 解析为已知的时间戳格式，使用当前时间")

        # 为特定子类准备参数
        instance_args = {
            "content": content,
            "timestamp": timestamp,
            "summary": summary,
            "message_id": message_id,
        }

        # --- 特定子类的字段处理 ---
        if target_cls == AssistantMessage:
            raw_tool_calls = data.get("tool_calls")
            tool_calls = []
            if isinstance(raw_tool_calls, list):
                for tc_data in raw_tool_calls:
                    if isinstance(tc_data, dict):
                        func_data = tc_data.get("function")
                        if isinstance(func_data, dict):
                            func = ToolCallFunction(
                                name=func_data.get("name", ""),
                                arguments=func_data.get("arguments", "")
                            )
                            tool_call = ToolCall(id=tc_data.get("id", ""), function=func)
                            tool_calls.append(tool_call)
            instance_args["tool_calls"] = tool_calls if tool_calls else None # 使用 None 而不是 []
            # AssistantMessage 的 display_style 在其 __init__ 中决定

        elif target_cls == ToolMessage:
            instance_args["tool_call_id"] = data.get("tool_call_id")
            # ToolMessage 的 display_style 在其 __init__ 中决定

        elif target_cls == AgentSystemMessage:
            # 从 data 中提取 AgentSystemMessage 特有的字段
            instance_args["base_prompt"] = data.get("base_prompt", "") # 确保有默认值
            instance_args["library_prompts"] = data.get("library_prompts", []) # 确保有默认值
            # few_shot_examples 不从 dict 反序列化，总是初始化为空列表
            instance_args["few_shot_examples"] = []
            # content 在 AgentSystemMessage 的 __init__ 中构建，无需传入
            del instance_args["content"] # 从通用参数中移除 content
            # AgentSystemMessage 的 display_style 在其 __init__ 中决定

        # 实例化目标类
        try:
            # 过滤掉值为 None 的参数，除非类定义允许
            filtered_args = {k: v for k, v in instance_args.items() if v is not None or k in target_cls.__init__.__annotations__}
            instance = target_cls(**filtered_args)
        except TypeError as e:
             logger.error(f"实例化 {target_cls.__name__} 出错: {e}. 参数: {filtered_args}")
             # 可以在这里创建一个默认实例或重新抛出异常
             instance = SystemMessage(f"反序列化错误: {e}") # 回退

        # 设置反序列化后可能丢失的字段
        instance.display_style = display_style
        # instance.id 已经在 instance_args 中处理

        return instance # type: ignore

# 具体消息类实现
class AssistantMessage(Message):
    def __init__(self,
                 content: Optional[str] = None, # 助手消息内容可以是 None，如果只有工具调用
                 tool_calls: Optional[List[ToolCall]] = None,
                 timestamp: Optional[datetime] = None,
                 summary: Optional[str] = None,
                 message_id: Optional[str] = None):
        super().__init__(content)
        self.tool_calls: Optional[List[ToolCall]] = tool_calls # 使用 None 表示没有
        self.timestamp = timestamp or datetime.now()
        self.summary = summary
        self.id = message_id
        self.object = self.__class__.__name__
        # 根据是否有工具调用设置默认 display_style
        self.display_style = "assistant_tool_call" if self.tool_calls else "assistant_text"

    def get_role(self) -> str:
        return "assistant"

    def to_dict(self) -> Dict[str, Any]:
        msg_dict = super().to_dict()
        if self.tool_calls:
            msg_dict["tool_calls"] = [
                {
                    "id": tc.id,
                    "type": "function", # OpenAI 需要 type 字段
                    "function": {
                        "name": tc.function.name,
                        "arguments": tc.function.arguments
                    }
                } for tc in self.tool_calls
            ]
        return {k: v for k, v in msg_dict.items() if v is not None}

class UserMessage(Message):
    def __init__(self,
                 content: str, # 用户消息内容通常不为 None
                 timestamp: Optional[datetime] = None,
                 summary: Optional[str] = None,
                 message_id: Optional[str] = None):
        super().__init__(content)
        self.timestamp = timestamp or datetime.now()
        self.summary = summary
        self.id = message_id
        self.object = self.__class__.__name__
        self.display_style = "user"

    def get_role(self) -> str:
        return "user"

class SystemMessage(Message):
    def __init__(self,
                 content: str, # 系统消息内容通常不为 None
                 timestamp: Optional[datetime] = None,
                 summary: Optional[str] = None,
                 message_id: Optional[str] = None):
        super().__init__(content)
        self.timestamp = timestamp or datetime.now()
        self.summary = summary
        self.id = message_id
        self.object = self.__class__.__name__
        # 根据内容设置默认 display_style
        self.display_style = "system_compact" if content else "system_hidden"

    def get_role(self) -> str:
        return "system"

class ToolMessage(Message):
    def __init__(self,
                 content: str, # 工具消息内容通常不为 None
                 tool_call_id: str, # 工具消息必须有关联的 tool_call_id
                 timestamp: Optional[datetime] = None,
                 summary: Optional[str] = None,
                 message_id: Optional[str] = None):
        super().__init__(content)
        self.tool_call_id: str = tool_call_id
        self.timestamp = timestamp or datetime.now()
        self.summary = summary
        self.id = message_id
        self.object = self.__class__.__name__
        self.display_style = "tool_result"

    def get_role(self) -> str:
        return "tool"

    def to_dict(self) -> Dict[str, Any]:
        msg_dict = super().to_dict()
        msg_dict["tool_call_id"] = self.tool_call_id
        return {k: v for k, v in msg_dict.items() if v is not None}

class AgentSystemMessage(SystemMessage):
    """
    结构化系统消息，承载基础提示、库提示和Few-Shot示例。
    在渲染阶段会被展开成基础 Message。数据库中存储的是其结构化表示。
    """
    def __init__(self,
                 base_prompt: str,
                 library_prompts: Optional[List[str]] = None,
                 few_shot_examples: Optional[List[List[Message]]] = None, # 不从DB加载
                 timestamp: Optional[datetime] = None,
                 summary: Optional[str] = None,
                 message_id: Optional[str] = None):

        # content 字段现在存储 base_prompt，因为它是主要的文本内容
        super().__init__(base_prompt, timestamp=timestamp, summary=summary, message_id=message_id)

        self.base_prompt: str = base_prompt # 重复存储以便访问
        self.library_prompts: List[str] = library_prompts or []
        self.few_shot_examples: List[List[Message]] = few_shot_examples or [] # 不持久化
        self.object = self.__class__.__name__
        self.display_style = "agent_system"

    def get_role(self) -> str:
        # 对于 API 来说，它扮演的是 system 角色
        return "system"

    def to_dict(self) -> Dict[str, Any]:
        """序列化 AgentSystemMessage 的结构化信息"""
        msg_dict = super().to_dict()
        # content 已经是 base_prompt，由父类处理
        msg_dict["base_prompt"] = self.base_prompt # 显式添加 base_prompt
        msg_dict["library_prompts"] = self.library_prompts
        # 不序列化 few_shot_examples，因为它不持久化且可能包含复杂对象
        msg_dict["has_few_shot_examples"] = bool(self.few_shot_examples) # 只存储标志
        return {k: v for k, v in msg_dict.items() if v is not None}

# 类型映射
MESSAGE_TYPE_MAP: Dict[str, Type[Message]] = {
    "UserMessage": UserMessage,
    "AssistantMessage": AssistantMessage,
    "SystemMessage": SystemMessage,
    "ToolMessage": ToolMessage,
    "AgentSystemMessage": AgentSystemMessage,
}