"use client"

import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { X } from "lucide-react"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetClose } from "@/components/ui/sheet"

interface ConfigPanelProps {
  model: string
  onModelChange: (model: string) => void
  temperature: number
  onTemperatureChange: (temp: number) => void
  maxLength: number
  onMaxLengthChange: (length: number) => void
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

export default function ConfigPanel({
  model,
  onModelChange,
  temperature,
  onTemperatureChange,
  maxLength,
  onMaxLengthChange,
  isOpen,
  onOpenChange,
}: ConfigPanelProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-[350px] sm:w-[450px] overflow-y-auto scrollbar-thin">
        <SheetHeader className="pb-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex items-center justify-between">
            <SheetTitle>配置</SheetTitle>
            <SheetClose asChild>
              <Button variant="ghost" size="icon">
                <X className="h-4 w-4" />
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>

        <div className="py-6 space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">模型</label>
            <Select value={model} onValueChange={onModelChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                <SelectItem value="claude-3">Claude 3</SelectItem>
                <SelectItem value="llama-3">Llama 3</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">温度</label>
              <span className="text-sm text-gray-500">{temperature.toFixed(1)}</span>
            </div>
            <Slider
              value={[temperature]}
              min={0}
              max={1}
              step={0.1}
              onValueChange={(value) => onTemperatureChange(value[0])}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>精确</span>
              <span>创造</span>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">最大长度</label>
            <Input
              type="number"
              value={maxLength}
              onChange={(e) => onMaxLengthChange(Number.parseInt(e.target.value) || 0)}
              min={1}
              max={10000}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">系统提示词</label>
            <textarea
              className="w-full min-h-[100px] p-2 rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm"
              placeholder="输入系统提示词..."
              defaultValue="你是一个智能助手，可以帮助用户解决各种问题。"
            />
          </div>

          <Button className="w-full">保存配置</Button>
        </div>
      </SheetContent>
    </Sheet>
  )
}
