description: "基础网络搜索工作流。"
metadata:
  version: "0.1"

steps:
  - name: "init"
    description: "初始化对话处理流程"
    operation: "NOP"

  - name: "browser_use"
    description: "通过使用浏览器，从网络中获取信息"
    operation: "EXECUTE"
    actions: 
      - names: 
        - "playwright"
        min_calls: 1
        max_calls: 1
      - names: 
        - "fetch.fetch"
        min_calls: 1
        max_calls: 1


  - name: "check_task_complete"
    description: "检查任务是否完成"
    operation: "CONDITION"
    condition: "is_task_complete"
    condition_description: "检查是否已经获取到足够的信息"
    true_branch: "generate_response"
    false_branch: "browser_use"

  - name: "generate_response"
    description: "结合网络搜索结果生成回复给用户"
    operation: "GENERATE"
    wait_user: true
    next: "init"


registers: {}
