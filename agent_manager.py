"""
作为MeowAgent系统的核心协调单元，`AgentManager` 模块扮演着至关重要的角色。
它被设计为单例模式，统一负责管理智能体（Agent）实例的生命周期、
共享核心资源，并优化整体系统性能。

主要职责与工作流程概述：

1.  **单例与异步初始化机制**：
    -   `AgentManager` 通过 `get_instance()` 类方法确保全局唯一性。
    -   其初始化过程是异步的，利用 `asyncio.Lock` 和 `asyncio.Event` 进行同步控制，
        保证了在管理器完全就绪（所有预加载任务完成）之前，不会被外部访问。

2.  **共享资源池化与管理**：
    -   **语言模型 (`shared_model`)**：集中创建并维护一个 `OpenAIModel` 实例，
        供所有Agent共享，有效减少了API客户端的重复创建和配置开销。
    -   **库定义与配置 (`available_library_classes`, `library_config`, `mcp_config`)**：
        在启动阶段，管理器会预加载所有工具库的类定义，并从 `library.json` 和 `mcp.json`
        文件中加载相关配置，为Agent提供统一的工具和行为设定。
    -   **MCP客户端实例 (`initialized_mcp_libraries`)**：为 `mcp.json` 中定义的每个MCP服务器
        创建并维护一个共享的 `MCPClientLibrary` 实例。这些客户端负责与外部工具服务器
        建立连接、发现远程工具，并将其能力集成到Agent中。

3.  **Agent实例的动态编排**：
    -   **创建 (`create_agent`)**：当需要新的对话会话时，`AgentManager` 会创建一个新的 `Agent` 实例。
        此过程中，会将先前初始化的共享模型、库定义、配置及MCP客户端等资源注入该Agent，
        使其具备完整的运行能力。
    -   **跟踪与检索 (`agents`, `get_agent`)**：管理器通过一个字典 (`agents`) 来存储和快速检索
        当前活跃的Agent实例，以对话ID作为键。
    -   **终止与清理 (`delete_conversation`)**：提供接口来终止一个对话会话。这包括关闭
        对应的Agent实例，释放其占用的特定资源，从管理器中移除该Agent的记录，
        并触发对话历史的删除。

4.  **优雅关闭与资源释放 (`close`)**：
    -   管理器提供 `close()` 方法，用于在系统关闭前执行必要的清理工作，
        尤其是关闭所有已建立的MCP连接，确保资源得到妥善释放。
    -   关闭后会重置单例状态，方便在测试等场景下重新构建和初始化。

5.  **性能优化与系统健壮性**：
    -   通过预加载和资源共享策略，避免了在每个Agent创建时重复执行耗时的初始化操作，
        显著提升了系统的响应速度和资源利用率。
    -   内置了详细的日志记录和异常处理机制，增强了问题排查的便捷性和系统的整体稳定性。

总结来说，`AgentManager` 是一个高度优化的组件，它通过集中管理和智能调度，
确保了多个Agent能够高效、稳定地运行，并能灵活地与外部系统和服务集成。
"""

import asyncio
import os
import json
import traceback
from typing import Any, Optional, Dict, Type, ClassVar, List
from datetime import datetime
from log import logger
from message import UserMessage, SystemMessage
from connection import Connection
from utils import dumps
import config
from agent import Agent, load_library_classes_from_directory
from conversation import Conversation
from model import OpenAIModel
from libraries.library import Library
from libraries.mcp_client import MCPClientLibrary
from history import History


class AgentManager:
    """
    智能体管理器，负责创建和管理Agent实例所需的共享资源
    采用单例模式实现，确保系统中只有一个AgentManager实例
    """
    _instance: ClassVar[Optional['AgentManager']] = None
    _init_lock: ClassVar[asyncio.Lock] = asyncio.Lock()
    _initialized_event: ClassVar[asyncio.Event] = asyncio.Event()
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            # 这个路径理想情况下不应直接使用，因为不会进行完整的异步初始化
            # get_instance是首选方式
            logger.debug("AgentManager.__new__ 被直接调用。实例将被创建但不会在此处完成异步初始化。")
            cls._instance = super(AgentManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        初始化AgentManager，如果是首次创建才执行实际的初始化逻辑
        """
        if hasattr(self, '_initialized_sync') and self._initialized_sync:
            return # 避免重复运行同步部分的初始化
        
        logger.debug("AgentManager __init__ (同步部分) 被调用。")
        self.shared_model: Optional[OpenAIModel] = None
        self._model_init_lock = asyncio.Lock()
        self.available_library_classes: Dict[str, Type[Library]] = {}
        self.library_config: Dict[str, Any] = {}
        self.mcp_config: Dict[str, Any] = {}
        self.initialized_mcp_libraries: Dict[str, Library] = {}
        self.agents: Dict[str, Agent] = {}
        self._preload_task: Optional[asyncio.Task] = None
        self._initialized_sync = True # 标记基本同步初始化已完成
        # _initialized_event 由 _async_initialize 设置

    async def _async_initialize(self):
        """执行异步初始化步骤。"""
        if self._initialized_event.is_set():
            logger.debug("AgentManager 已经完成异步初始化。")
            return
            
        logger.info("AgentManager: 开始异步初始化...")
        # 开始预加载库。preload_libraries 本身处理幂等性。
        await self.preload_libraries()
        self._initialized_event.set() # 发出信号表示异步初始化完成
        logger.info("AgentManager: 异步初始化完成。")

    @classmethod
    async def get_instance(cls) -> 'AgentManager':
        """
        获取AgentManager的单例实例
        
        返回:
            AgentManager的单例实例
        """
        if not cls._instance: # 第一次检查（无锁）
            async with cls._init_lock: # 获取锁
                if not cls._instance: # 第二次检查（有锁）
                    logger.debug("AgentManager.get_instance: 创建新实例。")
                    # 创建实例（调用 __new__ 然后 __init__）
                    # __init__ 设置基本属性。
                    cls._instance = cls() 
                    # 对新实例执行异步初始化。
                    await cls._instance._async_initialize()
                elif not cls._instance._initialized_event.is_set():
                    # 实例已存在但异步初始化尚未完成（例如另一个任务创建了它）
                    logger.debug("AgentManager.get_instance: 实例已存在，等待异步初始化完成。")
                    await cls._instance._initialized_event.wait()
        elif not cls._instance._initialized_event.is_set():
            # 实例已存在（由另一个任务创建）但异步初始化尚未完成。
            # 当 get_instance 被并发调用时可能发生这种情况。
            logger.debug("AgentManager.get_instance: 实例已存在（快速路径），等待异步初始化完成。")
            await cls._instance._initialized_event.wait()
        
        return cls._instance
    
    async def preload_libraries(self):
        """
        预加载库类定义和配置文件，初始化共享库（如MCP客户端）
        
        这是一个关键的优化方法，将耗时的初始化步骤前移到AgentManager中一次性完成，
        而不是在每个Agent创建时重复执行。
        """
        if hasattr(self, '_preload_completed') and self._preload_completed:
            logger.debug("库和配置已经预加载，跳过。")
            return
            
        logger.info("正在预加载库类和配置...")
        try:
            self.available_library_classes = load_library_classes_from_directory()
            logger.info(f"已预加载 {len(self.available_library_classes)} 个库类定义。")
            
            library_config_path = os.path.join(os.getcwd(), "library.json")
            if os.path.exists(library_config_path):
                try:
                    with open(library_config_path, "r", encoding="utf-8") as f:
                        self.library_config = json.load(f)
                    logger.info("成功预加载 library.json 配置。")
                except Exception as e_lib_json:
                    logger.error(f"读取 library.json 出错: {e_lib_json}")
                    self.library_config = {"libraries": []}
            else:
                logger.warning("找不到 library.json，使用空配置。")
                self.library_config = {"libraries": []}
            
            mcp_config_path = os.path.join(os.getcwd(), "mcp.json")
            if os.path.exists(mcp_config_path):
                try:
                    with open(mcp_config_path, "r", encoding="utf-8") as f:
                        self.mcp_config = json.load(f)
                    logger.info("成功预加载 mcp.json 配置。")
                except Exception as e_mcp_json:
                    logger.error(f"读取 mcp.json 出错: {e_mcp_json}")
                    self.mcp_config = {"mcpServers": {}}
            else:
                logger.warning("找不到 mcp.json，使用空配置。")
                self.mcp_config = {"mcpServers": {}}
            
            await self._initialize_shared_mcp_libraries()
            self._preload_completed = True
            logger.info("库和配置预加载完成。")
        except Exception as e_preload:
            logger.error(f"preload_libraries 过程中出错: {traceback.format_exc()}")
            # 确保有回退方案
            self.available_library_classes = self.available_library_classes or {}
            self.library_config = self.library_config or {"libraries": []}
            self.mcp_config = self.mcp_config or {"mcpServers": {}}
            self._preload_completed = False # 出错时标记为未完成
    
    async def _initialize_shared_mcp_libraries(self):
        """
        初始化所有MCP客户端库，这些库可以在所有Agent间共享
        """
        if self.initialized_mcp_libraries and all(lib.is_initialized() for lib in self.initialized_mcp_libraries.values()): # 更强健的检查
            logger.debug("共享MCP库已经初始化，跳过。")
            return
            
        logger.info("正在初始化共享MCP库...")
        mcp_servers = self.mcp_config.get("mcpServers", {})
        if not mcp_servers:
            logger.info("未找到MCP服务器配置，跳过MCP库初始化。")
            return
        
        init_tasks = []
        # 临时字典存储新创建的实例以进行初始化
        created_libs_for_init: Dict[str, MCPClientLibrary] = {}
        
        for server_name, server_conf in mcp_servers.items():
            if server_name in self.initialized_mcp_libraries and self.initialized_mcp_libraries[server_name].is_initialized():
                logger.debug(f"MCP库 {server_name} 已在管理器中初始化，跳过。")
                continue
            
            if not (server_conf.get("url") or (server_conf.get("command") and server_conf.get("args"))):
                logger.warning(f"MCP服务器 '{server_name}' 配置无效（缺少url或command/args），跳过。")
                continue
                
            try:
                mcp_lib = MCPClientLibrary(server_name, server_conf)
                created_libs_for_init[server_name] = mcp_lib
                # 将库实例传递给 _init_mcp_library 以避免任务中自引用问题
                init_tasks.append(asyncio.create_task(self._init_mcp_library(server_name, mcp_lib)))
                logger.debug(f"已创建MCP服务器 {server_name} 的初始化任务。")
            except Exception as e_create_mcp:
                logger.error(f"为 {server_name} 创建MCPClientLibrary实例时出错: {traceback.format_exc()}")

        if init_tasks:
            results = await asyncio.gather(*init_tasks, return_exceptions=True)
            for i, res in enumerate(results):
                # 需要一种方法将结果映射回server_name（如果某些任务未能创建或记录）
                # 暂时，只记录任务失败的错误
                if isinstance(res, Exception):
                    # 这个错误来自_init_mcp_library任务本身
                    logger.error(f"MCP库初始化任务过程中出错: {res}")
        
        logger.info(f"共享MCP库初始化过程完成。已初始化: {len(self.initialized_mcp_libraries)}/{len(mcp_servers)}。")
    
    async def _init_mcp_library(self, server_name: str, mcp_lib: MCPClientLibrary):
        """
        初始化单个MCP库实例并将成功的实例存储在字典中
        
        参数:
            server_name: 服务器名称
            mcp_lib: MCP库实例
        """
        try:
            logger.info(f"正在初始化MCP库: {server_name}...")
            await mcp_lib.initialize() # 这是MCPClientLibrary中的异步方法
            self.initialized_mcp_libraries[server_name] = mcp_lib # 存储成功初始化的库
            logger.info(f"MCP库 {server_name} 初始化成功。")
        except Exception as e_init_mcp:
            logger.error(f"初始化MCP库 {server_name} 失败: {traceback.format_exc()}")
            # 如果初始化失败，不添加到self.initialized_mcp_libraries

    async def initialize_shared_resources(self):
        """
        初始化共享资源（如模型）
        
        参数:
            output_connection: 可选的输出连接，用于发送初始化状态消息
        """
        async with self._model_init_lock:
            # 如果模型已经初始化，直接返回
            if self.shared_model is not None:
                return True
                
            # 获取符号模型名称
            symbolic_model_name = "agent"  # 使用符号名称

            # 检查配置是否存在
            if symbolic_model_name not in config.MODEL_CONFIG:
                logger.error(f"在config.MODEL_CONFIG中未找到符号名称 '{symbolic_model_name}' 的配置。")
                return False

            model_config = config.MODEL_CONFIG[symbolic_model_name]
            actual_model_name = model_config.get("model_name")
            base_url = model_config.get("base_url")

            logger.info(f"使用模型配置: {symbolic_model_name} (实际模型: {actual_model_name or '未指定'})")
            logger.info(f"使用API基础URL: {base_url or '默认URL'}")

            # 创建OpenAI模型实例，所有Agent共享
            self.shared_model = OpenAIModel(symbolic_model_name)
            return True

    async def create_agent(self, conversation_id: str, conversation: Conversation, output_connection: Optional[Connection] = None, agent_config: Optional[Dict[str, Any]] = None) -> Agent:
        """
        创建指定的Agent实例

        参数:
            conversation_id: 对话ID
            conversation: 会话实例
            output_connection: 输出连接

        返回:
            Agent实例
        """
        if not self._initialized_event.is_set():
            logger.warning("AgentManager尚未完全初始化。在创建agent前等待异步初始化完成。")
            await self._initialized_event.wait()

        if not await self._ensure_shared_model_initialized():
            raise RuntimeError("初始化共享模型失败，无法创建agent。")

        # 创建Agent实例，传递预加载和预初始化的资源
        agent = Agent(
            model=self.shared_model, 
            conversation=conversation, 
            output_connection=output_connection,
            available_library_classes=self.available_library_classes,
            library_config=self.library_config,
            initialized_mcp_libraries=self.initialized_mcp_libraries,
            agent_config=agent_config,
        )

        # 发送接入消息
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        join_content = f"对话 {conversation_id} 已接入，当前时间：{now}"
        join_message = UserMessage(content=join_content)
        # 添加到历史记录
        await agent.conversation.add_message(join_message)

        self.agents[conversation_id] = agent
        logger.info(f"对话ID '{conversation_id}' 的Agent已创建并注册。")
        return agent
        
    def list_conversation_ids(self) -> List[str]:
        """
        返回当前AgentManager管理的所有对话ID列表。
        """
        if not hasattr(self, 'agents'):
            return []
        return list(self.agents.keys())

    def get_agent(self, conversation_id: str) -> Optional[Agent]:
        """
        根据对话ID获取已创建的Agent实例

        参数:
            conversation_id: 对话ID

        返回:
            对应的Agent实例，如果不存在则返回None
        """
        # 确保agents字典已初始化
        if not hasattr(self, 'agents'):
            self.agents = {}
            
        # 返回相应的Agent实例，如果不存在则返回None
        return self.agents.get(conversation_id)

    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        删除指定的对话及其相关的Agent实例和历史记录。

        参数:
            conversation_id: 要删除的对话ID。

        返回:
            bool: 如果对话成功删除或不存在则返回True，否则返回False。
        """
        agent_to_delete = self.get_agent(conversation_id)

        if agent_to_delete:
            logger.info(f"准备删除对话ID '{conversation_id}' 的Agent实例...")
            try:
                await agent_to_delete.close() # 关闭Agent特定资源
                logger.info(f"Agent '{conversation_id}' 的资源已关闭。")
            except Exception as e_close:
                logger.error(f"关闭Agent '{conversation_id}' 时出错: {e_close}", show_traceback=True)
                # 根据策略，可以选择即使关闭失败也继续删除，或者在这里返回False
            
            # 从管理器中移除Agent实例
            if conversation_id in self.agents:
                del self.agents[conversation_id]
                logger.info(f"Agent '{conversation_id}' 已从管理器中移除。")
        else:
            logger.info(f"尝试删除对话ID '{conversation_id}'，但未找到活跃的Agent实例。仍将尝试删除历史记录。")

        # 删除对话历史
        # 即使没有活跃的Agent实例，历史记录也可能存在
        history_deleter = History(conversation_id) # 创建一个History实例用于删除
        try:
            delete_history_success = await history_deleter.delete_all_messages_for_conversation()
            if delete_history_success:
                logger.info(f"对话ID '{conversation_id}' 的历史记录已成功删除。")
            else:
                logger.warning(f"删除对话ID '{conversation_id}' 的历史记录失败或没有历史记录。")
            # 对于API来说，即使历史记录删除失败或不存在，只要Agent被处理了，也可以认为是部分成功的
            # 但如果agent不存在且历史记录删除也失败，可能需要返回False
            if not agent_to_delete and not delete_history_success:
                 return False # 既没有Agent也没有历史记录删除成功
            return True # 只要Agent被处理或历史被成功删除
        except Exception as e_history_delete:
            logger.error(f"删除对话ID '{conversation_id}' 的历史记录时发生意外错误: {e_history_delete}", show_traceback=True)
            return False

    async def close(self):
        """关闭AgentManager和所有共享资源"""
        logger.info("AgentManager关闭方法被调用。正在关闭共享MCP库...")
        close_tasks = []
        for name, lib in self.initialized_mcp_libraries.items():
            if hasattr(lib, 'close') and callable(lib.close):
                logger.info(f"正在关闭共享MCP库: {name}")
                # 假设MCPClientLibrary.close()是异步的
            close_tasks.append(lib.close())
        
        if close_tasks:
            results = await asyncio.gather(*close_tasks, return_exceptions=True)
            for i, res in enumerate(results):
                if isinstance(res, Exception):
                    logger.error(f"关闭MCP库任务时出错: {res}")
        
        self.initialized_mcp_libraries.clear()
        # 如果管理器显式拥有它们的生命周期，则关闭单个agent（目前不是这种情况）
        # for agent_id, agent_instance in self.agents.items():
        #    logger.info(f"正在关闭agent: {agent_id}")
        #    await agent_instance.close()
        # self.agents.clear()

        logger.info("AgentManager共享资源已关闭。")
        # 重置静态实例和事件，以便在测试或特定场景中可能重新创建
        AgentManager._instance = None
        AgentManager._initialized_event.clear()

    async def _ensure_shared_model_initialized(self) -> bool: # 为清晰起见重命名
        """如果尚未初始化，则初始化共享模型。"""
        if self.shared_model is not None:
            return True
        
        async with self._model_init_lock:
            if self.shared_model is not None:
                return True # 获取锁后再次检查

            symbolic_model_name = "agent"
            if symbolic_model_name not in config.MODEL_CONFIG:
                logger.error(f"在config.MODEL_CONFIG中未找到'{symbolic_model_name}'的模型配置。")
                return False

            model_config_params = config.MODEL_CONFIG[symbolic_model_name]
            actual_model_name = model_config_params.get("model_name")
            base_url = model_config_params.get("base_url")
            logger.info(f"使用配置初始化共享模型: '{symbolic_model_name}' (实际模型: {actual_model_name or '默认'}, URL: {base_url or '默认'})。")
            
            try:
                self.shared_model = OpenAIModel(symbolic_model_name)
                logger.info("共享模型初始化成功。")
                return True
            except Exception as e_model_init:
                logger.error(f"初始化共享模型失败: {e_model_init}", show_traceback=True)
                self.shared_model = None # 确保失败时为None
                return False