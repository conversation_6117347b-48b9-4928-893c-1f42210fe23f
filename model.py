"""
该模块为与大型语言模型 (LLM) 的交互提供了一个抽象层和具体实现。
它旨在统一不同 LLM 提供者的接口，并简化在智能体框架中使用这些模型的过程。

核心组件与功能:

1.  `Model` (抽象基类):
    *   定义了所有 LLM 实现必须遵循的通用接口。
    *   包含一个构造函数 `__init__(self, provider, api_key)` 用于初始化提供者信息和 API 密钥。
    *   声明了一个核心的抽象异步方法 `generate(self, messages, tools=None, **kwargs)`。
        *   `messages`: 对话历史，通常是一个符合特定格式 (如OpenAI格式) 的消息对象列表。
        *   `tools`: (可选) 一个工具列表，供 LLM 在需要时调用以获取额外信息或执行操作。
        *   `**kwargs`: 其他特定于模型的参数 (如 `temperature`, `max_tokens`)。
        *   该方法应返回一个包含模型响应的对象。

2.  `OpenAIModel` (继承自 `Model`):
    *   `Model` 接口的具体实现，用于与兼容 OpenAI API 的 LLM (包括 OpenAI 官方模型以及其他通过类似 API 暴露的模型) 进行交互。
    *   **初始化 (`__init__(self, symbolic_name)`)**:
        *   接受一个 `symbolic_name` (符号名称)，该名称用于从全局配置 (`config.MODEL_CONFIG`) 中查找模型的具体设置 (如 `api_key`, `base_url`, `model_name`, `tokenizer_name` 等)。
        *   如果配置中指定了 `tokenizer_name`，它会尝试使用 `transformers.AutoTokenizer` 加载对应的分词器。为了提高效率，加载过的分词器会被缓存在类变量 `_tokenizer_cache` 中。
        *   创建一个 `openai.AsyncOpenAI` 客户端实例，用于后续的异步 API 调用。
    *   **配置获取 (`_get_model_config(self)`)**:
        *   一个辅助方法，根据 `self.symbolic_name` 从 `config.MODEL_CONFIG` 安全地获取模型配置字典。
    *   **Token计数 (`count_tokens(self, messages: List[Dict])`)**:
        *   如果分词器已加载，此方法可以使用分词器的 `apply_chat_template` 和调用自身来估算给定消息列表的 token 数量。这对于管理上下文长度和成本控制非常有用。
    *   **核心生成逻辑 (`generate(self, messages, tools=None, stream_callback=None, n=1, **kwargs)`)**:
        *   实现了父类的 `generate` 方法。
        *   从配置中获取实际的模型名称 (`model_name`) 和其他参数 (如 `max_tokens`, `temperature`, `top_p`, `min_p`, `top_k`, `enable_thinking`)。
        *   构建传递给 OpenAI API `chat.completions.create` 方法的参数字典，默认启用流式输出 (`stream=True`)。
        *   如果提供了 `tools` 参数，会将其包含在 API 请求中。
        *   允许通过 `**kwargs` 覆盖默认或配置中的参数值。
        *   **流式处理**:
            *   异步迭代从 API 返回的 `completion_stream`。
            *   对于流中的每个 `chunk`:
                *   提取 `delta` (增量变化)。
                *   累积文本内容 (`delta.content`) 到 `final_content`。
                *   如果提供了 `stream_callback` 函数，会用包含 `{ "type": "content_delta", "content": delta.content }` 的字典调用它，以实现内容的实时流式传输。
                *   处理 `delta.model_extra.get("reasoning_content")` (如果存在)，累积到 `final_reasoning_content`，并通过 `stream_callback` 以 `{ "type": "reasoning_delta", ... }` 的形式流式传输。
                *   处理工具调用 (`delta.tool_calls`):
                    *   工具调用信息 (ID, 函数名, 函数参数) 会被逐步累积到一个 `tool_calls_dict` 中。
                    *   通过 `stream_callback` 发送 `{ "type": "tool_call_start", "id": ... }` (当新的工具调用开始时) 和 `{ "type": "tool_call_delta", "id": ..., "name": ..., "arguments": ... }` (当工具调用的名称或参数有增量更新时)。
            *   流处理结束后，如果提供了 `stream_callback`，会调用它并传递 `{ "type": "done" }` 来表示流已结束。
        *   **响应构建**:
            *   将累积的 `final_content`、`final_reasoning_content` 和解析后的 `final_tool_calls` (从 `tool_calls_dict` 构建的 `ToolCall` 对象列表) 封装到一个自定义的 `FinalMessage` 对象中返回。
            *   `FinalMessage`, `ToolCall`, `Function` 是在 `generate` 方法内部定义的简单数据类，用于结构化最终的响应。
            *   包含错误处理，如果 API 调用失败，会记录错误并抛出 `RuntimeError`。

该模块通过抽象化模型交互，使得更换或添加新的 LLM 提供者变得更加容易，
同时 `OpenAIModel` 提供了对流式响应、工具调用和精细参数控制的强大支持，
这对于构建响应迅速且功能丰富的智能体至关重要。
配置驱动的设计也增强了灵活性和可维护性。
"""

import traceback
from abc import ABC, abstractmethod
from openai import AsyncOpenAI
import config  # 导入配置模块
import json
import asyncio
import os
import tempfile
import time
from pathlib import Path
from log import logger
from transformers.models.auto.tokenization_auto import AutoTokenizer
from typing import List, Dict, Any, TYPE_CHECKING, Optional

# 避免循环导入
if TYPE_CHECKING:
    from message import Message, AssistantMessage

# 模型接口
class Model(ABC):
    def __init__(self, provider, api_key):
        self.provider = provider
        self.api_key = api_key
    
    # 统一的接口方法
    @abstractmethod
    async def generate(self, messages, tools=None, **kwargs):
        """
        统一的模型生成接口，用于处理对话和工具调用
        
        参数:
            messages: 消息历史（OpenAI格式的消息列表）
            tools: 可用工具列表（可选）
            **kwargs: 额外参数
            
        返回:
            模型响应
        """
        pass


# OpenAI模型实现
class OpenAIModel(Model):
    # 添加类变量，用于缓存已加载的tokenizer
    _tokenizer_cache = {}
    
    def __init__(self, symbolic_name):
        """初始化OpenAI模型，api_key 从 MODEL_CONFIG 中读取"""
        # 获取模型配置
        if symbolic_name not in config.MODEL_CONFIG:
            logger.error(f"在 config.MODEL_CONFIG 中未找到符号名称 '{symbolic_name}' 的配置。")
            raise ValueError(f"未找到模型配置: {symbolic_name}")
        model_config = config.MODEL_CONFIG[symbolic_name]
        api_key = model_config.get("api_key")
        if not api_key:
            logger.error("在模型配置中未找到 api_key 字段。")
            raise ValueError("模型配置缺少 api_key 字段")
        # 初始化父类，设置提供者与 API 密钥
        super().__init__('openai', api_key)
        self.symbolic_name = symbolic_name

        # 获取基础 URL 和 tokenizer
        base_url = model_config.get("base_url")
        tokenizer_name = model_config.get("tokenizer_name")
        
        if tokenizer_name:
            # 检查缓存中是否已存在此tokenizer
            if tokenizer_name in OpenAIModel._tokenizer_cache:
                self.tokenizer = OpenAIModel._tokenizer_cache[tokenizer_name]
                logger.debug(f"使用缓存的tokenizer: {tokenizer_name}")
            else:
                try:
                    logger.info(f"首次加载tokenizer: {tokenizer_name}")
                    self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
                    # 缓存tokenizer以供后续使用
                    OpenAIModel._tokenizer_cache[tokenizer_name] = self.tokenizer
                    logger.info(f"成功加载tokenizer: {tokenizer_name}")
                except Exception as e:
                    logger.error(f"加载tokenizer失败: {tokenizer_name}, 错误: {str(e)}")
                    self.tokenizer = None
        else:
            logger.warning("未指定tokenizer，无法进行token计数")
            self.tokenizer = None

        # 创建 OpenAI 客户端
        self.client = AsyncOpenAI(api_key=api_key, base_url=base_url)
    
    def _get_model_config(self) -> dict:
        """根据符号名称获取模型配置字典"""
        if self.symbolic_name in config.MODEL_CONFIG:
            return config.MODEL_CONFIG[self.symbolic_name]
        else:
            # 返回一个空字典或包含默认值的字典，以避免后续的KeyError
            logger.warning(f"警告: 使用默认模型参数，因为在config.MODEL_CONFIG中未找到符号名称 '{self.symbolic_name}'。")
            return {}
    
    def count_tokens(self, messages: List[Dict]) -> int:
        """
        计算消息列表的token数
        
        参数:
            messages: OpenAI格式的消息列表
            
        返回:
            token数量，如果tokenizer未初始化则返回-1
        """
        if not self.tokenizer:
            raise ValueError("无法计算token数: tokenizer未初始化")
            
        try:
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            tokens = self.tokenizer(text)
            return len(tokens.input_ids)
        except Exception as e:
            logger.error(f"计算token数出错: {str(e)}")
            return -1
    
    # 实现统一的生成方法，替代原来的completion和chat_completion
    async def generate(self, messages, tools=None, stream_callback=None, n=1, **kwargs):
        """
        统一的模型生成接口，处理对话和工具调用
        
        参数:
            messages: 消息历史（OpenAI格式的消息列表）
            tools: 可用工具列表（可选）
            stream_callback: 流式回调函数，接收delta内容（可选）
            n: 返回的候选回答数量，默认为1。当n>1时，stream将自动设置为False
            **kwargs: 额外参数，如temperature, max_tokens等
            
        返回:
            当n=1时: 返回单个模型响应
            当n>1时: 返回模型响应列表
        """
        try:
            model_config = self._get_model_config()
            actual_model_name = model_config.get("model_name")
            
            # 构建基本参数
            params = {
                "model": actual_model_name,
                "messages": messages,  # 直接使用传入的OpenAI格式消息
                "extra_body": {
                },
            }

            # 当n>1时，设置stream=False
            if n > 1:
                params["stream"] = False
                params["n"] = n
            else:
                params["stream"] = True  # 只有n=1时才启用流式输出

            if model_config.get("max_tokens"):
                params["max_tokens"] = model_config.get("max_tokens")
            if model_config.get("temperature"):
                params["temperature"] = model_config.get("temperature")
            if model_config.get("top_p"):
                params["top_p"] = model_config.get("top_p")

            if model_config.get("min_p"):
                params["extra_body"]["min_p"] = model_config.get("min_p")
            if model_config.get("top_k"):
                params["extra_body"]["top_k"] = model_config.get("top_k")
            if model_config.get("enable_thinking"):
                params["extra_body"]["enable_thinking"] = True
            
            # 添加工具参数（如果提供）
            if tools:
                params["tools"] = tools
                
            # 允许kwargs覆盖默认值或配置值
            params.update(kwargs)
            
            # 处理非流式响应 (n > 1 的情况)
            if n > 1:
                completion = await self.client.chat.completions.create(**params)
                
                # 导入message.py中的类
                from message import AssistantMessage, ToolCall, ToolCallFunction
                
                # 处理多个选择
                results = []
                for choice in completion.choices:
                    message = choice.message
                    
                    # 处理工具调用
                    tool_calls = None
                    if hasattr(message, 'tool_calls') and message.tool_calls:
                        tool_calls = []
                        for tc in message.tool_calls:
                            if hasattr(tc, 'id') and hasattr(tc, 'function'):
                                func = ToolCallFunction(
                                    name=getattr(tc.function, 'name', ''),
                                    arguments=getattr(tc.function, 'arguments', '')
                                )
                                tool_calls.append(ToolCall(id=tc.id, function=func))
                    
                    # 创建AssistantMessage对象
                    assistant_message = AssistantMessage(
                        content=getattr(message, 'content', None),
                        tool_calls=tool_calls
                    )
                    
                    # 获取推理内容（如果有）
                    if hasattr(message, 'model_extra') and message.model_extra and message.model_extra.get("reasoning_content"):
                        assistant_message.reasoning_content = message.model_extra.get("reasoning_content")
                    
                    results.append(assistant_message)
                
                return results
            
            # 以下是原始的流式处理逻辑 (n=1 的情况)
            completion_stream = await self.client.chat.completions.create(**params) # <--- API 调用
            
            # 处理流式响应
            final_content = ""
            final_reasoning_content = ""
            tool_calls_dict = {}  # 使用字典而不是列表来存储工具调用
            chunk_received = False # <--- 添加标志
            
            async for chunk in completion_stream: # <--- 迭代流
                chunk_received = True # <--- 标记收到 chunk
                # logger.debug(f"[Model.generate] 收到 chunk: {chunk.dict()}") # 打印原始 chunk
                if not chunk.choices:
                    continue
                    
                delta = chunk.choices[0].delta
                
                # 处理内容
                if delta.content is not None:
                    final_content += delta.content
                    # 如果提供了回调函数，调用它处理content delta
                    if stream_callback:
                        await stream_callback({
                            "type": "content_delta", 
                            "content": delta.content
                        })

                # 处理推理内容
                if delta.model_extra and delta.model_extra.get("reasoning_content"):
                    reasoning_delta = delta.model_extra.get("reasoning_content")
                    final_reasoning_content += reasoning_delta
                    # 如果提供了回调函数，调用它处理reasoning delta
                    if stream_callback:
                        await stream_callback({
                            "type": "reasoning_delta", 
                            "content": reasoning_delta
                        })
                    
                # 处理工具调用
                if delta.tool_calls is not None:
                    for tool_call in delta.tool_calls:
                        # 使用工具调用index或id作为字典键
                        tool_index = tool_call.index if hasattr(tool_call, 'index') else tool_call.id
                       
                        # 如果这是一个新的工具调用ID，初始化字典条目
                        if tool_index not in tool_calls_dict:
                            tool_calls_dict[tool_index] = {
                                "id": tool_call.id,
                                "function": {"name": "", "arguments": ""}
                            }
                            # 如果提供了回调函数，通知新工具调用开始
                            if stream_callback:
                                await stream_callback({
                                    "type": "tool_call_start",
                                    "id": tool_call.id
                                })
                        
                        # 收集工具调用数据
                        tool_call_updated = False
                        
                        if tool_call.function and hasattr(tool_call.function, 'name') and tool_call.function.name:
                            tool_calls_dict[tool_index]["function"]["name"] += tool_call.function.name
                            tool_call_updated = True
                        
                        if tool_call.function and hasattr(tool_call.function, 'arguments') and tool_call.function.arguments:
                            tool_calls_dict[tool_index]["function"]["arguments"] += tool_call.function.arguments
                            tool_call_updated = True
                        
                        # 如果工具调用有更新且提供了回调函数
                        if tool_call_updated and stream_callback:
                            await stream_callback({
                                "type": "tool_call_delta",
                                "id": tool_call.id,
                                "name": tool_call.function.name if hasattr(tool_call.function, 'name') else None,
                                "arguments": tool_call.function.arguments if hasattr(tool_call.function, 'arguments') else None
                            })
            
            if not chunk_received:
                logger.warning("流处理完成，但未收到任何数据块！") # <--- 改为中文日志
            
            # 如果有回调函数，发送完成信号
            if stream_callback:
                await stream_callback({"type": "done"})
            
            # 导入message.py中的类
            from message import AssistantMessage, ToolCall, ToolCallFunction
            
            # 处理工具调用（如果有）
            tool_calls = None
            if tool_calls_dict:
                tool_calls = []
                for tc_data in tool_calls_dict.values():
                    function = ToolCallFunction(
                        name=tc_data["function"]["name"],
                        arguments=tc_data["function"]["arguments"]
                    )
                    tool_call = ToolCall(id=tc_data["id"], function=function)
                    tool_calls.append(tool_call)
            
            # 创建AssistantMessage对象
            assistant_message = AssistantMessage(
                content=final_content,
                tool_calls=tool_calls
            )
            
            # 添加推理内容（如果有）
            if final_reasoning_content:
                assistant_message.reasoning_content = final_reasoning_content
            
            return assistant_message
        except Exception as e:
            logger.error(f'OpenAI API调用失败 (使用模型 {self.symbolic_name}): {traceback.format_exc()}')
            raise RuntimeError("模型调用失败")

    async def batch_generate(
        self,
        conversations: Dict[str, List['Message']],
        tools: Optional[List[Dict]] = None,
        **kwargs
    ) -> Dict[str, 'AssistantMessage']:
        """
        批量生成响应，使用阿里云百炼的Batch接口
        
        参数:
            conversations: 对话字典，key为自定义custom_id，value为Message列表
            tools: 可用工具列表（可选）
            **kwargs: 额外参数，如temperature, max_tokens等
            
        返回:
            Dict[str, AssistantMessage]: 结果字典，key为custom_id，value为AssistantMessage
        """
        if not conversations:
            logger.warning("batch_generate: 未提供对话字典")
            return {}
            
        try:
            start_time = time.time()
            logger.info(f"开始批处理生成，对话数量: {len(conversations)}")
            
            # 1. 准备JSONL数据
            jsonl_data = await self._prepare_batch_jsonl(conversations, tools, **kwargs)
            
            # 2. 创建临时文件并上传
            input_file_id = await self._upload_batch_file(jsonl_data)
            
            # 3. 创建batch任务
            batch_id = await self._create_batch_job(input_file_id)
            
            # 4. 等待任务完成
            await self._wait_for_batch_completion(batch_id)
            
            # 5. 下载并解析结果
            results = await self._download_and_parse_batch_results(batch_id, list(conversations.keys()))
            
            elapsed = time.time() - start_time
            logger.info(f"批处理生成完成，耗时 {elapsed:.2f}秒")
            
            return results
            
        except Exception as e:
            logger.error(f'Batch API调用失败 (使用模型 {self.symbolic_name}): {traceback.format_exc()}')
            raise RuntimeError(f"批量模型调用失败: {str(e)}")

    async def _prepare_batch_jsonl(
        self,
        conversations: Dict[str, List['Message']],
        tools: Optional[List[Dict]],
        **kwargs
    ) -> str:
        """准备批处理的JSONL数据"""
        model_config = self._get_model_config()
        actual_model_name = model_config.get("model_name")
        
        jsonl_lines = []
        
        for custom_id, messages in conversations.items():
            # 将Message对象转换为OpenAI格式
            openai_messages = []
            for msg in messages:
                openai_msg = {
                    "role": msg.get_role(),
                    "content": msg.get_content()
                }
                
                # 处理AssistantMessage的tool_calls
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    openai_msg["tool_calls"] = []
                    for tc in msg.tool_calls:
                        openai_msg["tool_calls"].append({
                            "id": tc.id,
                            "type": "function",
                            "function": {
                                "name": tc.function.name,
                                "arguments": tc.function.arguments
                            }
                        })
                
                # 处理ToolMessage的tool_call_id
                if hasattr(msg, 'tool_call_id') and msg.tool_call_id:
                    openai_msg["tool_call_id"] = msg.tool_call_id
                
                openai_messages.append(openai_msg)
            
            # 构建请求体
            body = {
                "model": actual_model_name,
                "messages": openai_messages,
                "stream": True,  
            }
            
            # 添加配置中的参数
            if model_config.get("max_tokens"):
                body["max_tokens"] = model_config.get("max_tokens")
            if model_config.get("temperature"):
                body["temperature"] = model_config.get("temperature")
            if model_config.get("top_p"):
                body["top_p"] = model_config.get("top_p")
            if model_config.get("min_p"):
                body["min_p"] = model_config.get("min_p")
            if model_config.get("top_k"):
                body["top_k"] = model_config.get("top_k")
            if model_config.get("enable_thinking"):
                body["enable_thinking"] = True
            
            # 添加工具参数
            if tools:
                body["tools"] = tools
            
            # 允许kwargs覆盖参数
            body.update(kwargs)
            
            # 构建JSONL行
            jsonl_line = {
                "custom_id": custom_id,
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": body
            }
            
            jsonl_lines.append(json.dumps(jsonl_line, ensure_ascii=False))
        
        return '\n'.join(jsonl_lines)

    async def _upload_batch_file(self, jsonl_data: str) -> str:
        """上传JSONL文件到阿里云"""
        logger.debug("正在上传批处理文件...")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False, encoding='utf-8') as f:
            f.write(jsonl_data)
            temp_file_path = f.name
        
        try:
            # 上传文件
            with open(temp_file_path, "rb") as f:
                file_object = await self.client.files.create(file=f, purpose="batch")
            
            logger.debug(f"文件上传成功，文件ID: {file_object.id}")
            return file_object.id
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

    async def _create_batch_job(self, input_file_id: str) -> str:
        """创建批处理任务"""
        logger.debug("正在创建批处理任务...")
        
        batch = await self.client.batches.create(
            input_file_id=input_file_id,
            endpoint="/v1/chat/completions",
            completion_window="24h"
        )
        
        logger.debug(f"批处理任务创建成功，任务ID: {batch.id}")
        return batch.id

    async def _wait_for_batch_completion(self, batch_id: str):
        """等待批处理任务完成"""
        logger.info("等待批处理任务完成...")
        
        max_wait_time = 3600  # 最大等待时间1小时
        check_interval = 5    # 检查间隔5秒
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            batch = await self.client.batches.retrieve(batch_id=batch_id)
            logger.debug(f"批处理任务状态: {batch.status}")
            
            if batch.status in ["completed", "failed", "cancelled"]:
                if batch.status == "completed":
                    logger.info("批处理任务完成")
                    return
                elif batch.status == "failed":
                    raise RuntimeError(f"批处理任务失败: {batch.id}")
                elif batch.status == "cancelled":
                    raise RuntimeError(f"批处理任务被取消: {batch.id}")
            
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval
        
        raise RuntimeError(f"批处理任务超时 (超过{max_wait_time}秒): {batch_id}")

    async def _download_and_parse_batch_results(
        self,
        batch_id: str,
        custom_ids: List[str]
    ) -> Dict[str, 'AssistantMessage']:
        """下载并解析批处理结果"""
        logger.debug("正在下载批处理结果...")
        
        # 获取结果文件ID
        batch = await self.client.batches.retrieve(batch_id=batch_id)
        logger.debug(f"批处理任务详情: status={batch.status}, output_file_id={batch.output_file_id}, error_file_id={getattr(batch, 'error_file_id', None)}")
        
        result_text = ""
        
        # 优先处理输出文件
        if batch.output_file_id:
            content = await self.client.files.content(batch.output_file_id)
            result_text = content.text
            logger.debug(f"成功下载输出文件，内容长度: {len(result_text)}")
        
        # 如果没有输出文件，或者输出文件为空，尝试处理错误文件
        if not result_text and hasattr(batch, 'error_file_id') and batch.error_file_id:
            try:
                error_content = await self.client.files.content(batch.error_file_id)
                result_text = error_content.text
                logger.warning(f"没有输出文件，使用错误文件作为结果，内容长度: {len(result_text)}")
            except Exception as e:
                logger.error(f"下载错误文件失败: {e}")
        
        if not result_text:
            raise RuntimeError(f"批处理任务没有可用的结果文件。任务状态: {batch.status}")
        
        # 导入AssistantMessage和相关类
        from message import AssistantMessage, ToolCall, ToolCallFunction
        
        # 解析结果
        results = {}
        
        for line in result_text.strip().split('\n'):
            if not line.strip():
                continue
                
            try:
                result_item = json.loads(line)
                custom_id = result_item.get("custom_id", "")
                
                if custom_id in custom_ids:
                    # 解析响应
                    response = result_item.get("response", {})
                    body = response.get("body", {})
                    
                    # 检查是否有错误
                    if "error" in body:
                        error_info = body["error"]
                        error_message = error_info.get("message", "未知错误")
                        logger.warning(f"对话 {custom_id} 返回错误: {error_message}")
                        results[custom_id] = AssistantMessage(
                            content=f"API错误: {error_message}"
                        )
                    elif response.get("status_code") == 200 and "choices" in body:
                        choices = body.get("choices", [])
                        
                        if choices:
                            # 取第一个选择（因为n=1）
                            choice = choices[0]
                            message = choice.get("message", {})
                            
                            # 处理工具调用
                            tool_calls = None
                            raw_tool_calls = message.get("tool_calls")
                            if raw_tool_calls:
                                tool_calls = []
                                for tc in raw_tool_calls:
                                    function_data = tc.get("function", {})
                                    func = ToolCallFunction(
                                        name=function_data.get("name", ""),
                                        arguments=function_data.get("arguments", "")
                                    )
                                    tool_calls.append(ToolCall(id=tc.get("id", ""), function=func))
                            
                            # 创建AssistantMessage对象
                            assistant_message = AssistantMessage(
                                content=message.get("content"),
                                tool_calls=tool_calls
                            )
                            
                            # 如果有推理内容，可以添加到某个属性中（如果AssistantMessage支持的话）
                            if "reasoning_content" in message:
                                # 可以考虑扩展AssistantMessage类来支持reasoning_content
                                # 暂时作为一个动态属性添加
                                assistant_message.reasoning_content = message["reasoning_content"]
                            
                            results[custom_id] = assistant_message
                        else:
                            logger.warning(f"对话 {custom_id} 没有返回choices")
                            # 创建错误消息
                            results[custom_id] = AssistantMessage(
                                content="批处理未返回有效结果"
                            )
                    else:
                        logger.error(f"对话 {custom_id} 处理失败: {response}")
                        # 创建错误消息
                        results[custom_id] = AssistantMessage(
                            content=f"处理失败: {response.get('status_code', '未知错误')}"
                        )
                
            except (json.JSONDecodeError, KeyError, ValueError) as e:
                logger.error(f"解析批处理结果行时出错: {e}, 行内容: {line[:100]}")
                continue
        
        # 检查结果完整性，为缺失的custom_id创建错误消息
        for custom_id in custom_ids:
            if custom_id not in results:
                logger.warning(f"对话 {custom_id} 没有结果，添加错误占位符")
                results[custom_id] = AssistantMessage(
                    content="批处理结果缺失"
                )
        
        logger.info(f"成功解析 {len(results)} 个对话的批处理结果")
        return results

