import pytest
import pytest_asyncio
import uuid
import asyncio

# 导入测试辅助工具
from tests.cli_tester import CLITester

# 标记所有测试为异步测试
pytestmark = pytest.mark.asyncio

TIMEOUT = 60

agent_config = {
    "default_workflow_name": "python_chat_workflow",
}

async def test_simple_greeting(cli_tester: CLITester):
    """
    测试简单的问候和回复。
    """
    conversation_id = f"test_user_{uuid.uuid4().hex[:8]}" # 生成唯一对话ID

    try:
        # 启动会话
        await cli_tester.start_session(conversation_id, agent_config=agent_config)

        # 发送问候
        user_input = "你好"
        await cli_tester.send_input(conversation_id, user_input)

        # 期望包含问候的助手回复
        expected_pattern = r"你好|您好|hello" # 可以更精确
        response = await cli_tester.expect_output(
            conversation_id,
            pattern=expected_pattern, 
            message_type="assistant",
            timeout=TIMEOUT # 首次调用可能需要更长时间
        )
        assert response["type"] == "assistant"
        # 可以添加对内容的断言，例如：
        # assert "你好" in response["content"] or "您好" in response["content"]

    finally:
        # 确保会话关闭
        await cli_tester.close_session(conversation_id)


async def test_basic_chat_workflow_memory(cli_tester: CLITester):
    """
    测试 basic_chat_workflow 是否按预期调用记忆工具。
    """
    conversation_id = f"test_user_{uuid.uuid4().hex[:8]}"

    try:
        # 启动会话 (默认应使用 basic_chat_workflow)
        await cli_tester.start_session(conversation_id, agent_config=agent_config)

        # 发送包含潜在记忆点的信息
        user_input = "我最喜欢的颜色是蓝色。"
        await cli_tester.send_input(conversation_id, user_input)

        # 期望调用 query_relevant_infos 步骤中的 memory 工具
        # 新工作流使用 list_directory, read_file 等，而非 query_memory
        await cli_tester.expect_output(
            conversation_id,
            pattern=r"memory\.(list_directory|change_directory|read_file|open_item_to_tab|close_tab|view_tab)", # 更精确地匹配第一步允许的操作
            message_type="tool_call",
            timeout=TIMEOUT
        )

        # 期望调用 add_memory 步骤中的 memory 工具 (因为输入包含信息点)
        # 新工作流使用 create_item, write_file 等，而非 add_memory
        await cli_tester.expect_output(
            conversation_id,
            pattern=r"memory\.(create_item|write_file|list_directory|change_directory|read_file|delete_item|open_item_to_tab|close_tab|view_tab)", # 更精确地匹配条件步骤允许的操作
            message_type="tool_call",
            timeout=TIMEOUT
        )

        # 期望最终的助手回复
        final_response = await cli_tester.expect_output(
            conversation_id,
            pattern=r".*", # 匹配任何回复内容
            message_type="assistant",
            timeout=TIMEOUT
        )
        assert final_response["type"] == "assistant"

    finally:
        # 确保会话关闭
        await cli_tester.close_session(conversation_id)


# # 你可以在这里添加更多测试用例...
# # 例如：测试特定的工具调用、测试错误处理、测试不同的工作流等 

# --- 从 test_simple.py 迁移过来的测试 --- 

async def test_memory_add_and_query_migrated(cli_tester: CLITester):
    """
    测试记忆的添加和查询功能 (从 test_simple.py 迁移)。
    """
    conversation_id = f"test_mem_{uuid.uuid4().hex[:8]}"
    fact_to_remember = "我的爱好是画画"
    question = "我的爱好是什么？"
    expected_answer_part = "画画"

    try:
        await cli_tester.start_session(conversation_id, agent_config=agent_config)

        # 1. 让 Agent 记住信息
        await cli_tester.send_input(conversation_id, f"请记住: {fact_to_remember}")
        
        # 期望调用 memory 工具 (根据 python_chat_workflow 的 add_memory 步骤)
        # 新工作流使用 create_item, write_file 等
        await cli_tester.expect_output(
            conversation_id,
            pattern=r"memory\.(create_item|write_file|list_directory|change_directory|read_file|delete_item|open_item_to_tab|close_tab|view_tab)", # 匹配 add_memory 步骤允许的工具
            message_type="tool_call",
            timeout=TIMEOUT 
        )
        
        # 期望一个确认性的回复（内容不严格要求）
        await cli_tester.expect_output(
            conversation_id,
            pattern=r".*", # 匹配任何确认回复
            message_type="assistant",
            timeout=TIMEOUT
        )

        # 2. 提问以验证记忆
        await cli_tester.send_input(conversation_id, question)

        # 期望回复包含记忆内容
        response = await cli_tester.expect_output(
            conversation_id,
            pattern=expected_answer_part, # 直接匹配关键部分
            message_type="assistant",
            timeout=TIMEOUT # 查询可能比简单问候慢
        )
        assert expected_answer_part in response["content"], \
               f"助手回复 '{response['content']}' 未包含预期的记忆内容 '{expected_answer_part}'"

    finally:
        await cli_tester.close_session(conversation_id)

async def test_history_loading_migrated(cli_tester: CLITester):
    """
    测试历史记录加载功能 (从 test_simple.py 迁移)。
    验证跨会话的上下文记忆。
    """
    conversation_id = f"test_hist_{uuid.uuid4().hex[:8]}" 
    context_message = "我最喜欢的城市是巴黎。"
    question = "我最喜欢的城市是哪个？"
    expected_answer_part = "巴黎"

    # --- 第一轮：建立上下文 --- 
    try:
        await cli_tester.start_session(conversation_id, agent_config=agent_config)
        await cli_tester.send_input(conversation_id, context_message)
        # 等待第一轮的任意助手回复以确认交互完成
        await cli_tester.expect_output(conversation_id, pattern=".*", message_type="assistant", timeout=TIMEOUT)
    finally:
        # 关闭第一轮会话，此时历史应已保存
        await cli_tester.close_session(conversation_id)
        # 短暂等待，模拟实际场景中的延迟或确保文件IO完成（如果需要）
        await asyncio.sleep(0.5)

    # --- 第二轮：验证历史加载 --- 
    history_response_content = ""
    try:
        # 使用相同的 conversation_id 开启新会话，AgentManager 应加载历史
        await cli_tester.start_session(conversation_id, agent_config=agent_config) 
        await cli_tester.send_input(conversation_id, question)
        
        # 期望回复包含第一轮的上下文信息
        response = await cli_tester.expect_output(
            conversation_id,
            pattern=expected_answer_part,
            message_type="assistant",
            timeout=25
        )
        history_response_content = response["content"]
        assert expected_answer_part in history_response_content, \
               f"助手回复 '{history_response_content}' 未包含预期的历史内容 '{expected_answer_part}'"

    finally:
        await cli_tester.close_session(conversation_id)
        
async def test_user_isolation_migrated(cli_tester: CLITester):
    """
    测试不同用户之间的对话隔离性 (从 test_simple.py 迁移)。
    """
    user_a_id = f"user_A_{uuid.uuid4().hex[:8]}"
    user_b_id = f"user_B_{uuid.uuid4().hex[:8]}"
    user_a_name = "张三"
    user_b_name = "李四"

    try:
        # --- 用户 A 设置名字 --- 
        await cli_tester.start_session(user_a_id, agent_config=agent_config)
        await cli_tester.send_input(user_a_id, f"请记住我的名字是{user_a_name}")
        await cli_tester.expect_output(user_a_id, ".*", message_type="assistant", timeout=TIMEOUT)
        await cli_tester.close_session(user_a_id) # 关闭 A 的会话
        await asyncio.sleep(0.5) # 短暂等待

        # --- 用户 B 设置名字 --- 
        await cli_tester.start_session(user_b_id, agent_config=agent_config)
        await cli_tester.send_input(user_b_id, f"我的名字叫{user_b_name}")
        await cli_tester.expect_output(user_b_id, ".*", message_type="assistant", timeout=TIMEOUT)
        await cli_tester.close_session(user_b_id) # 关闭 B 的会话
        await asyncio.sleep(0.5) # 短暂等待

        # --- 验证用户 A 的记忆 --- 
        await cli_tester.start_session(user_a_id, agent_config=agent_config) # 重新打开 A 的会话
        await cli_tester.send_input(user_a_id, "我的名字是什么？")
        response_a = await cli_tester.expect_output(user_a_id, user_a_name, message_type="assistant", timeout=25)
        assert user_a_name in response_a["content"], f"用户 A 回复未包含 '{user_a_name}'"
        assert user_b_name not in response_a["content"], f"用户 A 回复错误地包含 '{user_b_name}'"
        await cli_tester.close_session(user_a_id)
        await asyncio.sleep(0.5)

        # --- 验证用户 B 的记忆 --- 
        await cli_tester.start_session(user_b_id, agent_config=agent_config) # 重新打开 B 的会话
        await cli_tester.send_input(user_b_id, "我叫什么名字？")
        response_b = await cli_tester.expect_output(user_b_id, user_b_name, message_type="assistant", timeout=25)
        assert user_b_name in response_b["content"], f"用户 B 回复未包含 '{user_b_name}'"
        assert user_a_name not in response_b["content"], f"用户 B 回复错误地包含 '{user_a_name}'"
        await cli_tester.close_session(user_b_id)

    except Exception as e: # 使用更通用的异常捕获以防万一
        # 在 finally 块之外处理，以确保可以 pytest.fail
        pytest.fail(f"用户隔离测试过程中发生异常: {e}")
    # finally 块只用于清理，即使上面的 try 中有 pytest.fail

# # 你可以在这里添加更多测试用例...
# # 例如：测试特定的工具调用、测试错误处理、测试不同的工作流等 