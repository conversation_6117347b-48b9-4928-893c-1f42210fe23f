---
description: MeowAgent 框架设计了灵活的模型集成层，使其能够与各种大型语言模型 (LLM) 进行高效交互，支持流式输出和工具调用。
globs: 
alwaysApply: false
---
# MeowAgent 模型集成

MeowAgent 框架设计了灵活的模型集成层，使其能够与各种大型语言模型 (LLM) 进行高效交互，支持流式输出和工具调用。

## 模型抽象架构

模型抽象由 [model.py](mdc:meowagent/model.py) 实现，主要包括：

1. `Model` 抽象基类 - 定义所有LLM实现的通用接口
2. `OpenAIModel` - 与OpenAI API兼容的模型实现

### `Model` 基类

```python
class Model(ABC):
    """模型抽象基类"""
    
    def __init__(self, provider: str, api_key: str):
        self.provider = provider
        self.api_key = api_key
    
    @abstractmethod
    async def generate(self, messages: List[Dict], tools: Optional[List] = None, **kwargs) -> Any:
        """生成文本或工具调用响应"""
        pass
```

### `OpenAIModel` 实现

`OpenAIModel` 实现了对接OpenAI API的具体逻辑：

- 使用符号名称（symbolic_name）引用全局配置
- 支持本地和远程模型配置
- 提供token计数功能
- 实现流式文本输出和工具调用处理

## 配置与实例化

模型配置在 [config.py](mdc:meowagent/config.py) 的 `MODEL_CONFIG` 字典中定义：

```python
MODEL_CONFIG = {
    "default": {
        "api_key": os.getenv("OPENAI_API_KEY"),
        "base_url": os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"),
        "model_name": "gpt-4-turbo",
        "max_tokens": 4000,
        "temperature": 0.7,
        "enable_thinking": True,
        "tokenizer_name": "open_ai"
    },
    "long": {
        "api_key": os.getenv("OPENAI_API_KEY"),
        "base_url": os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"),
        "model_name": "gpt-4-turbo",
        "max_tokens": 4000,
        "temperature": 0.7
    }
}
```

实例化模型：
```python
# 创建默认模型
model = OpenAIModel("default")

# 创建长上下文模型
long_context_model = OpenAIModel("long")
```

## 流式输出处理

MeowAgent支持LLM流式输出，实时显示模型生成内容：

```python
# 流式输出处理函数
async def stream_callback(chunk):
    chunk_type = chunk.get("type")
    
    if chunk_type == "content_delta":
        # 处理内容增量
        content = chunk.get("content", "")
        if content:
            print(content, end="", flush=True)
    
    elif chunk_type == "reasoning_delta":
        # 处理推理过程增量
        reasoning = chunk.get("content", "")
        if reasoning:
            print(f"[思考] {reasoning}", end="", flush=True)
    
    elif chunk_type == "tool_call_delta":
        # 处理工具调用增量
        tool_id = chunk.get("id")
        tool_name = chunk.get("name")
        print(f"[调用工具] {tool_name}")

# 调用模型并传入回调
result = await model.generate(
    messages=formatted_messages,
    tools=available_tools,
    stream_callback=stream_callback
)
```

## 工具调用处理

MeowAgent 中的工具调用遵循 OpenAI 函数调用格式，在 [agent.py](mdc:meowagent/agent.py) 中处理：

```python
async def _process_multiple_tool_calls(self, tool_calls):
    """处理多个工具调用并获取结果"""
    tasks = []
    for tool_call in tool_calls:
        tasks.append(self._execute_single_tool(tool_call))
    
    # 并发执行所有工具调用
    if tasks:
        return await asyncio.gather(*tasks)
    return []

async def _execute_single_tool(self, tool_call):
    """执行单个工具调用并返回结果"""
    tool_name = tool_call.function.name
    # 解析参数
    try:
        if tool_call.function.arguments:
            arguments = json.loads(tool_call.function.arguments)
        else:
            arguments = {}
    except json.JSONDecodeError:
        # 处理参数解析错误
        ...
    
    # 查找库并执行工具
    library_name = tool_name.split(".")[0]
    library = self.libraries.get(library_name)
    result = await library.execute_tool(tool_name, arguments)
    return result
```

## Token计数与上下文管理

[model.py](mdc:meowagent/model.py) 中的 `count_tokens` 方法帮助追踪对话上下文的token使用量：

```python
def count_tokens(self, messages: List[Dict]) -> int:
    """计算消息列表的token数量"""
    if self.tokenizer is None:
        return -1  # 无法计算
    
    text = self.tokenizer.apply_chat_template(
        messages, 
        tokenize=False, 
        add_generation_prompt=True
    )
    return len(self.tokenizer.encode(text))
```

[renderer.py](mdc:meowagent/renderer.py) 中的 `TrimRenderer` 使用此功能进行消息裁剪，确保不超出模型上下文限制。

