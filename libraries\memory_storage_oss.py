"""
该模块提供了 `OSSStorage` 类，它实现了 `MemoryStorageBase` 接口，
使用阿里云对象存储服务 (OSS) 作为后端，为 MeowAgent 提供持久化的、类似文件系统的记忆存储能力。

核心功能与设计：
1.  **分层存储抽象**：
    -   将OSS中的对象（Object）映射为"文件"，并通过对象键（Key）的命名约定（如以 '/' 结尾的空对象）
        来模拟"文件夹"结构。
    -   支持显式文件夹标记（创建空对象 `folder/`）和隐式文件夹（存在 `folder/file.txt` 即认为 `folder/` 存在）。
    -   所有操作都基于用户会话ID (`conversation_id`) 进行隔离，每个用户的数据存储在OSS Bucket内的
        独立"根目录"下（由 `OSS_MEMORY_PREFIX` 和 `conversation_id` 构成）。

2.  **CRUD 操作**：
    -   `create_node`：创建文件（上传内容）或文件夹（创建标记对象）。
    -   `get_node_by_path`：获取指定路径的文件或文件夹的元信息。
    -   `read_node_content` / `read_file_content`：读取文件内容。
    -   `update_node_content`：更新文件内容（如果文件不存在则创建）。
    -   `delete_node_recursive`：删除文件或递归删除文件夹及其所有内容。
    -   `get_children_of_node`：列出指定文件夹下的直接子节点（文件和文件夹）。

3.  **查询与搜索**：
    -   `path_exists`：检查指定路径（文件或文件夹）是否存在。
    -   `search_nodes_by_name`：按名称（文件名或文件夹名）搜索节点，支持递归和非递归搜索。
    -   `search_file_contents`：在文件内容中搜索指定文本，支持递归和非递归搜索。

4.  **阿里云 OSS SDK V2 集成**：
    -   使用 `alibabacloud-oss-v2` Python SDK与OSS服务进行交互。
    -   通过 `_run_sync_oss_call` 辅助方法，将同步的SDK调用包装在异步任务中执行，
        以适应 MeowAgent 的异步环境。

5.  **配置与初始化**：
    -   OSS连接参数（AccessKey ID/Secret, Endpoint, Bucket名称, Region, 全局前缀）
        从 `config.py` 模块中导入。
    -   在 `__init__` 和 `initialize` 方法中完成客户端的配置和实例化。

6.  **错误处理与日志**：
    -   捕获并处理来自OSS SDK的 `ServiceError` 和 `OperationError` 异常 (例如 `NoSuchKey`)。
    -   使用带有特定前缀 `[OSS存储 V2]` 的日志记录器（`log.py`）详细记录操作过程和潜在问题。

7.  **路径处理**：
    -   所有面向用户的路径都以 '/' 开头，代表相对于用户会话的根。
    -   内部将这些相对路径转换为OSS Bucket中的完整对象键。

已知限制：
-   `rename_node` (重命名节点) 和 `move_node` (移动节点) 方法尚未实现，因为OSS本身不直接支持这些操作，
    需要通过复制和删除的组合来实现，这部分逻辑当前缺失。
-   节点的 `created_at` 时间戳可能不准确或为 None，因为OSS V2 SDK的某些接口（如 `HeadObject` 或列表操作返回的摘要）
    可能不直接提供对象的创建时间，通常提供的是 `Last-Modified` 时间。

该类旨在为需要结构化、持久化存储的应用场景（如用户文件管理、长期记忆片段等）提供一个可靠的后端方案。
"""
import asyncio
import json
import os
import time
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple, Union

import alibabacloud_oss_v2 as oss

from config import OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_ENDPOINT, OSS_BUCKET_NAME, OSS_MEMORY_PREFIX, OSS_REGION
from libraries.memory_storage import MemoryStorageBase, NodeType
from log import logger

# 创建一个该模块专用的日志前缀
LOG_PREFIX = "[OSS存储 V2]"

class OSSStorage(MemoryStorageBase):
    """
    使用阿里云OSS作为记忆存储后端。
    迁移到 alibabacloud-oss-v2 SDK。
    """

    def __init__(self):
        self.endpoint = OSS_ENDPOINT
        self.bucket_name = OSS_BUCKET_NAME
        self.access_key_id = OSS_ACCESS_KEY_ID
        self.access_key_secret = OSS_ACCESS_KEY_SECRET
        self.region = OSS_REGION # V2 SDK 需要 Region
        # 确保前缀以 '/' 结尾，如果存在的话
        self.prefix = OSS_MEMORY_PREFIX.rstrip('/') + '/' if OSS_MEMORY_PREFIX else ''
        
        if not all([self.endpoint, self.bucket_name, self.access_key_id, self.access_key_secret, self.region]):
            err_msg = ("OSS存储配置不完整。请设置 OSS_ENDPOINT, OSS_BUCKET_NAME, OSS_ACCESS_KEY_ID, "
                       "OSS_ACCESS_KEY_SECRET, 和 OSS_REGION。")
            logger.error(f"{LOG_PREFIX} {err_msg}")
            raise ValueError(err_msg)

        # V2 SDK 客户端初始化 - 使用官方推荐的方式
        def get_credentials():
            return oss.credentials.Credentials(
                access_key_id=self.access_key_id, 
                access_key_secret=self.access_key_secret
            )
        
        credentials_provider = oss.credentials.CredentialsProviderFunc(func=get_credentials)
        cfg = oss.config.load_default()
        cfg.credentials_provider = credentials_provider
        cfg.region = self.region
        if self.endpoint:
            cfg.endpoint = self.endpoint # 明确设置 Endpoint

        self.client = oss.Client(cfg)
        logger.info(f"{LOG_PREFIX} 已初始化。存储桶: {self.bucket_name}, 前缀: {self.prefix}, 区域: {self.region}")

    def _get_user_base_key(self, conversation_id: str) -> str:
        """获取用户在OSS中的基础对象键前缀。"""
        return f"{self.prefix}{conversation_id}/"

    async def _run_sync_oss_call(self, func, *args, **kwargs):
        """辅助函数，在线程池中运行同步的OSS SDK调用。"""
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, lambda: func(*args, **kwargs))
            logger.debug(f"{LOG_PREFIX} _run_sync_oss_call 成功: func={func.__name__}, args={args}")
            return result
        except oss.exceptions.ServiceError as e:
            # 对于 OSS 的 ServiceError，根据错误类型决定日志级别
            if e.code == 'NoSuchKey':
                # NoSuchKey 是正常的响应，表示对象不存在，使用 debug 级别
                logger.debug(f"{LOG_PREFIX} OSS对象不存在: func={func.__name__}, Code={e.code}")
            else:
                # 其他 ServiceError 使用 error 级别
                logger.error(f"{LOG_PREFIX} OSS服务错误: func={func.__name__}, Code={e.code}, Message={e.message}, RequestId={e.request_id}")
            raise # 重新抛出异常，让调用者处理
        except oss.exceptions.OperationError as e:
            # 检查是否是由 NoSuchKey 引起的 OperationError
            if hasattr(e, 'args') and len(e.args) > 0 and isinstance(e.args[0], str) and 'NoSuchKey' in e.args[0]:
                logger.debug(f"{LOG_PREFIX} OSS对象不存在: func={func.__name__}, 底层错误: NoSuchKey")
            else:
                logger.error(f"{LOG_PREFIX} OSS操作错误: func={func.__name__}, 错误: {str(e)}", show_traceback=True)
            raise # 重新抛出异常，让调用者处理
        except Exception as e:
            # 其他未知异常使用 error 级别
            logger.error(f"{LOG_PREFIX} _run_sync_oss_call 失败: func={func.__name__}, 错误: {str(e)}", show_traceback=True)
            raise # 重新抛出异常，让调用者处理

    async def _read_oss_object(self, key: str) -> Optional[bytes]:
        """从OSS异步读取对象。"""
        request = oss.GetObjectRequest(bucket=self.bucket_name, key=key)
        try:
            response = await self._run_sync_oss_call(self.client.get_object, request)
            # response.body 是一个可读流 (io.BytesIO 或类似)，需要读取
            return await self._run_sync_oss_call(response.body.read)
        except oss.exceptions.ServiceError as e: # 捕获 ServiceError
            if e.code == 'NoSuchKey':
                logger.debug(f"{LOG_PREFIX} OSS对象未找到: {key} (Code: {e.code})")
            else:
                logger.error(f"{LOG_PREFIX} 读取OSS对象 {key} 时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}")
            return None
        except Exception as e_unhandled: # 其他非SDK预期的错误
            logger.error(f"{LOG_PREFIX} 读取OSS对象 {key} 时发生未知错误: {e_unhandled}")
            return None
            
    async def _write_oss_object(self, key: str, data: Union[str, bytes]) -> bool:
        """向OSS异步写入对象。"""
        body_data = data.encode('utf-8') if isinstance(data, str) else data
        request = oss.PutObjectRequest(bucket=self.bucket_name, key=key, body=body_data)
        try:
            await self._run_sync_oss_call(self.client.put_object, request)
            return True
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 写入OSS对象 {key} 时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}")
            return False
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 写入OSS对象 {key} 时发生未知错误: {e_unhandled}")
            return False
            
    async def _delete_oss_object(self, key: str) -> bool:
        """从OSS异步删除对象。"""
        request = oss.DeleteObjectRequest(bucket=self.bucket_name, key=key)
        try:
            await self._run_sync_oss_call(self.client.delete_object, request)
            return True
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 删除OSS对象 {key} 时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}")
            return False
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 删除OSS对象 {key} 时发生未知错误: {e_unhandled}")
            return False
            
    async def _list_oss_objects_keys(self, prefix_filter: str, delimiter: Optional[str] = None, max_keys_per_page: int = 1000) -> List[str]:
        """异步列出OSS对象键（仅键字符串列表）。"""
        keys = []
        try:
            # 使用官方推荐的分页器方式
            paginator = self.client.list_objects_paginator()
            request = oss.ListObjectsRequest(
                bucket=self.bucket_name, 
                prefix=prefix_filter,
                delimiter=delimiter,
                max_keys=max_keys_per_page
            )
            for page in await self._run_sync_oss_call(list, paginator.iter_page(request)):
                if page.contents:
                    for obj_summary in page.contents:
                        keys.append(obj_summary.key)
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 使用前缀 {prefix_filter} 列出OSS对象键时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}")
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 使用前缀 {prefix_filter} 列出OSS对象键时发生未知错误: {e_unhandled}")
        return keys

    async def initialize(self):
        """初始化OSS存储。"""
        logger.info(f"{LOG_PREFIX} 开始初始化，存储桶 {self.bucket_name}, 前缀 {self.prefix}, 区域: {self.region}。")
        # V2 SDK 初始化已在 __init__ 中完成
        logger.info(f"{LOG_PREFIX} 初始化完成。")

    async def get_node_by_id(self, conversation_id: str, node_id: int) -> Optional[NodeType]: # 将被彻底移除
        """通过ID获取节点。此方法在OSS原生实现中已被废弃。"""
        logger.warning(f"{LOG_PREFIX} get_node_by_id 在原生OSS实现中已废弃，请使用基于路径的方法。")
        return None

    async def get_node_by_path(self, conversation_id: str, path: str) -> Optional[NodeType]:
        """通过路径获取节点，支持文件夹的隐式存在检查。"""
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path
        
        try:
            # 首先尝试HEAD请求以检查对象是否存在及其元数据
            head_request = oss.HeadObjectRequest(bucket=self.bucket_name, key=full_oss_key)
            head_response = await self._run_sync_oss_call(self.client.head_object, head_request)
            
            # 判断节点类型：以 / 结尾的键被视为文件夹标记对象
            is_folder = full_oss_key.endswith('/')
            node_type = "folder" if is_folder else "file"
            
            # 构造文件或文件夹节点信息
            node_info = {
                "name": os.path.basename(normalized_path.rstrip('/')),
                "path": '/' + normalized_path,
                "node_type": node_type,
                "conversation_id": conversation_id,
                # 使用 last-modified 时间
                "updated_at": head_response.last_modified if hasattr(head_response, 'last_modified') else None,
                "created_at": None, # OSS V2 不提供创建时间
                "content": None # 不包含内容，需要单独读取
            }
            
            return node_info
            
        except oss.exceptions.OperationError as e:
            # 判断底层是否为 NoSuchKey 错误 
            if hasattr(e, 'args') and len(e.args) > 0 and isinstance(e.args[0], str) and 'NoSuchKey' in e.args[0]:
                # 对文件夹路径，当显式文件夹标记不存在时，尝试隐式文件夹检查
                if full_oss_key.endswith('/'):
                    # 列举该前缀下的对象，检查是否有子对象，即隐式文件夹
                    list_req = oss.ListObjectsRequest(
                        bucket=self.bucket_name, 
                        prefix=full_oss_key,
                        max_keys=1 # 只需要知道是否有至少一个对象
                    )
                    try:
                        list_resp = await self._run_sync_oss_call(self.client.list_objects, list_req)
                        if list_resp.contents or list_resp.common_prefixes:
                            # 有对象以这个前缀开头，因此这是一个隐式文件夹
                            return {
                                "name": os.path.basename(normalized_path.rstrip('/')),
                                "path": '/' + normalized_path,
                                "node_type": "folder",
                                "conversation_id": conversation_id,
                                "updated_at": None, # 隐式文件夹没有更新时间
                                "created_at": None,
                                "content": None
                            }
                    except oss.exceptions.OperationError as list_e:
                        # 如果 ListObjects 也报错，可能不是 NoSuchKey
                        if hasattr(list_e, 'args') and len(list_e.args) > 0 and isinstance(list_e.args[0], str) and 'NoSuchKey' not in list_e.args[0]:
                            logger.warning(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时ListObjects失败: {str(list_e)}", show_traceback=True)
                        else:
                            # NoSuchKey错误
                            logger.debug(f"{LOG_PREFIX} 隐式文件夹也不存在: {full_oss_key}")
                    except Exception as list_e:
                        # 其他未知异常
                        logger.error(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时发生未知错误: {list_e}", show_traceback=True)
                logger.debug(f"{LOG_PREFIX} 路径 '{full_oss_key}' 未找到。")
                return None
            else:
                logger.error(f"{LOG_PREFIX} 获取路径 '{full_oss_key}' 节点信息时出错: {str(e)}", show_traceback=True)
                return None
        except oss.exceptions.ServiceError as e:
            if e.code == 'NoSuchKey':
                # 对文件夹路径，当显式文件夹标记不存在时，尝试隐式文件夹检查
                if full_oss_key.endswith('/'):
                    # 列举该前缀下的对象，检查是否有子对象，即隐式文件夹
                    list_req = oss.ListObjectsRequest(
                        bucket=self.bucket_name, 
                        prefix=full_oss_key,
                        max_keys=1 # 只需要知道是否有至少一个对象
                    )
                    try:
                        list_resp = await self._run_sync_oss_call(self.client.list_objects, list_req)
                        if list_resp.contents or list_resp.common_prefixes:
                            # 有对象以这个前缀开头，因此这是一个隐式文件夹
                            return {
                                "name": os.path.basename(normalized_path.rstrip('/')),
                                "path": '/' + normalized_path,
                                "node_type": "folder",
                                "conversation_id": conversation_id,
                                "updated_at": None, # 隐式文件夹没有更新时间
                                "created_at": None,
                                "content": None
                            }
                    except oss.exceptions.ServiceError as list_e:
                        # 如果 ListObjects 也报错（例如权限问题，不太可能是 NoSuchKey）
                        if isinstance(list_e, oss.exceptions.ServiceError):
                            logger.warning(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时ListObjects失败: Code={list_e.code}, Message={list_e.message}", show_traceback=True)
                        else: # Exception caught by the inner try-except
                            logger.error(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时发生未知错误: {list_e}", show_traceback=True)
                logger.debug(f"{LOG_PREFIX} 路径 '{full_oss_key}' 未找到 (Code: {e.code})。")
                return None
            else:
                logger.error(f"{LOG_PREFIX} 获取路径 '{full_oss_key}' 节点信息时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
                return None
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 获取路径 '{full_oss_key}' 节点信息时发生未知错误: {e_unhandled}", show_traceback=True)
            return None

    async def get_children_of_node(
        self, conversation_id: str, parent_path: Optional[str]
    ) -> List[NodeType]:
        """获取指定父路径下的所有直接子节点（文件和文件夹）。"""
        child_nodes = []
        base_user_prefix = self._get_user_base_key(conversation_id)
        current_listing_prefix: str
        
        if parent_path is None or parent_path == "" or parent_path == "/":
            current_listing_prefix = base_user_prefix
        else:
            normalized_parent_path = parent_path.strip('/')
            full_parent_path = f"{base_user_prefix}{normalized_parent_path}"
            if not full_parent_path.endswith('/'):
                 full_parent_path += '/'
            current_listing_prefix = full_parent_path

        logger.debug(f"{LOG_PREFIX} 为用户 {conversation_id} 列出路径 '{current_listing_prefix}' 的子节点...")
        
        marker = None # V2 SDK ListObjectsRequest 使用 marker 而不是 next_marker
        try:
            while True:
                list_request = oss.ListObjectsRequest(
                    bucket=self.bucket_name,
                    prefix=current_listing_prefix,
                    delimiter='/',
                    marker=marker,
                    max_keys=100 # 限制每次列举数量
                )
                response = await self._run_sync_oss_call(self.client.list_objects, list_request)
                
                if response.common_prefixes:
                    for common_prefix_obj in response.common_prefixes: # common_prefixes 现在是对象列表
                        common_prefix_key = common_prefix_obj.prefix # 获取前缀字符串
                        name = common_prefix_key[len(current_listing_prefix):].strip('/')
                        if name:
                            relative_path_to_user_root = '/' + common_prefix_key[len(base_user_prefix):].lstrip('/')
                            child_nodes.append({
                                "name": name, "path": relative_path_to_user_root, "node_type": "folder",
                                "conversation_id": conversation_id, "created_at": None, "updated_at": None,
                                "content": None
                            })

                if response.contents: # contents 是对象列表
                    for obj_summary in response.contents:
                        name = obj_summary.key[len(current_listing_prefix):]
                        if name and '/' not in name and obj_summary.key != current_listing_prefix: # 确保不是父文件夹自身
                            relative_path_to_user_root = '/' + obj_summary.key[len(base_user_prefix):].lstrip('/')
                            last_modified_iso = obj_summary.last_modified.isoformat() if obj_summary.last_modified else None
                            child_nodes.append({
                                "name": name, "path": relative_path_to_user_root, "node_type": "file",
                                "conversation_id": conversation_id, "created_at": None,
                                "updated_at": last_modified_iso, "content": None
                            })
                
                if not response.is_truncated:
                    break
                marker = response.next_marker # 使用 next_marker 进行分页
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 为用户 {conversation_id} 列出路径 '{current_listing_prefix}' 的子节点时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}")
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 为用户 {conversation_id} 列出路径 '{current_listing_prefix}' 的子节点时发生未知错误: {e_unhandled}")

        child_nodes.sort(key=lambda x: (x.get('node_type', 'file') != 'folder', x.get('name', '')))
        return child_nodes

    async def create_node(self, conversation_id: str, path: str, node_type: str, content: str = None) -> Optional[NodeType]:
        """
        在指定的对话中创建一个新节点（文件或文件夹）。
        对于文件夹，将创建一个空的标记对象，其键以'/'结尾。
        对于文件，将创建包含指定内容的对象。
        """
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path
        
        # 调试日志
        logger.info(f"{LOG_PREFIX} 开始创建节点: conversation_id={conversation_id}, path={path}, node_type={node_type}, full_oss_key={full_oss_key}")
        
        # 如果是文件夹但未以 / 结尾，添加 /
        if node_type == "folder" and not full_oss_key.endswith('/'):
            full_oss_key += '/'
            normalized_path += '/'
            logger.debug(f"{LOG_PREFIX} 规范化文件夹路径: {full_oss_key}")
            
        file_name = os.path.basename(normalized_path.rstrip('/'))
        logger.debug(f"{LOG_PREFIX} 提取的文件名: {file_name}")
        
        # 对于文件夹，创建一个0字节的标记对象；对于文件，创建带内容的对象
        try:
            if node_type == "folder":
                # 创建空的文件夹标记对象
                logger.debug(f"{LOG_PREFIX} 正在创建文件夹标记对象: {full_oss_key}")
                request = oss.PutObjectRequest(
                    bucket=self.bucket_name,
                    key=full_oss_key,
                    body=b""  # 空内容
                )
            else: # 文件
                # 确保内容存在，转换为bytes
                content_bytes = content.encode('utf-8') if content is not None else b""
                logger.debug(f"{LOG_PREFIX} 正在创建文件对象: {full_oss_key}, 内容长度: {len(content_bytes)}字节")
                
                request = oss.PutObjectRequest(
                    bucket=self.bucket_name,
                    key=full_oss_key,
                    body=content_bytes
                )
                
            # 执行上传
            logger.debug(f"{LOG_PREFIX} 执行put_object请求: bucket={self.bucket_name}, key={full_oss_key}")
            response = await self._run_sync_oss_call(self.client.put_object, request)
            logger.debug(f"{LOG_PREFIX} put_object请求成功, 响应: {response}")
            
            # 获取系统当前时间作为上传时间
            now_iso = datetime.now().isoformat()
            
            # 构造并返回节点信息
            node_info = {
                "name": file_name,
                "path": '/' + normalized_path,
                "node_type": node_type,
                "conversation_id": conversation_id,
                "created_at": now_iso,
                "updated_at": now_iso,
                "content": content if node_type == "file" else None
            }
            logger.info(f"{LOG_PREFIX} 节点创建成功: {node_info['path']}, 类型: {node_info['node_type']}")
            return node_info
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 创建 {node_type} 节点 '{full_oss_key}' 时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
            return None
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 创建 {node_type} 节点 '{full_oss_key}' 时发生未知错误: {e_unhandled}", show_traceback=True)
            return None

    async def update_node_content(self, conversation_id: str, path: str, content: str) -> Optional[NodeType]:
        """更新指定路径下的节点内容。仅适用于文件节点。如果文件不存在，将创建它。"""
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path
        
        # 验证这是一个文件节点（路径不以斜杠结尾）
        if full_oss_key.endswith('/'):
            logger.error(f"{LOG_PREFIX} 无法更新文件夹 '{full_oss_key}' 的内容，只有文件才能更新内容。")
            return None
            
        file_name = os.path.basename(normalized_path)
        
        try:
            # 将内容编码为 bytes
            content_bytes = content.encode('utf-8') if content is not None else b""
                
            # 创建文件上传请求
            request = oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=full_oss_key,
                body=content_bytes
            )
            
            # 执行上传
            response = await self._run_sync_oss_call(self.client.put_object, request)
            
            # 获取当前时间作为更新时间
            now_iso = datetime.now().isoformat()
            
            # 构造并返回更新后的节点信息
            return {
                "name": file_name,
                "path": '/' + normalized_path,
                "node_type": "file", # 只能更新文件内容
                "conversation_id": conversation_id,
                "created_at": None, # 创建时间我们不知道，OSS V2 不会告诉我们
                "updated_at": now_iso,
                "content": content
            }
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 更新文件 '{full_oss_key}' 内容时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
            return None
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 更新文件 '{full_oss_key}' 内容时发生未知错误: {e_unhandled}", show_traceback=True)
            return None

    async def rename_node(
        self, conversation_id: str, old_path: str, new_name: str
    ) -> Optional[NodeType]:
        """重命名节点 (暂未实现，OSS原生重命名是复制+删除)"""
        logger.warning(f"{LOG_PREFIX} rename_node 方法尚未针对V2 SDK完全实现（需要复制+删除）。")
        raise NotImplementedError("重命名节点功能尚未针对V2 SDK完全实现。")

    async def move_node(
        self, conversation_id: str, source_path: str, target_parent_path: str
    ) -> Optional[NodeType]:
        """移动节点 (暂未实现，OSS原生移动是复制+删除)"""
        logger.warning(f"{LOG_PREFIX} move_node 方法尚未针对V2 SDK完全实现（需要复制+删除）。")
        raise NotImplementedError("移动节点功能尚未针对V2 SDK完全实现。")

    async def delete_node_recursive(self, conversation_id: str, path: str) -> bool:
        """递归删除节点（文件或文件夹）及其所有子节点。"""
        normalized_path = path.lstrip('/')
        full_oss_key_prefix = self._get_user_base_key(conversation_id) + normalized_path

        is_file = not full_oss_key_prefix.endswith('/')
        
        if is_file:
            logger.info(f"{LOG_PREFIX} 正在删除文件: {full_oss_key_prefix}")
            return await self._delete_oss_object(full_oss_key_prefix)
        else: # 是文件夹 (前缀以 / 结尾)
            logger.info(f"{LOG_PREFIX} 正在递归删除文件夹及其内容: {full_oss_key_prefix}")
            objects_to_delete_keys: List[str] = []
            marker = None
            try:
                while True:
                    list_req = oss.ListObjectsRequest(
                        bucket=self.bucket_name,
                        prefix=full_oss_key_prefix, # 列出此前缀下所有对象
                        marker=marker,
                        max_keys=1000 # 批量删除的上限也是1000
                    )
                    list_resp = await self._run_sync_oss_call(self.client.list_objects, list_req)
                    if list_resp.contents:
                        for obj_summary in list_resp.contents:
                            objects_to_delete_keys.append(obj_summary.key)
                    
                    if not list_resp.is_truncated:
                        break
                    marker = list_resp.next_marker
                
                if not objects_to_delete_keys:
                    logger.info(f"{LOG_PREFIX} 文件夹 '{full_oss_key_prefix}' 为空或其内容已被列出。")
                    return True 

                all_deleted_successfully = True
                # 逐个删除对象（原批量删除操作不适用于V2 SDK）
                for key in objects_to_delete_keys:
                    success = await self._delete_oss_object(key)
                    if not success:
                        all_deleted_successfully = False
                        logger.warning(f"{LOG_PREFIX} 删除对象 '{key}' 失败")
                
                if all_deleted_successfully:
                    logger.info(f"{LOG_PREFIX} 成功递归删除文件夹 '{full_oss_key_prefix}' 及其内容。")
                return all_deleted_successfully

            except oss.exceptions.ServiceError as e:
                logger.error(f"{LOG_PREFIX} 递归删除文件夹 '{full_oss_key_prefix}' 时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
                return False
            except Exception as e_unhandled:
                logger.error(f"{LOG_PREFIX} 递归删除文件夹 '{full_oss_key_prefix}' 时发生未知错误: {e_unhandled}", show_traceback=True)
                return False

    async def path_exists(self, conversation_id: str, path: str) -> bool:
        """检查给定的路径 (文件或文件夹标记对象) 在OSS中是否存在。"""
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path
        
        head_request = oss.HeadObjectRequest(bucket=self.bucket_name, key=full_oss_key)
        try:
            await self._run_sync_oss_call(self.client.head_object, head_request)
            return True # 如果 HeadObject 成功，则对象存在
        except oss.exceptions.OperationError as e:
            # 判断底层是否为 NoSuchKey 错误 
            if hasattr(e, 'args') and len(e.args) > 0 and isinstance(e.args[0], str) and 'NoSuchKey' in e.args[0]:
                # 如果是文件夹路径 (以 / 结尾) 且 HeadObject 失败，它仍可能是隐式文件夹
                if full_oss_key.endswith('/'):
                    list_req = oss.ListObjectsRequest(
                        bucket=self.bucket_name, prefix=full_oss_key, max_keys=1
                    )
                    try:
                        list_resp = await self._run_sync_oss_call(self.client.list_objects, list_req)
                        return bool(list_resp.contents or list_resp.common_prefixes)
                    except oss.exceptions.OperationError as list_e: 
                        logger.debug(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时ListObjects失败: {str(list_e)}")
                        return False # 列举也失败
                    except Exception as list_e_unhandled: 
                        logger.debug(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时发生未知错误: {list_e_unhandled}")
                        return False # 其他未知错误
                logger.debug(f"{LOG_PREFIX} 路径 '{full_oss_key}' 未找到。")
                return False # 文件不存在，且不是查询文件夹路径
            else: # 其他 OperationError (非 NoSuchKey)
                logger.error(f"{LOG_PREFIX} 检查路径 '{full_oss_key}' 是否存在时出错: {str(e)}", show_traceback=True)
                return False
        except oss.exceptions.ServiceError as e:
            if e.code == 'NoSuchKey':
                # 如果是文件夹路径 (以 / 结尾) 且 HeadObject 失败，它仍可能是隐式文件夹
                if full_oss_key.endswith('/'):
                    list_req = oss.ListObjectsRequest(
                        bucket=self.bucket_name, prefix=full_oss_key, max_keys=1
                    )
                    try:
                        list_resp = await self._run_sync_oss_call(self.client.list_objects, list_req)
                        return bool(list_resp.contents or list_resp.common_prefixes)
                    except oss.exceptions.ServiceError as list_e: 
                        logger.debug(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时ListObjects失败: Code={list_e.code}, Message={list_e.message}")
                        return False # 列举也失败
                    except Exception as list_e_unhandled: 
                        logger.debug(f"{LOG_PREFIX} 检查隐式文件夹 {full_oss_key} 时发生未知错误: {list_e_unhandled}")
                        return False # 其他未知错误
                logger.debug(f"{LOG_PREFIX} 路径 '{full_oss_key}' 未找到 (Code: {e.code})。")
                return False # 文件不存在，且不是查询文件夹路径
            else: # 其他 ServiceError (非 NoSuchKey)
                logger.error(f"{LOG_PREFIX} 检查路径 '{full_oss_key}' 是否存在时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
                return False
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 检查路径 '{full_oss_key}' 是否存在时发生未知错误: {e_unhandled}", show_traceback=True)
            return False

    async def get_path_string_for_node(self, conversation_id: str, path: Optional[str]) -> str:
        """规范化路径字符串。"""
        if path is None or not path.strip(): return "/"
        p = path.strip()
        if not p.startswith('/'): p = '/' + p
        while p.startswith('//') and len(p) > 1: p = p[1:]
        logger.debug(f"{LOG_PREFIX} get_path_string_for_node 返回路径: {p}")
        return p

    async def search_nodes_by_name(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True
    ) -> List[NodeType]:
        """按名称在OSS中搜索节点（文件和文件夹）。"""
        results: List[NodeType] = []
        base_user_prefix = self._get_user_base_key(conversation_id)
        
        search_prefix: str
        if parent_path is None or parent_path == "/" or parent_path == "":
            search_prefix = base_user_prefix
        else:
            normalized_parent = parent_path.lstrip('/')
            search_prefix = base_user_prefix + normalized_parent
            if not search_prefix.endswith('/'): search_prefix += '/'

        logger.debug(f"{LOG_PREFIX} 按名称搜索: query='{query}', prefix='{search_prefix}', recursive={recursive}")
        marker = None
        try:
            while True:
                list_req = oss.ListObjectsRequest(
                    bucket=self.bucket_name,
                    prefix=search_prefix,
                    delimiter='/' if not recursive else None,
                    marker=marker,
                    max_keys=100
                )
                response = await self._run_sync_oss_call(self.client.list_objects, list_req)

                if not recursive and response.common_prefixes:
                    for common_prefix_obj in response.common_prefixes:
                        name = common_prefix_obj.prefix[len(search_prefix):].strip('/')
                        if query.lower() in name.lower():
                            relative_path_to_user_root = '/' + common_prefix_obj.prefix[len(base_user_prefix):].lstrip('/')
                            results.append({
                                "name": name, "path": relative_path_to_user_root, "node_type": "folder",
                                "conversation_id": conversation_id, "created_at": None, "updated_at": None, "content": None
                            })
                
                if response.contents:
                    for obj_summary in response.contents:
                        relative_key_part = obj_summary.key[len(search_prefix):]
                        is_explicit_folder_object = obj_summary.key.endswith('/') # and obj_summary.size == 0 (size not always available in ListObjects V2 content summary for folders)
                        
                        name_to_check = relative_key_part.strip('/').split('/')[-1] if recursive else relative_key_part
                        
                        if obj_summary.key == search_prefix and is_explicit_folder_object: # 跳过父文件夹自身
                            continue

                        if query.lower() in name_to_check.lower():
                            node_type = "folder" if is_explicit_folder_object else "file"
                            relative_path_to_user_root = '/' + obj_summary.key[len(base_user_prefix):].lstrip('/')
                            last_mod_iso = obj_summary.last_modified.isoformat() if obj_summary.last_modified else None
                            results.append({
                                "name": name_to_check, "path": relative_path_to_user_root, "node_type": node_type,
                                "conversation_id": conversation_id, "created_at": None,
                                "updated_at": last_mod_iso, "content": None
                            })
                
                if not response.is_truncated: break
                marker = response.next_marker
            
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 按名称搜索时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 按名称搜索时发生未知错误: {e_unhandled}", show_traceback=True)

        unique_results = {item['path']: item for item in results}.values()
        return sorted(list(unique_results), key=lambda x: (x.get('node_type', 'file') != 'folder', x.get('name', '')))

    async def search_file_contents(
        self, conversation_id: str, query: str, parent_path: Optional[str] = None, recursive: bool = True
    ) -> List[NodeType]:
        """在OSS中的文件内容中搜索。"""
        results: List[NodeType] = []
        base_user_prefix = self._get_user_base_key(conversation_id)
        
        search_prefix: str
        if parent_path is None or parent_path == "/" or parent_path == "":
            search_prefix = base_user_prefix
        else:
            normalized_parent = parent_path.lstrip('/')
            search_prefix = base_user_prefix + normalized_parent
            if not search_prefix.endswith('/'): search_prefix += '/'

        logger.debug(f"{LOG_PREFIX} 搜索文件内容: query='{query}', prefix='{search_prefix}', recursive={recursive}")
        marker = None
        try:
            while True:
                list_req = oss.ListObjectsRequest(
                    bucket=self.bucket_name,
                    prefix=search_prefix,
                    delimiter='/' if not recursive else None,
                    marker=marker,
                    max_keys=100
                )
                response = await self._run_sync_oss_call(self.client.list_objects, list_req)

                if response.contents:
                    for obj_summary in response.contents:
                        if obj_summary.key.endswith('/'): continue # 跳过文件夹标记对象

                        # 对于非递归，确保是当前目录下的文件
                        if not recursive and '/' in obj_summary.key[len(search_prefix):]:
                            continue

                        file_content_bytes = await self._read_oss_object(obj_summary.key)
                        if file_content_bytes:
                            try:
                                file_content_str = file_content_bytes.decode('utf-8')
                                if query.lower() in file_content_str.lower():
                                    name = obj_summary.key.split('/')[-1]
                                    relative_path_to_user_root = '/' + obj_summary.key[len(base_user_prefix):].lstrip('/')
                                    last_mod_iso = obj_summary.last_modified.isoformat() if obj_summary.last_modified else None
                                    results.append({
                                        "name": name, "path": relative_path_to_user_root, "node_type": "file",
                                        "conversation_id": conversation_id, "created_at": None,
                                        "updated_at": last_mod_iso,
                                        "content": file_content_str # 或摘要
                                    })
                            except UnicodeDecodeError:
                                logger.warning(f"{LOG_PREFIX} 文件 '{obj_summary.key}' 解码失败，跳过内容搜索。")
                
                if not response.is_truncated: break
                marker = response.next_marker
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 搜索文件内容时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
        except Exception as e_unhandled:
             logger.error(f"{LOG_PREFIX} 搜索文件内容时发生未知错误: {e_unhandled}", show_traceback=True)
        
        return sorted(results, key=lambda x: x.get('updated_at', ''), reverse=True) 

    async def read_file_content(self, conversation_id: str, path: str) -> Optional[str]:
        """读取指定路径文件的内容。"""
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path

        if full_oss_key.endswith('/'):
            logger.warning(f"{LOG_PREFIX} 尝试读取文件夹 '{full_oss_key}' 的内容。")
            return None

        content_bytes = await self._read_oss_object(full_oss_key)
        if content_bytes is not None:
            try:
                return content_bytes.decode('utf-8')
            except UnicodeDecodeError as e:
                logger.error(f"{LOG_PREFIX} 解码文件 '{full_oss_key}' 内容时出错: {e}")
                return None
        return None 

    async def read_node_content(self, conversation_id: str, path: str) -> Optional[str]:
        """读取文件节点的内容。"""
        normalized_path = path.lstrip('/')
        full_oss_key = self._get_user_base_key(conversation_id) + normalized_path
        
        if full_oss_key.endswith('/'):
            logger.warning(f"{LOG_PREFIX} 尝试读取文件夹 '{full_oss_key}' 的内容。")
            return None
            
        try:
            request = oss.GetObjectRequest(bucket=self.bucket_name, key=full_oss_key)
            response = await self._run_sync_oss_call(self.client.get_object, request)
            
            # 读取响应中的数据
            if hasattr(response, 'body') and response.body is not None:
                # 先读取流对象的内容为字节
                body_bytes = await self._run_sync_oss_call(response.body.read)
                if body_bytes:
                    try:
                        # 尝试以 UTF-8 解码
                        content = body_bytes.decode('utf-8')
                        return content
                    except UnicodeDecodeError:
                        # 如果无法解码为 UTF-8，记录错误并返回二进制内容的字符串表示
                        logger.warning(f"{LOG_PREFIX} 无法将对象 '{full_oss_key}' 内容解码为 UTF-8，可能是二进制文件")
                        return f"[二进制内容，长度: {len(body_bytes)} 字节]"
            return ""
        except oss.exceptions.OperationError as e:
            # 判断底层是否为 NoSuchKey 错误 
            if hasattr(e, 'args') and len(e.args) > 0 and isinstance(e.args[0], str) and 'NoSuchKey' in e.args[0]:
                logger.debug(f"{LOG_PREFIX} OSS对象未找到: {full_oss_key}")
            else:
                # 对于其他 OperationError，记录详细信息
                logger.error(f"{LOG_PREFIX} 读取OSS对象 {full_oss_key} 时出错: {str(e)}", show_traceback=True)
            return None
        except oss.exceptions.ServiceError as e:
            if e.code == 'NoSuchKey':
                logger.debug(f"{LOG_PREFIX} OSS对象未找到: {full_oss_key} (Code: {e.code})")
            else:
                # 对于其他 ServiceError，记录详细信息
                logger.error(f"{LOG_PREFIX} 读取OSS对象 {full_oss_key} 时 ServiceError: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
            return None
        except Exception as e_unhandled: # 其他非SDK预期的错误
            # 记录未知错误的类型和堆栈信息
            logger.error(f"{LOG_PREFIX} 读取OSS对象 {full_oss_key} 时发生未知错误 (Type: {type(e_unhandled).__name__}): {e_unhandled}", show_traceback=True)
            return None

    async def _get_objects_for_prefix(self, prefix: str, delimiter: str = None, max_keys: int = 1000) -> List[Dict[str, Any]]:
        """获取指定前缀下的对象列表。"""
        all_objects = []
        marker = None
        
        try:
            while True:
                list_request = oss.ListObjectsRequest(
                    bucket=self.bucket_name,
                    prefix=prefix,
                    delimiter=delimiter,
                    marker=marker,
                    max_keys=max_keys
                )
                
                list_response = await self._run_sync_oss_call(self.client.list_objects, list_request)
                
                if list_response.contents:
                    all_objects.extend(list_response.contents)
                
                if not list_response.is_truncated:
                    break
                
                marker = list_response.next_marker
                
            return all_objects
        except oss.exceptions.ServiceError as e:
            logger.error(f"{LOG_PREFIX} 列举前缀 '{prefix}' 的对象时出错: Code={e.code}, Message={e.message}, RequestId={e.request_id}", show_traceback=True)
            return []
        except Exception as e_unhandled:
            logger.error(f"{LOG_PREFIX} 列举前缀 '{prefix}' 的对象时发生未知错误: {e_unhandled}", show_traceback=True)
            return [] 