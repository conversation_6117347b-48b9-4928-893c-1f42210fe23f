"""
该模块为 MeowAgent 智能体提供强大的长期记忆管理能力，其核心是 `MemoryLibrary` 类。
它通过模拟一个分层文件系统（支持文件和文件夹）来组织和持久化信息，帮助 LLM 克服上下文长度的限制。

主要功能与逻辑：
1.  **`MemoryLibrary` (核心类)**:
    *   继承自 `Library` 基类，将其功能注册为可供 LLM 调用的工具。
    *   管理用户特定的记忆空间，通过 `conversation_id` 区分不同用户的存储。
    *   维护当前工作目录 (`current_path_str`) 和打开的"标签页" (`open_tabs`) 列表。
    *   提供了一系列类似文件系统的操作工具，如创建、读取、写入、删除、列出目录、更改目录等。
    *   实现了"标签页"机制 (`OpenTab` dataclass)，允许 LLM "打开"文件或文件夹到标签页中，以便快速访问其内容或摘要。打开的标签页数量受配置限制。
    *   动态更新提供给 LLM 的提示信息 (`_update_library_prompt`)，实时反映当前路径和标签页状态，引导 LLM 高效使用记忆库。
    *   通过 `_resolve_path_to_node` 方法处理和规范化文件/文件夹路径，支持绝对路径和相对路径。
    *   在初始化时 (`initialize` 方法) 异步获取存储后端实例，并设置初始状态。

2.  **存储后端抽象与工厂模式**:
    *   依赖于 `MemoryStorageBase` 抽象基类（在 `libraries.memory_storage` 中定义），实现了存储逻辑的解耦。
    *   通过 `get_memory_storage` 工厂函数，根据配置 (`config_module.MEMORY_STORAGE_TYPE`) 动态创建并初始化具体的存储后端实例（如 `FSStorage` 用于文件系统存储，`OSSStorage` 用于阿里云 OSS 存储）。该函数使用锁确保单例存储实例的创建。

3.  **数据结构**:
    *   `OpenTab`: 一个 dataclass，用于表示一个打开的标签页，包含路径、名称、类型、内容（若是文件）或子项摘要（若是文件夹）。
    *   `NodeType`: (来自 `libraries.memory_storage`) 一个类型别名，通常是字典，表示文件或文件夹节点的数据结构。

4.  **配置驱动**:
    *   从 `config_module` 读取配置项，如最大标签页数量 (`MAX_OPEN_TABS`)、提示中列表项的最大数量 (`MAX_LIST_ITEMS_IN_PROMPT_SUMMARY`)、存储类型 (`MEMORY_STORAGE_TYPE`) 以及模型配置等。

5.  **工具注册**:
    *   使用 `@register_tool` 装饰器将其公共方法（如 `create_item`, `read_file` 等）暴露为 LLM 可调用的工具，并自动生成工具的 JSON Schema。

6.  **路径处理与规范化**:
    *   路径字符串是核心标识。模块内部处理相对路径和绝对路径的解析，确保路径的规范性（例如，文件夹路径以 `/` 结尾）。

该模块通过提供直观的文件系统式交互和高效的上下文管理（通过标签页和动态提示），极大地增强了智能体处理和回忆长期信息的能力。
"""

import asyncio
import traceback
import re
from typing import Dict, Optional, Union, List, Any, Tuple
from dataclasses import dataclass

from conversation import Conversation
from libraries.library import Library, register_tool
from libraries.memory_storage_fs import FSStorage
from libraries.memory_storage_oss import OSSStorage
from model import OpenAIModel #保留用于未来的搜索功能
import config as config_module
from log import logger

# 从新的存储层导入
from libraries.memory_storage import MemoryStorageBase, NodeType

# 默认配置 (这些也可以考虑移到 config_module)
DEFAULT_MAX_OPEN_TABS = 10
DEFAULT_MAX_LIST_ITEMS_IN_PROMPT_SUMMARY = 5

@dataclass
class OpenTab:
    """代表一个打开的标签页"""
    path: str # 替换 node_id，存储节点的完整规范路径
    name: str
    # path_str: str # path 即为规范路径字符串，此字段可移除或与 path 合并
    node_type: str # 'file' or 'folder'
    content = None # 文件内容
    children_summary = None # 文件夹子项摘要

# --- 存储实例工厂 --- 
_storage_instance = None
_storage_lock = asyncio.Lock()


async def get_memory_storage() -> MemoryStorageBase:
    """
    工厂函数，用于获取配置的记忆存储实例。
    在首次调用时进行初始化。
    """
    global _storage_instance
    if _storage_instance is None:
        async with _storage_lock:
            if _storage_instance is None: # 获取锁后再次检查
                storage_type = config_module.MEMORY_STORAGE_TYPE.lower()
                logger.info(f"正在初始化类型为 '{storage_type}' 的记忆存储...")
                if storage_type == 'fs':
                    _storage_instance = FSStorage()
                elif storage_type == 'oss':
                    _storage_instance = OSSStorage()
                else:
                    raise ValueError(f"未知的 MEMORY_STORAGE_TYPE: {storage_type}。")

                try:
                    await _storage_instance.initialize()
                    logger.info(f"{_storage_instance.__class__.__name__} 存储实例初始化成功。")
                except Exception as e:
                    logger.critical(f"初始化存储实例 {_storage_instance.__class__.__name__} 失败: {e}", show_traceback=True)
                    # 根据严重程度，可以抛出异常或再次尝试默认回退
                    raise RuntimeError(f"记忆存储初始化失败: {e}") from e

    return _storage_instance

class MemoryLibrary(Library):
    """
    分层记忆管理工具库，模拟文件系统操作，提供节点的创建、导航、读写和"标签页"功能。
    使用可配置的存储后端。
    """
    base_library_prompt = """**层级记忆系统 (文件系统模式)**

你正在使用一个模拟文件系统的记忆库。你可以创建文件夹和文件来组织信息，并通过路径导航。
为了高效管理上下文，你可以"打开"文件或文件夹到"标签页"中直接访问其内容。

**核心概念:**
- **路径 (Path):** 使用 `/` 分隔，例如 `/notes/project_alpha/tasks.txt`。绝对路径以 `/` 开头。相对路径基于当前目录。
- **当前目录 (Current Directory):** 你当前"所在"的文件夹。新项目默认在根目录 `/`。
- **标签页 (Tabs):** 你可以"打开"文件或文件夹。打开的文件会显示其完整内容，打开的文件夹会显示其子项列表。标签页数量有限。

**常用命令:**
- `memory.list_directory(path: Optional[str] = None)`: 列出指定路径（默认为当前目录）的**所有**内容。
- `memory.change_directory(path: str)`: 切换当前目录到指定路径。
- `memory.create_item(name: str, item_type: str, content: Optional[str] = None, parent_path: Optional[str] = None)`: 在指定父路径（默认为当前目录）下创建文件或文件夹。`item_type` 为 'file' 或 'folder'。
- `memory.read_file(path: str)`: 读取并返回指定文件的内容。推荐先用 `open_item_to_tab`。
- `memory.write_file(path: str, content: str, overwrite: bool = False)`: 写入或覆盖文件内容。
- `memory.delete_item(path: str)`: 删除文件或文件夹（文件夹会递归删除其内容）。
- `memory.open_item_to_tab(path: str)`: 将文件或文件夹加载到一个新的标签页。
- `memory.close_tab(tab_index: int)`: 关闭指定索引的标签页。
- `memory.view_tab(tab_index: int)`: 查看指定索引标签页的详细内容。
- `memory.get_current_status()`: (辅助工具) 显示当前路径和已打开的标签页的文本描述（主要用于调试，LLM应关注下方动态提示部分）。

**使用提示:**
- **频繁保存:** 对于重要的信息片段，及时创建文件保存。
- **组织结构:** 合理使用文件夹组织信息，方便查找。
- **管理标签页:** 关闭不再需要的标签页，以保持上下文清爽并为新内容腾出空间。
- **关注动态提示:** 下方会自动更新你当前的路径和打开的标签页信息，请密切关注。
"""

    def __init__(self, config = None):
        if config is None or 'conversation' not in config:
            raise ValueError("MemoryLibrary 初始化需要包含 'conversation' 的配置字典。")

        self.conversation: Conversation = config['conversation']
        self.conversation_id = self.conversation.conversation_id
        self.storage = None # 将在异步初始化时设置

        self.current_path_str: str = "/" # 从 current_path_id 更改为 current_path_str，默认为根路径
        self.open_tabs = []
        
        self.max_open_tabs = getattr(config_module, 'MAX_OPEN_TABS', DEFAULT_MAX_OPEN_TABS)
        self.max_list_items_in_prompt_summary = getattr(config_module, 'MAX_LIST_ITEMS_IN_PROMPT_SUMMARY', DEFAULT_MAX_LIST_ITEMS_IN_PROMPT_SUMMARY)

        symbolic_model_name = "long"
        if symbolic_model_name not in config_module.MODEL_CONFIG:
            logger.warning(f"MemoryLibrary 在 config.MODEL_CONFIG 中未找到符号名称 '{symbolic_model_name}' 的配置。基于LLM的搜索功能可能受限。")
            self.model = None
        else:
            self.model = OpenAIModel(symbolic_model_name)

        super().__init__(name="memory", description="分层记忆管理工具库 (文件系统模式)", prompt=self.base_library_prompt, config=config)
        
        # 异步初始化任务将由外部调用 initialize() 方法触发
        # asyncio.create_task(self._async_library_setup())

    async def initialize(self):
        """异步初始化库，获取存储实例并设置初始状态。应在Agent启动时由外部调用。"""
        try:
            self.storage = await get_memory_storage()
            logger.info(f"用户 {self.conversation_id} 的 MemoryLibrary 已成功初始化，存储类型: {self.storage.__class__.__name__}")
            await self._async_init_current_path() # 初始化当前路径
            await self._update_library_prompt()   # 首次构建并设置完整提示
        except Exception as e:
            logger.critical(f"用户 {self.conversation_id} 的 MemoryLibrary 初始化失败: {e}", show_traceback=True)
            # 如果初始化失败，storage 可能为 None，工具方法需要能安全处理这种情况
            self.storage = None # 确保 self.storage 为 None，以便工具方法可以检查
            # 可以考虑抛出异常，让调用者知道初始化失败
            raise RuntimeError(f"用户 {self.conversation_id} 的 MemoryLibrary 初始化失败，原因: {str(e)}") from e

    async def _async_init_current_path(self):
        """(内部) 异步初始化当前工作路径，默认为根。"""
        # 当前 current_path_str = "/" 就代表根。
        if self.conversation_id: # 确保 conversation_id 已设置
            logger.info(f"用户 {self.conversation_id} 的 MemoryLibrary: 当前路径已初始化为根目录 ('{self.current_path_str}')。")
        else:
            logger.warning("MemoryLibrary _async_init_current_path 调用时 conversation_id 未设置。")


    async def _build_dynamic_prompt_content(self) -> str:
        """(内部) 生成并返回当前的动态提示部分，显示当前路径和打开的标签页"""
        if not self.storage: # 如果存储未初始化成功
            return "\n**[记忆库错误：存储未初始化]**"

        status_parts = []
        # current_path_str 已经是规范的，或者通过 _get_path_string 获取
        current_display_path = await self._get_path_string(self.current_path_str) 
        status_parts.append(f"**当前状态:**\n  **当前路径:** `{current_display_path}`")

        if self.open_tabs:
            status_parts.append("  **打开的标签页:**")
            for i, tab in enumerate(self.open_tabs):
                tab_type_emoji = "📄" if tab.node_type == 'file' else "📁"
                # tab.path 已经是规范的路径字符串
                status_parts.append(f"    `{i}`: {tab_type_emoji} `{tab.path}` ({tab.node_type})")
                if tab.node_type == 'file':
                    preview_len = 150
                    if tab.content is not None:
                        summary = (tab.content[:preview_len] + '...') if len(tab.content) > preview_len else tab.content
                        summary_clean = summary.replace('\n', ' ')
                        status_parts.append(f"       内容预览 (前 {preview_len} 字符，总共 {len(tab.content)} 字符): {summary_clean}")
                    else:
                        status_parts.append(f"       内容: (空或未加载)")
                elif tab.node_type == 'folder' and tab.children_summary is not None:
                    summary_items = tab.children_summary[:self.max_list_items_in_prompt_summary]
                    summary_str = ", ".join([item['name'] for item in summary_items])
                    if len(tab.children_summary) > self.max_list_items_in_prompt_summary:
                        summary_str += f", ... (总计 {len(tab.children_summary)} 个项目)"
                    elif not tab.children_summary:
                        summary_str = "(空文件夹)"
                    status_parts.append(f"       包含: [{summary_str}]")
        else:
            status_parts.append("  **打开的标签页:** (无)")
        
        return "\n" + "\n".join(status_parts)

    async def _update_library_prompt(self):
        """(内部) 构建完整的库提示词（静态+动态）并调用父类的 update_prompt。"""
        if not self.storage: # 如果存储未初始化，不要尝试更新提示词，避免访问 self.storage 的方法
             logger.warning(f"用户 {self.conversation_id} 的 MemoryLibrary: 存储未初始化，跳过提示词更新。")
             return
        dynamic_content = await self._build_dynamic_prompt_content()
        full_prompt = self.base_library_prompt + dynamic_content
        super().update_prompt(full_prompt)
        logger.debug(f"用户 {self.conversation_id} 的 MemoryLibrary 提示词已更新。动态部分: {dynamic_content[:200]}...")

    async def _get_path_string(self, path: str = None):
        """获取并规范化给定路径字符串。如果path为None或空，则代表根，返回'/'。"""
        if path is None or path.strip() == "":
            return "/"
        # 存储层的 get_path_string_for_node 现在也接受 Optional[str]
        # 但在 MemoryLibrary 内部，我们应该已经有相对规范的路径了
        # 此方法主要用于确保返回的是一个一致的显示格式，例如总是以 / 开头
        # 或者调用存储层进行最终的规范化（如果存储层有特定逻辑）
        if self.storage:
            return await self.storage.get_path_string_for_node(self.conversation_id, path)
        else: # 存储未初始化时的回退
            p = path.strip()
            if not p.startswith('/'): p = '/' + p
            if p == '//': p = '/'
            return p

    async def _resolve_path_to_node(self, path: str, base_path_str: str = None):
        """
        解析路径字符串，返回目标节点数据字典和其规范化路径字符串。
        base_path_str: 当前的工作目录路径。
        """
        if not self.storage:
            # 规范化请求的路径以用于错误消息或返回
            requested_path_norm = await self._get_path_string(path) 
            return None, requested_path_norm # 存储未初始化

        path_input = path.strip()
        if not path_input:
            # 尝试解析空路径，通常意味着当前目录
            # current_node = await self.storage.get_node_by_path(self.conversation_id, self.current_path_str)
            # return current_node, self.current_path_str
            # 或者根据上下文返回错误或特定行为
            logger.debug("resolve_path_to_node: 收到空路径输入。")
            # 返回当前路径的信息
            if self.current_path_str == "/":
                return None, "/" # 根目录没有单一节点对象
            current_node_data = await self.storage.get_node_by_path(self.conversation_id, self.current_path_str)
            return current_node_data, self.current_path_str

        # 检查输入路径是否以斜杠结尾（表示文件夹）
        is_folder_path = path_input.endswith('/')
        
        effective_base_path = (base_path_str or self.current_path_str).strip()
        if not effective_base_path.startswith('/'):
            effective_base_path = '/' + effective_base_path
        # 不再移除基路径末尾的斜杠，我们需要保留它来正确处理文件夹路径

        resolved_segments = []
        if path_input.startswith('/'): # 绝对路径
            # effective_base_path 被忽略，从根开始
            pass
        else: # 相对路径
            if effective_base_path != "/":
                resolved_segments.extend([seg for seg in effective_base_path.split('/') if seg])
        
        input_segments = [seg for seg in path_input.split('/') if seg and seg != '.']

        for segment in input_segments:
            if segment == '..':
                if resolved_segments:
                    resolved_segments.pop()
                # else: 试图在根目录之上 ".."，或在空的相对路径之上，保持在根
            else:
                resolved_segments.append(segment)
        
        # 构建最终的规范化路径 (相对于用户根)
        if not resolved_segments:
            final_norm_path = "/" # 到达根目录
        else:
            final_norm_path = "/" + "/".join(resolved_segments)
        
        # 如果原始路径以斜杠结尾（表示文件夹），则保留这个特性
        if is_folder_path and final_norm_path != "/":
            final_norm_path += '/'
        
        # 现在使用这个 final_norm_path 从存储层获取节点信息
        # 注意：如果路径指向一个文件夹，存储层的 get_node_by_path 应该能够处理
        # （例如，OSSStorage 会检查是否存在以 / 结尾的0字节对象或前缀下有内容）
        node_data = await self.storage.get_node_by_path(self.conversation_id, final_norm_path)

        # 如果 node_data 为 None 但 final_norm_path 是 "/"，这是有效的（代表根）
        if node_data is None and final_norm_path == "/":
            return None, "/"
        
        # 如果解析的路径是文件夹，但存储层返回的路径不以 / 结尾，我们可能需要规范化一下
        # (这取决于 get_node_by_path 的实现，它应该返回带有正确尾部斜杠的文件夹路径)
        # OSSStorage的get_node_by_path会确保文件夹路径以/结尾，如果它是一个对象或隐式文件夹
        if node_data and node_data.get('node_type') == 'folder':
            if not final_norm_path.endswith('/') and final_norm_path != "/":
                final_norm_path += '/'
            if node_data.get('path') and not node_data['path'].endswith('/') and node_data['path'] != "/":
                 node_data['path'] += '/' # 确保返回的节点数据中的路径也是规范的

        logger.debug(f"解析路径到节点: 输入='{path_input}', 基础路径='{base_path_str}', 解析为路径='{final_norm_path}', 找到节点={node_data is not None}")
        return node_data, final_norm_path


    @register_tool
    async def get_current_status(self):
        """
        获取当前记忆库的工作状态概览。

        该函数返回一个字典，包含以下信息：
        - `success` (bool): 操作是否成功。
        - `status_text` (str): 当前状态的文本描述，包括当前路径和已打开标签页的摘要。
        - `current_path` (str): 当前工作目录的规范化路径。
        - `open_tabs_count` (int): 当前打开的标签页数量。
        - `max_open_tabs` (int): 允许打开的最大标签页数量。
        - `open_tabs_details` (List[Dict]]): 一个列表，包含每个打开标签页的详细信息，
          每个字典包含 `index` (int), `path` (str), `type` (str), `name` (str)。
        - `error` (str, optional): 如果操作失败，则包含错误信息。

        主要供用户调试或快速查看状态，LLM应更多关注动态更新的库提示。

        Returns:
            包含当前状态信息的字典。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        
        current_display_path = await self._get_path_string(self.current_path_str)
        tabs_info_details = []
        for i, tab in enumerate(self.open_tabs):
            tab_info_detail = f"{i}: {tab.path} ({tab.node_type})"
            if tab.node_type == 'file':
                tab_info_detail += f" - 内容长度: {len(tab.content) if tab.content else 0}"
            elif tab.node_type == 'folder' and tab.children_summary:
                tab_info_detail += f" - 子项数量: {len(tab.children_summary)}"
            tabs_info_details.append(tab_info_detail)
        
        status_text = f"当前路径: {current_display_path}\n打开的标签页 ({len(self.open_tabs)}/{self.max_open_tabs}):\n  "
        status_text += "\n  ".join(tabs_info_details) if tabs_info_details else "  (无打开的标签页)"

        return {
            "success": True,
            "status_text": status_text,
            "current_path": current_display_path,
            "open_tabs_count": len(self.open_tabs),
            "max_open_tabs": self.max_open_tabs,
            "open_tabs_details": [
                {"index": i, "path": t.path, "type": t.node_type, "name": t.name}
                for i, t in enumerate(self.open_tabs)
            ]
        }

    @register_tool
    async def create_item(
        self,
        name: str,
        item_type: str,
        content: str = None,
        parent_path: str = None
    ):
        """
        在记忆库中创建新的文件或文件夹。

        参数:
            name (str): 要创建的项目名称。名称只能包含字母、数字、下划线(_)、点(.)和连字符(-)，不能包含斜杠(/)。
            item_type (str): 要创建的项目类型，必须是 'file' 或 'folder'。
            content (str, optional): 如果 item_type 是 'file'，则此参数为文件的初始内容。默认为 None。
            parent_path (str, optional): 指定新项目创建位置的父文件夹路径。
                                         可以是绝对路径或相对于当前目录的路径。
                                         如果为 None 或空字符串，则在当前工作目录下创建。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，包含新创建项目的类型、名称和路径。
                - `path` (str, optional): 新创建项目的规范化完整路径。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}

        if item_type not in ['file', 'folder']:
            return {"success": False, "error": "无效的项目类型。必须是 'file' 或 'folder'。"}
        if not re.match(r"^[a-zA-Z0-9_.-]+$", name):
             return {"success": False, "error": "名称包含无效字符。只允许字母、数字、下划线、点和连字符。"}
        if '/' in name:
            return {"success": False, "error": "名称不能包含 '/'。请使用 parent_path 指定目录。"}

        # 如果父路径是文件夹，确保它以 / 结尾
        if parent_path:
            parent_path = parent_path.strip()
            # 对于根路径 "/" 不需要添加额外的斜杠
            if parent_path != "/" and not parent_path.endswith('/'):
                parent_path += '/'

        target_parent_path_str: str = None
        if parent_path:
            # 解析父路径以验证它是否存在并且是文件夹
            parent_node_data, resolved_parent_path_str = await self._resolve_path_to_node(parent_path, self.current_path_str)
            if parent_node_data is None and resolved_parent_path_str != "/": # 父路径不是根且未找到
                 return {"success": False, "error": f"父路径 '{parent_path}' (解析为 '{resolved_parent_path_str}') 无效或未找到。"}
            if parent_node_data and parent_node_data['node_type'] != 'folder':
                return {"success": False, "error": f"父路径 '{resolved_parent_path_str}' 不是一个文件夹。"}
            target_parent_path_str = resolved_parent_path_str
        else:
            target_parent_path_str = self.current_path_str
        
        # 确保目标父路径是一个文件夹路径（以 / 结尾）
        if target_parent_path_str != "/" and not target_parent_path_str.endswith('/'):
            target_parent_path_str += '/'
        
        # 构建新项目的完整路径
        if target_parent_path_str == "/":
            new_item_full_path = "/" + name
        else:
            new_item_full_path = target_parent_path_str + name
        
        # 对于文件夹，确保其路径以 / 结尾
        if item_type == 'folder' and not new_item_full_path.endswith('/'):
            new_item_full_path += '/'

        # 检查新路径是否已存在 (覆盖逻辑由存储层处理，但这里可以预先检查)
        if await self.storage.path_exists(self.conversation_id, new_item_full_path):
            return {"success": False, "error": f"在 '{target_parent_path_str}' 中已存在名为 '{name}' 的项目 (路径: '{new_item_full_path}')。"}

        try:
            new_node_data = await self.storage.create_node(
                conversation_id=self.conversation_id,
                path=new_item_full_path, # 传递完整路径
                node_type=item_type,
                content=content if item_type == 'file' else None
            )
            if not new_node_data:
                return {"success": False, "error": "创建项目失败（存储层返回空）。"}

            # new_node_data['path'] 应该是规范化的路径
            parent_display = target_parent_path_str if target_parent_path_str != "/" else "/"
            node_path = new_node_data['path']
            return {
                "success": True,
                "message": f"{item_type.capitalize()} '{name}' 已在 '{parent_display}' 中创建，路径为 '{node_path}'。",
                "path": node_path,
                # "node_id": new_node_data['id'] # id 可能不再是主要标识符，或等于path
            }
        except Exception as e:
            logger.error(f"创建项目 '{name}' 于父路径 '{target_parent_path_str}' 时出错: {traceback.format_exc()}")
            return {"success": False, "error": f"创建项目时发生内部错误: {str(e)}"}
        finally:
            await self._update_library_prompt()

    @register_tool
    async def list_directory(
        self,
        path: str = None
    ):
        """
        列出指定文件夹路径下的所有文件和子文件夹。

        参数:
            path (str, optional): 要列出其内容的文件夹路径。
                                  可以是绝对路径或相对于当前目录的路径。
                                  如果为 None 或空字符串，则列出当前工作目录的内容。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `path` (str): 被列出内容的目录的规范化路径。
                - `message` (str, optional): 操作成功的消息，描述了列出的项目数量。
                - `items` (List[Dict[str, Any]], optional): 一个列表，包含目录中每个项目的信息。
                  每个项目字典包含：
                    - `name` (str): 项目名称。
                    - `type` (str): 项目类型 ('file' 或 'folder')。
                    - `path` (str): 项目的完整规范路径。
                    - `created_at` (str, optional): 项目创建时间 (ISO 格式)。
                    - `updated_at` (str, optional): 项目最后修改时间 (ISO 格式)。
                - `total_items_in_directory` (int, optional): 目录中的项目总数。
                - `items_shown` (int, optional): 当前返回结果中显示的项目数量 (目前等于总数)。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}

        # 如果提供了路径，确保文件夹路径以斜杠结尾
        if path:
            path = path.strip()
            # 对于根路径 "/" 不需要添加额外的斜杠
            if path != "/" and not path.endswith('/'):
                path += '/'

        target_path_to_list = None
        if path:
            # 解析提供的路径以确保它是有效的文件夹路径
            node_data, resolved_path_str = await self._resolve_path_to_node(path, self.current_path_str)
            if node_data is None and resolved_path_str != "/":
                return {"success": False, "error": f"路径 '{path}' (解析为 '{resolved_path_str}') 无效或未找到。"}
            if node_data and node_data['node_type'] != 'folder':
                return {"success": False, "error": f"路径 '{resolved_path_str}' 不是一个文件夹。"}
            target_path_to_list = resolved_path_str
        else:
            target_path_to_list = self.current_path_str
        
        # 确保目标路径是一个文件夹路径（以 / 结尾，除了根路径）
        if target_path_to_list != "/" and not target_path_to_list.endswith('/'):
            target_path_to_list += '/'

        items_data_list = []
        try:
            # get_children_of_node 现在接受 parent_path
            children_nodes = await self.storage.get_children_of_node(self.conversation_id, target_path_to_list)
            for item_node in children_nodes:
                items_data_list.append({
                    "name": item_node['name'],
                    "type": item_node['node_type'],
                    "path": item_node['path'], # 使用path作为标识
                    "created_at": item_node.get('created_at'),
                    "updated_at": item_node.get('updated_at')
                })
            
            total_items = len(items_data_list)
            message = f"目录 '{target_path_to_list}' 包含 {total_items} 个项目:"
            
            return {
                "success": True,
                "path": target_path_to_list,
                "message": message,
                "items": items_data_list,
                "total_items_in_directory": total_items,
                "items_shown": total_items 
            }
        except Exception as e:
            logger.error(f"列出目录 '{target_path_to_list}' 时出错: {traceback.format_exc()}")
            return {"success": False, "error": f"列出目录时发生内部错误: {str(e)}"}

    @register_tool
    async def change_directory(
        self,
        path: str
    ):
        """
        更改当前工作目录到指定的文件夹路径。

        参数:
            path (str): 要切换到的目标文件夹路径。可以是绝对路径（以 '/' 开头）或
                        相对于当前目录的相对路径。路径不能为空。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，指示新的当前路径。
                - `new_current_path` (str, optional): 更改后的当前工作目录的规范化路径。
                - `directory_listing` (List[Dict[str, Any]], optional): 新当前目录下项目的列表，格式同 `list_directory` 返回的 `items`。
                - `listing_message` (str, optional): 关于新目录列表的消息。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        if not path:
            return {"success": False, "error": "路径不能为空。"}

        # 确保文件夹路径以斜杠结尾
        path_input = path.strip()
        # 对于根路径 "/" 不需要添加额外的斜杠
        if path_input != "/" and not path_input.endswith('/'):
            path_input += '/'

        node_data, resolved_path_str = await self._resolve_path_to_node(path_input, self.current_path_str)

        if resolved_path_str == "/": # 目标是根目录 (node_data此时为None)
            self.current_path_str = "/"
        elif node_data and node_data['node_type'] == 'folder':
            self.current_path_str = resolved_path_str # resolved_path_str 应该是规范的文件夹路径
            # 确保当前路径以斜杠结尾（如果不是根路径）
            if self.current_path_str != "/" and not self.current_path_str.endswith('/'):
                self.current_path_str += '/'
        elif node_data and node_data['node_type'] != 'folder':
             await self._update_library_prompt()
             return {"success": False, "error": f"路径 '{resolved_path_str}' 不是一个文件夹，无法进入。"}
        else: # node_data is None and resolved_path_str is not "/"
            await self._update_library_prompt()
            return {"success": False, "error": f"路径 '{path}' (解析为 '{resolved_path_str}') 无效或未找到。"}

        await self._update_library_prompt()
        current_path_str = await self._get_path_string(self.current_path_str) # 获取更新后的路径
        list_result = await self.list_directory(path=current_path_str)

        return {
            "success": True,
            "message": f"当前目录已更改为: {current_path_str}",
            "new_current_path": current_path_str,
            "directory_listing": list_result.get("items", []),
            "listing_message": list_result.get("message", "")
        }

    @register_tool
    async def open_item_to_tab(
        self,
        path: str
    ):
        """
        将指定路径的文件或文件夹加载到一个新的标签页中，以便快速访问其内容或摘要。

        如果项目已在标签页中打开，则不会重复打开，而是返回现有标签页的信息。
        标签页数量有限制，达到最大限制时无法打开新的标签页。

        参数:
            path (str): 要打开到标签页的文件或文件夹的路径。可以是绝对路径或相对路径。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，指示项目已打开到哪个标签页。
                - `tab_index` (int, optional): 打开或已存在的标签页的索引 (从0开始)。
                - `path` (str, optional): 被打开项目的规范化完整路径。
                - `item_type` (str, optional): 被打开项目的类型 ('file' 或 'folder')。
                - `name` (str, optional): 被打开项目的名称。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}

        if len(self.open_tabs) >= self.max_open_tabs:
            return {"success": False, "error": f"无法打开更多标签页。已达到最大限制 ({self.max_open_tabs})。请先关闭一些标签页。"}

        node_data, resolved_path_str = await self._resolve_path_to_node(path, self.current_path_str)

        if node_data is None and resolved_path_str != "/":
             return {"success": False, "error": f"路径 '{path}' (解析为 '{resolved_path_str}') 无效或未找到。"}
        if node_data is None and resolved_path_str == "/":
            return {"success": False, "error": "不能将根目录 '/' 打开到标签页。"}
        
        # node_data is guaranteed to be not None here because root case is handled
        item_path_in_tab = resolved_path_str # 使用已解析的、规范的路径
        item_name = node_data['name']
        item_type = node_data['node_type']

        for i, tab in enumerate(self.open_tabs):
            if tab.path == item_path_in_tab: # 比较路径
                # No need to update prompt if already open, just return info
                return {"success": True, "message": f"项目 '{item_path_in_tab}' 已在标签页 {i} 中打开。", "tab_index": i, "path": item_path_in_tab, "item_type": item_type, "name": item_name}
        
        tab_content = None
        tab_children_summary = None

        if item_type == 'file':
            # tab_content = node_data.get('content', "") # get_node_by_path 不再返回content
            tab_content = await self.storage.read_file_content(self.conversation_id, item_path_in_tab)
            if tab_content is None: # 读取失败或文件为空(read_file_content应返回""代表空，None代表错误)
                logger.warning(f"为标签页打开文件 '{item_path_in_tab}' 时未能读取内容。")
                # 可以决定是报错还是允许打开一个内容为None的标签页
                # 暂时允许，但LLM的prompt可能需要反映这一点
        elif item_type == 'folder':
            list_result = await self.list_directory(path=item_path_in_tab) # list_directory uses storage
            if list_result["success"]:
                tab_children_summary = list_result.get("items", [])
            else:
                logger.warning(f"为文件夹标签页 '{item_path_in_tab}' 获取子项摘要失败: {list_result.get('error')}")
        try:
            # OpenTab现在使用path，而不是node_id
            new_tab = OpenTab(path=item_path_in_tab, name=item_name, node_type=item_type, content=tab_content, children_summary=tab_children_summary)
            self.open_tabs.append(new_tab)
            tab_index = len(self.open_tabs) - 1
            return {"success": True, "message": f"项目 '{item_path_in_tab}' ({item_type}) 已打开到标签页 {tab_index}。", "tab_index": tab_index, "path": item_path_in_tab, "item_type": item_type, "name": item_name}
        except Exception as e:
            logger.error(f"打开标签页 '{path}' 时出错: {traceback.format_exc()}")
            return {"success": False, "error": f"打开标签页时发生内部错误: {str(e)}"}
        finally:
            await self._update_library_prompt()

    @register_tool
    async def close_tab(self, tab_index: int):
        """
        关闭指定索引的标签页，释放其占用的上下文空间。

        参数:
            tab_index (int): 要关闭的标签页的索引，从0开始。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，指示哪个标签页已被关闭。
                - `closed_tab_path` (str, optional): 被关闭标签页对应项目的路径。
                - `remaining_tabs_count` (int, optional): 关闭后剩余打开的标签页数量。
                - `open_tabs_count` (int, optional): (如果失败) 当前打开的标签页数量。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        if not 0 <= tab_index < len(self.open_tabs):
            return {"success": False, "error": f"无效的标签页索引 {tab_index}。有效范围 0-{len(self.open_tabs)-1}。", "open_tabs_count": len(self.open_tabs)}
        
        closed_tab = self.open_tabs.pop(tab_index)
        await self._update_library_prompt()
        return {"success": True, "message": f"标签页 {tab_index} ('{closed_tab.path}') 已关闭。", "closed_tab_path": closed_tab.path, "remaining_tabs_count": len(self.open_tabs)}

    @register_tool
    async def view_tab(self, tab_index: int):
        """
        查看指定索引的已打开标签页的详细内容。

        如果标签页对应的是文件，将返回文件内容（可能会因长度被截断）。
        如果标签页对应的是文件夹，将返回其子项目的摘要列表。

        参数:
            tab_index (int): 要查看的标签页的索引，从0开始。

        返回:
            一个字典，包含操作结果和标签页内容：
                - `success` (bool): 操作是否成功。
                - `tab_index` (int): 被查看的标签页索引。
                - `path` (str): 标签页对应项目的规范化路径。
                - `name` (str): 标签页对应项目的名称。
                - `type` (str): 标签页对应项目的类型 ('file' 或 'folder')。
                - `content` (str, optional): 如果是文件标签页，则为文件内容。
                                            如果内容过长，可能会被截断并在 `warning` 字段中提示。
                - `children` (List[Dict[str, Any]], optional): 如果是文件夹标签页，则为其子项目的摘要列表。
                                                             格式同 `list_directory` 返回的 `items` 中的单个项目。
                - `warning` (str, optional): 如果文件内容被截断，则包含警告信息。
                - `open_tabs_count` (int, optional): (如果失败) 当前打开的标签页数量。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        # Prompt update not strictly needed as viewing doesn't change state, but good for consistency if LLM relies on it
        # await self._update_library_prompt() # Not changing state, so not strictly needed before returning.

        if not 0 <= tab_index < len(self.open_tabs):
            return {"success": False, "error": f"无效的标签页索引 {tab_index}。有效范围 0-{len(self.open_tabs)-1}。", "open_tabs_count": len(self.open_tabs)}

        tab = self.open_tabs[tab_index]
        response_data = {"success": True, "tab_index": tab_index, "path": tab.path, "name": tab.name, "type": tab.node_type}

        if tab.node_type == 'file':
            response_data["content"] = tab.content
            if tab.content and len(tab.content) > config_module.TOOL_RESULT_MAX_LENGTH:
                 response_data["warning"] = f"文件内容过长，已部分截断以适应显示。完整内容在标签页中可用。"
                 response_data["content"] = tab.content[:config_module.TOOL_RESULT_MAX_LENGTH] + config_module.TRUNCATION_SUFFIX
        elif tab.node_type == 'folder':
            response_data["children"] = tab.children_summary
        return response_data

    @register_tool
    async def read_file(self, path: str):
        """
        读取指定路径文件的内容。

        如果文件已在标签页中打开，将优先从标签页读取内容。否则，从存储后端读取。
        返回的文件内容可能会因为过长而被截断，并通过 `warning` 和 `full_content_length` 字段提示。

        参数:
            path (str): 要读取内容的文件路径。可以是绝对路径或相对路径。

        返回:
            一个字典，包含操作结果和文件内容：
                - `success` (bool): 操作是否成功。
                - `path` (str, optional): 被读取文件的规范化完整路径。
                - `content` (str, optional): 文件的内容。如果内容为空或读取失败，可能为空字符串。
                                            如果内容过长被截断，这里是截断后的内容。
                - `warning` (str, optional): 如果文件内容被截断，则包含警告信息。
                - `full_content_length` (int, optional): 如果文件内容被截断，则指示原始内容的完整长度。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        node_data, resolved_path_str = await self._resolve_path_to_node(path, self.current_path_str)

        if node_data is None and resolved_path_str != "/": # Path is invalid and not root
             return {"success": False, "error": f"文件路径 '{path}' (解析为 '{resolved_path_str}') 无效或未找到。"}
        if node_data and node_data['node_type'] != 'file':
            return {"success": False, "error": f"路径 '{resolved_path_str}' 不是一个文件。"}
        if node_data is None and resolved_path_str == "/": # Trying to read root as a file
            return {"success": False, "error": "不能将根目录作为文件读取。"}
        
        # node_data is guaranteed to be a file node here
        file_path_to_read = resolved_path_str

        for tab in self.open_tabs:
            if tab.path == file_path_to_read and tab.node_type == 'file':
                logger.info(f"从打开的标签页读取文件 '{file_path_to_read}'")
                content_to_return = tab.content if tab.content is not None else ""
                # 添加截断逻辑
                if content_to_return and len(content_to_return) > config_module.TOOL_RESULT_MAX_LENGTH:
                    warning_msg = f"文件内容过长 (总长度: {len(content_to_return)})，已截断以适应输出。请使用标签页或分块读取以查看完整内容。"
                    logger.info(warning_msg)
                    return {
                        "success": True,
                        "path": file_path_to_read,
                        "content": content_to_return[:config_module.TOOL_RESULT_MAX_LENGTH] + config_module.TRUNCATION_SUFFIX,
                        "warning": warning_msg,
                        "full_content_length": len(content_to_return)
                    }
                return {"success": True, "path": file_path_to_read, "content": content_to_return}

        # Read from storage if not in tab or tab.content is None
        # file_node_data_from_storage = await self.storage.get_node_by_path(self.conversation_id, file_path_to_read)
        # content = file_node_data_from_storage.get('content', "") if file_node_data_from_storage else ""
        content_to_return = await self.storage.read_file_content(self.conversation_id, file_path_to_read)
        if content_to_return is None:
             logger.warning(f"读取文件 '{file_path_to_read}' 时未能从存储层获取内容。")
             # 根据策略，可以返回错误，或者返回空内容并记录
             # return {"success": False, "error": f"无法读取文件 '{file_path_to_read}' 的内容。"}
             content_to_return = "" # 假设读取失败或不存在时返回空字符串，或由存储层决定

        logger.info(f"从存储层读取文件 '{file_path_to_read}'")
        # Truncation logic (omitted for brevity)
        # 注意：截断逻辑应该在这里应用到 content_to_return
        if len(content_to_return) > config_module.TOOL_RESULT_MAX_LENGTH:
            warning_msg = f"文件内容过长 (总长度: {len(content_to_return)})，已截断以适应输出。请使用标签页或分块读取以查看完整内容。"
            logger.info(warning_msg)
            return {
                "success": True, 
                "path": file_path_to_read, 
                "content": content_to_return[:config_module.TOOL_RESULT_MAX_LENGTH] + config_module.TRUNCATION_SUFFIX,
                "warning": warning_msg,
                "full_content_length": len(content_to_return)
            }

        return {"success": True, "path": file_path_to_read, "content": content_to_return}


    @register_tool
    async def write_file(
        self,
        path: str,
        content: str,
        overwrite: bool = False
    ):
        """
        向指定路径的文件写入内容。

        如果文件不存在，则会尝试创建该文件并写入内容。
        如果文件已存在：
            - 若 `overwrite` 为 True，则覆盖现有内容。
            - 若 `overwrite` 为 False（默认），则操作失败并返回错误。
        如果指定路径是文件夹，则操作失败。
        如果文件已在标签页中打开，其标签页中的内容也会同步更新。

        参数:
            path (str): 要写入内容的目标文件路径。可以是绝对路径或相对路径。
            content (str): 要写入文件的文本内容。
            overwrite (bool): 如果文件已存在，是否覆盖其内容。默认为 False。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，指示文件是被创建还是被更新。
                - `path` (str, optional): 被写入或创建的文件的规范化完整路径。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        
        node_data, resolved_path_str = await self._resolve_path_to_node(path, self.current_path_str)
        
        message = ""

        try:
            if node_data and node_data['node_type'] == 'folder':
                return {"success": False, "error": f"路径 '{resolved_path_str}' 是一个文件夹，不能写入内容。"}

            if node_data and node_data['node_type'] == 'file': # File exists
                if not overwrite:
                    return {"success": False, "error": f"文件 '{resolved_path_str}' 已存在。如需覆盖，请设置 overwrite=True。"}
                updated_node = await self.storage.update_node_content(self.conversation_id, resolved_path_str, content)
                if not updated_node:
                     return {"success": False, "error": f"更新文件 '{resolved_path_str}' 内容失败。"}
                message = f"文件 '{resolved_path_str}' 内容已更新。"
            else: # File does not exist at this exact path, or _resolve_path_to_node returned None for a non-existing path segment
                  # We will attempt to create it using storage.create_node
                
                # create_node in storage now takes the full path.
                # We need to ensure parent directories implicitly exist or are handled by storage.
                # OSS storage does this automatically.
                
                created_node = await self.storage.create_node(self.conversation_id, resolved_path_str, 'file', content)
                if not created_node:
                     return {"success": False, "error": f"创建新文件 '{resolved_path_str}' 失败。"}
                message = f"新文件 '{resolved_path_str}' 已创建并写入内容。"
            
            # Update tab if open
            # target_node_id is no longer available directly, use path
            for tab in self.open_tabs:
                if tab.path == resolved_path_str:
                    tab.content = content
                    logger.info(f"已同步更新打开的标签页中文件 '{resolved_path_str}' 的内容。")
                    break
            
            return {"success": True, "message": message, "path": resolved_path_str}
        except Exception as e:
            logger.error(f"写入文件 '{resolved_path_str}' 时出错: {traceback.format_exc()}")
            return {"success": False, "error": f"写入文件时发生内部错误: {str(e)}"}
        finally:
            await self._update_library_prompt()

    @register_tool
    async def delete_item(self, path: str):
        """
        删除指定路径的文件或文件夹。

        如果删除的是文件夹，其包含的所有内容（文件和子文件夹）也将被递归删除。
        如果被删除的项目或其子项目已在标签页中打开，相关的标签页将被自动关闭。
        如果当前工作目录是被删除的文件夹或其子文件夹，当前路径将被重置到其父目录或根目录。
        不能删除根目录 ('/')。

        参数:
            path (str): 要删除的文件或文件夹的路径。可以是绝对路径或相对路径。

        返回:
            一个字典，包含操作结果：
                - `success` (bool): 操作是否成功。
                - `message` (str, optional): 操作成功的消息，描述了被删除的项目以及可能发生的相关操作
                                            （如关闭标签页、重置当前路径）。
                - `error` (str, optional): 如果操作失败，则包含错误信息。
        """
        if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
        node_data, resolved_path_str = await self._resolve_path_to_node(path, self.current_path_str)

        if resolved_path_str == "/":
            return {"success": False, "error": "不能删除根目录。"}
        # node_data might be None if the path doesn't exist, or if it points to an implicit folder not having a direct object.
        # However, for deletion, we need a concrete target. path_exists might be better here before calling delete.
        if not await self.storage.path_exists(self.conversation_id, resolved_path_str):
            return {"success": False, "error": f"路径 '{path}' (解析为 '{resolved_path_str}') 未找到，无法删除。"}

        # Path exists. Get its type for the message.
        # Use resolved_path_str which is canonical from _resolve_path_to_node.
        node_data_for_type_msg = await self.storage.get_node_by_path(self.conversation_id, resolved_path_str)
        
        if node_data_for_type_msg is None:
            # This case implies path_exists is true, but get_node_by_path returns None.
            # Could be an implicit folder that get_node_by_path doesn't return a full node for,
            # or a race condition. For the message, infer from path.
            logger.warning(f"delete_item: 路径 '{resolved_path_str}' 存在，但 get_node_by_path 返回 None。从路径推断类型用于消息。")
            node_type_for_delete_message = '文件夹' if resolved_path_str.endswith('/') else '项目'
        else:
            node_type_for_delete_message = '文件夹' if node_data_for_type_msg['node_type'] == 'folder' else '文件'
        
        # item_id_to_delete = node_data['id'] # No longer using ID here
        # item_type = node_data['node_type']
        
        try:
            success = await self.storage.delete_node_recursive(self.conversation_id, resolved_path_str)
            if not success:
                return {"success": False, "error": f"存储层删除项目 '{resolved_path_str}' 失败。"}

            tabs_to_remove_indices = []
            for i, tab in enumerate(self.open_tabs):
                # Check if tab.path is the deleted path or is a child of the deleted path (if folder)
                if tab.path == resolved_path_str or \
                   (node_type_for_delete_message == '文件夹' and resolved_path_str.endswith('/') and tab.path.startswith(resolved_path_str)):
                    tabs_to_remove_indices.append(i)
            
            closed_tab_paths = []
            for i in sorted(tabs_to_remove_indices, reverse=True):
                closed_tab = self.open_tabs.pop(i)
                closed_tab_paths.append(closed_tab.path)
            
            message_main = f"{node_type_for_delete_message} '{resolved_path_str}' 已成功删除。"
            if closed_tab_paths:
                message_main += f" 以下相关标签页已关闭: {', '.join(closed_tab_paths)}。"
            
            # If deleted item was part of the current path, navigate to its parent or root
            if self.current_path_str.startswith(resolved_path_str):
                # If current path was the deleted item itself or a child of it
                parent_of_deleted_path = "/" + "/".join(resolved_path_str.strip('/').split('/')[:-1])
                if not parent_of_deleted_path or parent_of_deleted_path == "/":
                    self.current_path_str = "/"
                else: # Ensure parent_of_deleted_path is a valid folder path (ends with /)
                    if not parent_of_deleted_path.endswith('/'): parent_of_deleted_path += '/'
                    self.current_path_str = parent_of_deleted_path
                message_main += f" 当前路径已重置到 '{self.current_path_str}'。"

            return {"success": True, "message": message_main}
        except Exception as e:
            logger.error(f"删除 '{resolved_path_str}' 时出错: {traceback.format_exc()}")
            return {"success": False, "error": f"删除时发生内部错误: {str(e)}"}
        finally:
            await self._update_library_prompt()

    # Placeholder for future search functionality
    # @register_tool
    # async def search_memory(query: str, search_path: Optional[str] = None, item_type: Optional[str] = None) -> Dict[str, Any]:
    #     if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
    #     if not self.model: return {"success": False, "error": "LLM模型未初始化，无法执行语义搜索。"}
    #     # Implementation would involve:
    #     # 1. Listing candidate nodes (files) from self.storage based on search_path and item_type.
    #     # 2. For each candidate, possibly using self.model to assess relevance of content to query.
    #     # 3. Returning a list of relevant node paths and summaries.
    #     logger.info(f"搜索记忆 (未完全实现): 查询='{query}', 路径='{search_path}', 类型='{item_type}'")
    #     return {"success": False, "error": "搜索功能暂未完全实现。"}

    # rename_node 和 move_node 的工具暂时不实现，等待存储层支持
    # @register_tool
    # async def rename_item(self, old_path: str, new_name: str) -> Dict[str, Any]:
    #     if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
    #     # ... 实现调用 self.storage.rename_node ...
    #     # ... 更新标签页 ...
    #     # ... 更新提示 ...
    #     raise NotImplementedError("重命名项目工具尚未完全实现。")

    # @register_tool
    # async def move_item(self, source_path: str, target_parent_path: str) -> Dict[str, Any]:
    #     if not self.storage: return {"success": False, "error": "记忆存储未初始化。"}
    #     # ... 实现调用 self.storage.move_node ...
    #     # ... 更新标签页和当前路径（如果受影响） ...
    #     # ... 更新提示 ...
    #     raise NotImplementedError("移动项目工具尚未完全实现。")

