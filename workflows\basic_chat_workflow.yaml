description: "基础对话工作流，指导智能体如何处理用户对话，包括记忆管理、文档搜索和回复生成。"
metadata:
  version: "0.1"

steps:
  - name: "init"
    description: "初始化对话处理流程"
    operation: "NOP"

  - name: "query_relevant_infos"
    description: "查询与当前对话相关的记忆内容和文档内容"
    operation: "EXECUTE"
    actions: 
      - names: 
        - "memory.query_memory"
        min_calls: 1
        max_calls: 1
      - names: 
        - "document.search_in_documents"
        min_calls: 1
        max_calls: 1

  - name: "check_important_info"
    description: "判断当前对话是否包含需要记忆的信息"
    operation: "CONDITION"
    condition: "contains_important_info"
    condition_description: "检查用户输入是否包含值得记忆的个人信息、偏好、重要事实等"
    true_branch: "add_memory"
    false_branch: "generate_response"

  - name: "add_memory"
    description: "添加新记忆到记忆库"
    operation: "EXECUTE"
    actions: 
      - names: 
        - "memory.add_memory"
        min_calls: 1
        max_calls: 1

  - name: "generate_response"
    description: "结合记忆和文档搜索结果生成回复给用户"
    operation: "GENERATE"
    wait_user: true
    next: "query_relevant_infos"

registers: {}
