"""
该模块定义了多种类型的通信连接，用于在系统不同组件之间（例如智能体、用户界面、工具等）进行异步消息传递。
它对底层通信机制（如进程内队列或网络WebSocket）进行了抽象，提供了一致的接口。

主要组件与功能:

1.  `BaseConnection` (Protocol):
    *   定义了所有连接类型必须实现的通用异步接口 (`send`, `recv`, `close`)。
    *   确保不同连接实现之间的互操作性。

2.  `QueueConnection`:
    *   基于 `asyncio.Queue` 实现，用于进程内的高效、全双工通信。
    *   适用于在同一应用程序内的不同协程或任务之间传递消息。

3.  `WebSocketConnection`:
    *   基于 `websockets` 库实现，用于通过WebSocket进行网络通信。
    *   封装了消息的JSON序列化/反序列化以及连接生命周期管理。
    *   适用于客户端或服务器端的WebSocket连接。

4.  `FastAPIWebSocketConnection`:
    *   专门为FastAPI框架设计的WebSocket连接实现。
    *   直接包装FastAPI的 `WebSocket` 对象，并使其与 `BaseConnection` 协议兼容。
    *   处理与FastAPI WebSocket相关的特定异常和行为。

5.  `ConnectionPair`:
    *   一个工厂类，用于创建一对相互连接的 `Connection` 对象。
    *   支持两种模式：
        *   **队列模式**: 创建两个 `QueueConnection` 实例，它们的输入/输出队列交叉连接，用于测试或本地通信。
        *   **WebSocket模式**: 启动一个本地WebSocket服务器，并创建一个客户端连接到该服务器，从而建立一个基于网络的连接对。
    *   简化了设置双向通信通道的过程。

6.  `DummyConnection`:
    *   一个虚拟连接实现，遵循 `BaseConnection` 协议，但不执行实际的通信。
    *   主要用于系统预热或测试场景，其中需要一个连接对象但不需要实际的消息传递。
    *   发送操作仅记录日志，接收操作返回固定的虚拟消息。

该模块通过提供这些抽象和实现，旨在简化异步通信逻辑的开发，
使得系统组件可以灵活地选择适合其需求的通信方式，同时保持接口的一致性。
"""

import asyncio
import json
import logging
from typing import Any, Dict, Optional, Tuple, Union, List, Callable, TypeVar, Protocol, runtime_checkable, Awaitable, cast
import traceback
from abc import ABC, abstractmethod
from log import logger
from utils import dumps

# 导入WebSocket相关模块
from websockets.asyncio.client import connect as ws_connect
from websockets.asyncio.server import serve as ws_serve
from websockets.exceptions import ConnectionClosed
from fastapi import WebSocket, WebSocketDisconnect

# 定义一个类型变量，表示连接类型
C = TypeVar('C', bound='BaseConnection')

@runtime_checkable
class BaseConnection(Protocol):
    """连接的协议，定义统一接口"""
    
    async def send(self, message: Dict[str, Any]) -> None:
        """发送消息"""
        ...
    
    async def recv(self) -> Dict[str, Any]:
        """接收消息"""
        ...
    
    async def close(self) -> None:
        """关闭连接"""
        ...


class QueueConnection:
    """
    基于队列的连接类，用于进程内通信
    提供全双工通信能力，支持异步发送和接收
    """
    def __init__(self, 
                input_queue: Optional[asyncio.Queue] = None, 
                output_queue: Optional[asyncio.Queue] = None):
        """
        初始化连接
        
        参数:
            input_queue: 输入队列，默认创建新队列
            output_queue: 输出队列，默认创建新队列
        """
        # 如果没有提供队列，创建新的队列
        self.input_queue = input_queue or asyncio.Queue()
        self.output_queue = output_queue or asyncio.Queue()
        
        # 连接状态
        self._closed = False
    
    async def send(self, message: Dict[str, Any]) -> None:
        """
        发送消息
        
        参数:
            message: 消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
        """
        if self._closed:
            raise RuntimeError("连接已关闭")
            
        # 放入输出队列
        await self.output_queue.put(message)
    
    async def recv(self) -> Dict[str, Any]:
        """
        接收消息
        
        返回:
            接收到的消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
        """
        if self._closed:
            raise RuntimeError("连接已关闭")
        
        # 从输入队列获取消息
        message = await self.input_queue.get()
        
        # 不调用task_done，由于这是一个全双工通信接口
        # 我们不需要关心队列的任务是否完成
        # 只需要关心消息的传递
        return message
    
    async def close(self) -> None:
        """关闭连接"""
        if self._closed:
            return
        
        self._closed = True
        logger.info("队列连接已关闭")


class WebSocketConnection:
    """
    基于WebSocket的连接类，用于网络通信
    提供全双工通信能力，支持异步发送和接收
    """
    def __init__(self, websocket: Any):
        """
        初始化WebSocket连接
        
        参数:
            websocket: WebSocket连接对象
        """
        self.websocket = websocket
        self._closed = False
    
    async def send(self, message: Dict[str, Any]) -> None:
        """
        发送消息
        
        参数:
            message: 消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
        """
        if self._closed:
            raise RuntimeError("WebSocket连接已关闭")
        
        # 将消息转换为JSON字符串并发送
        try:
            await self.websocket.send(dumps(message))
        except ConnectionClosed:
            self._closed = True
            raise RuntimeError("WebSocket连接已关闭") from None
    
    async def recv(self) -> Dict[str, Any]:
        """
        接收消息
        
        返回:
            接收到的消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
        """
        if self._closed:
            raise RuntimeError("WebSocket连接已关闭")
        
        # 接收JSON字符串并转换为字典
        try:
            message_str = await self.websocket.recv()
            try:
                return json.loads(message_str)
            except json.JSONDecodeError:
                logger.error(f"无法解析WebSocket消息: {message_str}")
                return {"type": "error", "content": "无法解析消息", "conversation_id": "system"}
        except ConnectionClosed:
            self._closed = True
            raise RuntimeError("WebSocket连接已关闭") from None
    
    async def close(self) -> None:
        """关闭WebSocket连接"""
        if self._closed:
            return
        
        try:
            await self.websocket.close()
        except Exception as e:
            logger.error(f"关闭WebSocket连接时出错: {e}")
        
        self._closed = True
        logger.info("WebSocket连接已关闭")


class FastAPIWebSocketConnection:
    """
    基于FastAPI WebSocket的连接类，提供与BaseConnection兼容的接口
    直接包装FastAPI的WebSocket对象
    """
    def __init__(self, websocket: WebSocket):
        """
        初始化FastAPI WebSocket连接
        
        参数:
            websocket: FastAPI WebSocket对象
        """
        self.websocket = websocket
        self._closed = False
    
    async def send(self, message: Dict[str, Any]) -> None:
        """
        发送消息到WebSocket客户端
        
        参数:
            message: 消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
        """
        if self._closed:
            raise RuntimeError("FastAPI WebSocket连接已关闭")
        
        try:
            # 将消息字典转换为JSON并发送文本
            await self.websocket.send_text(dumps(message))
        except WebSocketDisconnect:
            self._closed = True
            raise RuntimeError("FastAPI WebSocket客户端断开连接") from None
        except Exception as e:
            logger.error(f"发送WebSocket消息时出错: {e}")
            raise RuntimeError(f"发送WebSocket消息失败: {str(e)}") from e
    
    async def recv(self) -> Dict[str, Any]:
        """
        从WebSocket客户端接收消息
        
        返回:
            接收到的消息字典
        
        抛出:
            RuntimeError: 如果连接已关闭
            WebSocketDisconnect: 如果WebSocket连接断开
        """
        if self._closed:
            raise RuntimeError("FastAPI WebSocket连接已关闭")
        
        try:
            # 接收文本消息并解析为JSON
            message_text = await self.websocket.receive_text()
            try:
                return json.loads(message_text)
            except json.JSONDecodeError as e:
                logger.error(f"无法解析WebSocket消息: {message_text}")
                return {"type": "error", "content": f"无法解析JSON消息: {str(e)}", "conversation_id": "system"}
        except WebSocketDisconnect:
            self._closed = True
            raise  # 直接重新抛出WebSocketDisconnect异常，让调用者处理
        except Exception as e:
            logger.error(f"接收WebSocket消息时出错: {e}")
            raise RuntimeError(f"接收WebSocket消息失败: {str(e)}") from e
    
    async def close(self) -> None:
        """关闭WebSocket连接"""
        if self._closed:
            return
        
        try:
            await self.websocket.close()
        except Exception as e:
            logger.error(f"关闭WebSocket连接时出错: {e}")
        
        self._closed = True
        logger.info("FastAPI WebSocket连接已关闭")


# 为了兼容性，定义Connection类型
Connection = Union[QueueConnection, WebSocketConnection, FastAPIWebSocketConnection, BaseConnection]


class ConnectionPair:
    """
    连接对，用于创建两个相互连接的Connection对象
    支持基于队列的测试模式和基于WebSocket的网络模式
    """
    def __init__(self, use_websocket=False, host='localhost', port=8765):
        """
        创建一对相互连接的Connection对象
        
        参数:
            use_websocket: 是否使用WebSocket连接
            host: WebSocket服务器主机
            port: WebSocket服务器端口
        """
        self.use_websocket = use_websocket
        self.host = host
        self.port = port
        self.server = None
        self.connection_a: Optional[Connection] = None
        self.connection_b: Optional[Connection] = None
        self.server_task = None
        
        if not use_websocket:
            # 使用队列创建连接对
            self._create_queue_pair()
            logger.info("创建了基于队列的连接对")
    
    def _create_queue_pair(self):
        """创建基于队列的连接对"""
        maxsize = 100
        queue_a = asyncio.Queue(maxsize=maxsize)
        queue_b = asyncio.Queue(maxsize=maxsize)
        
        # 创建两个连接，交叉连接队列
        self.connection_a = QueueConnection(input_queue=queue_a, output_queue=queue_b)
        self.connection_b = QueueConnection(input_queue=queue_b, output_queue=queue_a)
    
    async def _handle_websocket(self, websocket):
        """
        处理WebSocket连接
        
        参数:
            websocket: WebSocket连接对象
        """
        logger.info(f"收到新的WebSocket连接")
        # 为服务器端创建一个WebSocket连接
        self.connection_a = WebSocketConnection(websocket)
        
        # 等待连接关闭
        try:
            while True:
                await asyncio.sleep(1)
        except Exception:
            pass
    
    async def start_server(self):
        """启动WebSocket服务器"""
        if not self.use_websocket:
            logger.info("在非WebSocket模式下不需要启动服务器")
            return
        
        # 启动WebSocket服务器
        self.server = await ws_serve(
            self._handle_websocket, 
            self.host, 
            self.port
        )
        logger.info(f"WebSocket服务器已启动在 ws://{self.host}:{self.port}")
        
        # 创建一个任务来保持服务器运行
        self.server_task = asyncio.create_task(self.server.wait_closed())
    
    async def create_client(self):
        """创建WebSocket客户端连接"""
        if not self.use_websocket:
            logger.info("在非WebSocket模式下不需要创建客户端")
            return
        
        # 连接到WebSocket服务器
        websocket = await ws_connect(f"ws://{self.host}:{self.port}")
        self.connection_b = WebSocketConnection(websocket)
        logger.info(f"已连接到WebSocket服务器 ws://{self.host}:{self.port}")
    
    async def get_connection_async(self) -> Tuple[Connection, Connection]:
        """异步获取连接对（WebSocket模式需要）"""
        if self.use_websocket:
            # 如果还没启动服务器，先启动
            if not self.server:
                await self.start_server()
            
            # 如果还没创建客户端连接，创建一个
            if not self.connection_b:
                await self.create_client()
            
            # 等待服务器端连接建立
            retry_count = 0
            while not self.connection_a and retry_count < 10:
                await asyncio.sleep(0.2)
                retry_count += 1
            
            if not self.connection_a or not self.connection_b:
                raise RuntimeError("WebSocket连接未完全建立")
                
            # 使用类型检查确保返回的连接对是有效的
            return cast(Connection, self.connection_a), cast(Connection, self.connection_b)
        
        # 确保队列模式下的连接已创建
        if not self.connection_a or not self.connection_b:
            self._create_queue_pair()
            
        return cast(Connection, self.connection_a), cast(Connection, self.connection_b)
    
    def get_connection(self) -> Tuple[Connection, Connection]:
        """
        获取连接对（队列模式直接返回，WebSocket模式需要先异步初始化）
        
        注意：在WebSocket模式下，必须先调用get_connection_async
        """
        if self.use_websocket and (not self.connection_a or not self.connection_b):
            raise RuntimeError("在WebSocket模式下，必须先调用get_connection_async")
        
        return cast(Connection, self.connection_a), cast(Connection, self.connection_b)

    async def close(self) -> None:
        """关闭连接对"""
        # 关闭连接
        if self.connection_a:
            await self.connection_a.close()
        if self.connection_b:
            await self.connection_b.close()
        
        # 关闭服务器
        if self.server:
            self.server.close()
        
        logger.info("连接对已关闭")


class DummyConnection:
    """
    虚拟连接类，用于Agent预热
    实现BaseConnection协议，但不实际发送消息，只记录日志
    """
    def __init__(self):
        """初始化虚拟连接"""
        self._closed = False
        logger.debug("创建了虚拟连接(DummyConnection)用于预热")
    
    async def send(self, message: Dict[str, Any]) -> None:
        """
        模拟发送消息，实际上只记录日志
        
        参数:
            message: 消息字典
        """
        if self._closed:
            logger.debug("尝试通过已关闭的虚拟连接发送消息")
            return
            
        logger.debug(f"虚拟连接收到发送请求: {message.get('type', 'unknown')}")
    
    async def recv(self) -> Dict[str, Any]:
        """
        模拟接收消息，实际上只返回空消息
        
        返回:
            空消息字典
        """
        if self._closed:
            logger.debug("尝试从已关闭的虚拟连接接收消息")
            return {"type": "error", "content": "连接已关闭"}
            
        logger.debug("虚拟连接模拟接收消息")
        return {"type": "dummy", "content": "预热消息"}
    
    async def close(self) -> None:
        """关闭虚拟连接"""
        if self._closed:
            return
            
        self._closed = True
        logger.debug("虚拟连接已关闭") 