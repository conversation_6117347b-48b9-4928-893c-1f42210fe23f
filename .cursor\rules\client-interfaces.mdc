---
description: MeowAgent 提供多种客户端接口，使用户能够通过不同方式与智能体进行交互。
globs: 
alwaysApply: false
---
# MeowAgent 客户端接口

MeowAgent 提供多种客户端接口，使用户能够通过不同方式与智能体进行交互。

## 接口概览

MeowAgent 支持三种主要的用户界面：

1. [cli.py](mdc:meowagent/cli.py) - 命令行界面，适合开发测试和脚本集成
2. [api.py](mdc:meowagent/api.py) - 基于FastAPI的Web API，支持多用户和WebSocket
3. [tui.py](mdc:meowagent/tui.py) - 基于Textual库的文本用户界面，提供富文本交互体验

## 命令行界面 (CLI)

命令行界面提供简单直观的控制台交互：

- 支持流式输出，实时显示智能体思考过程和回复
- 允许在多个对话之间切换
- 使用彩色输出区分不同类型的消息
- 通过 `QueueConnection` 与 Agent 进行异步通信

使用方式：
```bash
# 启动新对话
python cli.py --stream

# 使用现有对话ID
python cli.py --user <conversation_id>
```

## Web API 服务器

[api.py](mdc:meowagent/api.py) 实现了一个功能完整的FastAPI服务器：

- 提供 WebSocket 端点 (`/ws`) 进行实时双向通信
- 支持多用户同时连接，每个用户拥有独立的 Agent 实例
- 提供 RESTful HTTP 端点管理对话和消息
- 集成 CORS 中间件支持跨域请求

主要端点：
```
WebSocket:
/ws  - 主要的WebSocket连接点

REST API:
GET    /api/v1/conversations - 列出所有对话
DELETE /api/v1/conversations/{conversation_id} - 删除指定对话
GET    /api/v1/conversations/{conversation_id}/messages - 获取对话消息
PUT    /api/v1/conversations/{conversation_id}/messages/reorder - 重排序消息
```

## 文本用户界面 (TUI)

[tui.py](mdc:meowagent/tui.py) 是基于 [Textual](mdc:meowagent/https:/textual.io) 构建的高级文本界面：

- 支持多窗格布局，同时显示对话、工具调用和状态
- 提供富文本渲染，包括代码高亮和Markdown格式
- 使用键盘快捷键进行导航和操作
- 具有深色/浅色主题切换功能

## 通用连接架构

所有客户端接口通过 [connection.py](mdc:meowagent/connection.py) 中定义的连接抽象与Agent交互：

- `BaseConnection` - 定义了连接协议
- `QueueConnection` - 基于队列的进程内连接
- `WebSocketConnection` - 基于WebSocket的网络连接
- `FastAPIWebSocketConnection` - 为FastAPI定制的WebSocket连接
- `ConnectionPair` - 创建一对相互连接的对象的工厂

```python
# 在CLI中创建连接对
in_conn, out_conn = ConnectionPair.create_queue_pair()

# Agent使用out_conn作为输出连接
agent = Agent(model, conversation, output_connection=out_conn)

# CLI使用in_conn接收来自Agent的消息
async def process_message_connection(connection, message_handler):
    while True:
        message = await connection.recv()
        await message_handler.handle_message(message)
```

## Web前端

MeowAgent-Web 前端实现位于独立的 [meowagent-web](mdc:meowagent/meowagent-web) 目录：

- 基于 Next.js 和 React 构建
- 使用 WebSocket 与后端API服务器通信
- 提供响应式布局和现代化UI
- 支持消息历史、工具调用可视化和配置面板

