"""
管理单个对话会话（Conversation）的核心模块。

主要功能与逻辑：

1.  **对话隔离与状态管理**：
    *   每个 `Conversation` 实例通过 `conversation_id` 与特定的对话相关联。
    *   封装 `History` 对象 (`self.history`) 来处理底层消息的持久化存储（如数据库）和检索。

2.  **消息操作接口**：
    *   提供 `add_message` 方法向当前对话添加新消息，并触发持久化。
    *   提供 `get_messages`、`get_recent_messages` 和 `load_all_messages` (通过 `history` 对象)
        来从持久化存储中异步加载消息。

3.  **消息渲染与格式化**：
    *   利用 `RendererPipeline` (`self.renderer_pipeline`) 对消息列表进行灵活的转换和格式化。
    *   `get_formatted_messages`: 将消息列表渲染成符合 OpenAI API 输入格式的字典列表，
        支持基于模型和 `max_tokens` 的自动裁剪。
    *   `get_serializable_messages`: 将消息对象列表转换为可序列化的字典格式，方便进行 API 传输或日志记录。

4.  **与大语言模型 (LLM) 交互**：
    *   `generate`: 核心方法，用于调用 `OpenAIModel` 生成回复。
        它会先使用渲染管道准备输入消息，然后调用模型，并处理模型返回的文本内容和工具调用。
        支持流式输出 (`stream_callback`)。
    *   `convert_tools`: 静态辅助方法，将内部定义的工具列表转换为 OpenAI API 所要求的工具格式。
    *   `count_tokens`: 计算当前对话历史（经过渲染后）对于特定模型所占用的 token 数量。

5.  **工具调用支持**：
    *   在 `generate` 方法中，能够解析模型返回的工具调用请求，并将其封装到 `AssistantMessage` 的 `tool_calls` 属性中。

6.  **后台摘要生成**：
    *   `start_summary_generation_task`: 启动一个异步后台任务，为对话中没有摘要的消息生成简短摘要。
    *   `_generate_summaries_for_messages`: 实际执行摘要生成的异步方法，调用 LLM 生成摘要并更新到数据库。
    *   `is_summary_task_running`: 检查摘要生成任务的运行状态。

7.  **外部历史同步**：
    *   `sync_history_from_api`: 允许从外部源（例如 API 请求体中的消息列表）完全替换当前对话的历史记录。

该模块通过组合 `History`、`RendererPipeline` 和 `OpenAIModel` 等组件，
为上层应用（如 Agent）提供了管理和驱动对话流程的完整功能。
"""

import asyncio
import uuid
from history import History
# 导入消息类型和渲染函数
from message import (
    AssistantMessage, Message, SystemMessage, AgentSystemMessage,
    ToolCall, ToolCallFunction, ToolMessage, UserMessage,
)
from model import OpenAIModel
from typing import Any, Callable, Dict, List, Optional, Set, Union
# 导入新的渲染器组件
from renderer import RendererPipeline, DEFAULT_RENDERER_PIPELINE
from log import logger
from datetime import datetime

class Conversation:
    def __init__(self, conversation_id: Optional[str] = None):
        """
        初始化对话

        参数:
            conversation_id: 对话ID，用于隔离不同用户的对话
        """
        self.conversation_id = conversation_id
        self.history = History(conversation_id)
        # 使用默认渲染管道
        self.renderer_pipeline: RendererPipeline = DEFAULT_RENDERER_PIPELINE

        # 后台任务相关属性
        self._summary_task: Optional[asyncio.Task] = None  # 摘要生成任务

    # === 核心历史操作 (现在直接调用异步的 History 方法) ===

    async def add_message(self, message: Message):
        """
        添加消息到历史并持久化。

        参数:
            message: 消息对象
        """
        await self.history.add_message(message)

    def get_history_manager(self) -> History: # Renamed for clarity
        """
        获取历史记录管理器对象 (History instance)。

        返回:
            History对象
        """
        return self.history

    async def load_all_messages(self, limit: Optional[int] = None) -> List[Message]: # Renamed
        """
        加载历史消息。调用 History.load_messages。

        参数:
            limit: 限制加载的消息数量，默认为None（加载所有消息）

        返回:
            消息对象列表
        """
        return await self.history.load_messages(limit)

    async def get_messages(self) -> List[Message]:
        """
        获取所有历史消息（从History对象获取，这将从数据库加载）。

        返回:
            消息对象列表的副本
        """
        return await self.history.get_messages() # History.get_messages() 已改为从DB加载

    async def get_recent_messages(self, n: int) -> List[Message]:
        """
        获取最近的n条历史消息（从History对象获取，这将从数据库加载）。

        参数:
            n: 消息数量

        返回:
            最近n条消息对象列表
        """
        return await self.history.get_recent_messages(n)

    # === 消息编译与格式化 (使用 RendererPipeline) ===

    async def get_formatted_messages(self, exclude_system: bool = True, max_tokens: Optional[int] = None, model: Optional[OpenAIModel] = None) -> List[Dict[str, Any]]:
        """
        使用渲染管道获取符合OpenAI格式的消息列表，并可选择进行裁剪

        参数:
            exclude_system: 是否排除系统消息
            max_tokens: 最大token数限制，None表示不限制
            model: 用于计算token的模型对象，与max_tokens一起提供才会进行裁剪

        返回:
            符合OpenAI格式的消息字典列表
        """
        messages = await self.get_messages()
        context = {
            "exclude_system": exclude_system,
            "max_tokens": max_tokens,
            "model": model
        }
        return await self.renderer_pipeline.render(messages, **context)

    async def get_serializable_messages(self) -> List[Dict[str, Any]]:
        """
        获取所有历史消息的可序列化版本，包含完整的消息属性。
        会调用每个 Message 对象的 to_dict() 方法。

        返回:
            List[Dict[str, Any]]: 一个包含所有消息完整字典的列表。
        """
        messages = await self.history.get_messages()
        serialized_messages = [message.to_dict() for message in messages]
        return serialized_messages

    @staticmethod
    def convert_tools(tools: Optional[List[Dict[str, Any]]]) -> Optional[List[Dict[str, Any]]]:
        if tools is None:
            return None
            
        formatted_tools = []
        for tool_dict in tools: # Renamed tool to tool_dict for clarity
            if not isinstance(tool_dict, dict):
                logger.warning(f"工具定义格式无效（非字典），已跳过: {tool_dict}")
                continue

            # 检查是否已经是 OpenAI 格式
            if tool_dict.get("type") == "function" and "function" in tool_dict and isinstance(tool_dict["function"], dict) and "name" in tool_dict["function"]:
                # 已经是 OpenAI 格式，直接使用，但确保 parameters 存在
                if "parameters" not in tool_dict["function"]:
                    tool_dict["function"]["parameters"] = {"type": "object", "properties": {}} # OpenAI 要求 parameters
                formatted_tools.append(tool_dict)
            # 检查是否是我们的简化格式
            elif "name" in tool_dict and "description" in tool_dict:
                # 转换为 OpenAI 格式
                # 确保 input_schema 存在或默认为空字典，并符合OpenAI参数结构
                input_schema = tool_dict.get("input_schema", {})
                if not (isinstance(input_schema, dict) and input_schema.get("type") == "object" and "properties" in input_schema):
                    # 如果 input_schema 无效或不符合 OpenAI 结构，则提供一个默认的空参数对象
                    logger.debug(f"工具 '{tool_dict['name']}' 的 input_schema 无效或缺失，使用默认空参数。 Schema: {input_schema}")
                    parameters = {"type": "object", "properties": {}}
                else:
                    parameters = input_schema
                
                formatted_tools.append({
                    "type": "function",
                    "function": {
                        "name": tool_dict["name"],
                        "description": tool_dict["description"],
                        "parameters": parameters
                    }
                })
            else:
                logger.warning(f"工具定义格式无效（缺少name/description或非OpenAI格式），已跳过: {tool_dict}")
        
        return formatted_tools if formatted_tools else None # Return None if list is empty

    async def generate(self, model: OpenAIModel, tools: Optional[List[Dict]] = None, stream_callback: Optional[Callable] = None, n: int = 1, **kwargs) -> Union[Message, List[Message]]:
        """
        使用模型生成响应，内部调用渲染管道编译消息。

        参数:
            model: 用于生成的 OpenAIModel 对象。
            tools: 可选的工具列表（字典格式）。
            stream_callback: 可选的流式回调函数。
            n: 返回的候选回答数量，默认为1。当n>1时，将返回多个消息。
            **kwargs: 传递给模型 generate 方法的其他参数。

        返回:
            当n=1时: 生成的助手消息（AssistantMessage）。
            当n>1时: 生成的助手消息列表（List[AssistantMessage]）。
        """
        messages_for_render = await self.get_messages()

        messages_for_api = await self.renderer_pipeline.render(
            messages_for_render,
            model=model,
            max_tokens=kwargs.get('max_tokens'),
            exclude_system=False 
        )

        formatted_tools = self.convert_tools(tools)

        model_response = await model.generate(
            messages_for_api,
            tools=formatted_tools, # Pass the formatted tools
            stream_callback=stream_callback,
            n=n,
            **kwargs
        )

        # 处理 n>1 的情况（多个结果）
        if n > 1:
            assistant_messages = []
            for response in model_response:
                final_tool_calls = None
                if hasattr(response, 'tool_calls') and response.tool_calls:
                    final_tool_calls = []
                    for tc in response.tool_calls:
                        if hasattr(tc, 'id') and hasattr(tc, 'function'):
                            func = ToolCallFunction(name=getattr(tc.function, 'name', ''), arguments=getattr(tc.function, 'arguments', ''))
                            final_tool_calls.append(ToolCall(id=tc.id, function=func))

                response_content = getattr(response, 'content', None)
                assistant_msg = AssistantMessage(content=response_content, tool_calls=final_tool_calls)
                assistant_messages.append(assistant_msg)
            
            return assistant_messages
        
        # 以下是 n=1 的原始逻辑
        final_tool_calls = None
        if hasattr(model_response, 'tool_calls') and model_response.tool_calls:
            final_tool_calls = []
            for tc in model_response.tool_calls:
                if hasattr(tc, 'id') and hasattr(tc, 'function'):
                    func = ToolCallFunction(name=getattr(tc.function, 'name', ''), arguments=getattr(tc.function, 'arguments', ''))
                    final_tool_calls.append(ToolCall(id=tc.id, function=func))

        response_content = getattr(model_response, 'content', None)
        assistant_msg = AssistantMessage(content=response_content, tool_calls=final_tool_calls)
            
        return assistant_msg

    async def count_tokens(self, model: OpenAIModel) -> int:
        """
        计算当前历史消息使用指定模型所需的 token 数。
        内部调用渲染管道编译消息（不进行裁剪）。
        """
        messages_for_render = await self.get_messages()
        messages_for_api: List[Dict[str, Any]] = await self.renderer_pipeline.render(
            messages_for_render,
            model=model,
            exclude_system=False 
        )
        return model.count_tokens(messages_for_api)

    # === 摘要生成后台任务 ===

    def start_summary_generation_task(self, model: OpenAIModel):
        """
        启动后台任务，为没有摘要的消息生成摘要

        参数:
            model: 用于生成摘要的模型
        """
        if self._summary_task and not self._summary_task.done():
            logger.info("摘要生成任务已在运行中")
            return

        self._summary_task = asyncio.create_task(self._generate_summaries_for_messages(model))
        logger.info("已启动摘要生成后台任务")

    async def _generate_summaries_for_messages(self, model: OpenAIModel):
        """
        为数据库中没有摘要的消息生成摘要的后台任务。

        参数:
            model: 用于生成摘要的模型
        """
        if not self.conversation_id:
            logger.info("摘要生成：conversation_id 未设置，跳过")
            return
        
        # 检查 History 是否处于内存模式
        history_manager = self.get_history_manager()
        if hasattr(history_manager, '_is_memory_mode') and history_manager._is_memory_mode:
            logger.info(f"摘要生成：History 处于内存模式 (conversation_id: {self.conversation_id})，跳过摘要生成。")
            return

        try:
            logger.info(f"开始为对话 {self.conversation_id} 生成缺失的摘要")
            # 直接从 history 对象（即数据库）获取需要摘要的消息的 ID 和内容
            # History 类没有直接提供此功能，需要通过其 _get_db_collection 访问
            history_manager = self.get_history_manager()
            db_collection = await history_manager._get_db_collection() # Accessing protected member for this specific task
            
            if not db_collection: # 如果是内存模式，或者获取collection失败
                logger.warning(f"摘要生成：无法获取数据库集合 (conversation_id: {self.conversation_id})，跳过摘要生成。")
                return

            messages_to_summarize_cursor = db_collection.find(
                {"conversation_id": self.conversation_id, "summary": {"$in": [None, ""]}},
                {"_id": 1, "content": 1} # 只获取 ID 和 content
            )
            
            count = 0
            async for msg_doc in messages_to_summarize_cursor:
                message_id_str = str(msg_doc.get("_id"))
                content_to_summarize = msg_doc.get("content")

                if not content_to_summarize or not message_id_str:
                    continue

                system_prompt = "你是一个专门负责生成消息摘要的助手。请为以下消息生成一个简短的摘要（不超过20个字）。摘要应该简明扼要地概括消息的主要内容。"
                prompt_messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"消息内容: {content_to_summarize}\n\n请生成摘要:"}
                ]

                try:
                    response = await model.generate(prompt_messages) # Assuming model.generate can take list of dicts
                    summary = response.content.strip() if hasattr(response, 'content') and response.content else ""

                    if len(summary) > 50:
                        summary = summary[:47] + "..."

                    if summary:
                        # 使用 history manager 更新摘要
                        success = await history_manager.update_message_summary(message_id_str, summary)
                        if success:
                            logger.debug(f"为消息 {message_id_str} 生成并更新摘要: {summary}")
                            count += 1
                        else:
                            logger.warning(f"为消息 {message_id_str} 更新摘要失败")
                except Exception as e_generate:
                    logger.error(f"为消息 {message_id_str} 生成摘要时出错: {e_generate}")
                
                await asyncio.sleep(0.2) # 避免API请求过于频繁

            if count > 0:
                logger.info(f"为 {count} 条消息成功生成并更新了摘要 (对话: {self.conversation_id}) 。")
            else:
                logger.info(f"对话 {self.conversation_id} 中没有需要生成摘要的消息，或生成失败。")

        except Exception as e_task:
            logger.error(f"摘要生成后台任务出错 (对话: {self.conversation_id}): {e_task}", show_traceback=True)
        finally:
            self._summary_task = None # 标记任务完成或出错

    def is_summary_task_running(self) -> bool:
        """
        检查摘要生成任务是否正在运行

        返回:
            布尔值，表示任务是否正在运行
        """
        return self._summary_task is not None and not self._summary_task.done()

    async def sync_history_from_api(self, messages_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从API同步历史记录，将前端发送的完整消息列表与后端同步。
        使用 Message.from_dict() 进行反序列化。

        参数:
            messages_data: 来自API的消息字典列表

        返回:
            同步后的可序列化消息列表
        """
        message_objects = [Message.from_dict(msg_data) for msg_data in messages_data]
        await self.history.replace_all_messages(message_objects)
        return await self.get_serializable_messages()
