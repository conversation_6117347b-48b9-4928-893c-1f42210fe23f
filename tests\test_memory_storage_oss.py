import pytest
import pytest_asyncio
import uuid
import asyncio
import os

from libraries.memory_storage_oss import OSSStorage
from libraries.memory_storage import NodeType # NodeType for type hinting
import config as config # To access OSS config for checks

# 标记所有测试为异步测试
pytestmark = pytest.mark.asyncio

# 全局变量，用于在测试之间共享OSSStorage实例，以减少初始化开销
# 但每个测试方法仍应使用独立的conversation_id来隔离数据
_oss_storage_instance: OSSStorage = None

async def get_oss_storage_instance() -> OSSStorage:
    """获取或创建OSSStorage的单例。"""
    global _oss_storage_instance
    if _oss_storage_instance is None:
        # 检查配置是否足以运行测试
        if not all([config.OSS_ENDPOINT, config.OSS_BUCKET_NAME, config.OSS_ACCESS_KEY_ID, config.OSS_ACCESS_KEY_SECRET, config.OSS_REGION]):
            pytest.skip("OSS 配置不完整，跳过真实的OSS集成测试。请检查 .env 文件中的 OSS_* 和 OSS_REGION 设置。")
        
        _oss_storage_instance = OSSStorage()
        await _oss_storage_instance.initialize()
    return _oss_storage_instance

@pytest_asyncio.fixture(scope="module")
async def oss_storage() -> OSSStorage:
    """模块级别的fixture，提供OSSStorage实例。"""
    storage = await get_oss_storage_instance()
    return storage

@pytest_asyncio.fixture
async def test_conversation_id(oss_storage: OSSStorage):
    """为每个测试创建一个唯一的conversation_id，并在测试后清理相关数据。"""
    conv_id = f"test_oss_storage_{uuid.uuid4().hex[:12]}"
    # 基本路径前缀，用于此测试会话的所有操作
    base_test_path_prefix = f"/{conv_id}/" 

    yield conv_id # 提供 conversation_id 给测试函数

    # --- 清理阶段 ---
    # 测试结束后，删除以此 conversation_id 创建的所有内容
    # 注意：OSS的文件夹是概念性的，删除其下所有对象即可
    # 我们将尝试删除整个 <prefix><conversation_id>/ 目录
    try:
        full_prefix_to_delete = oss_storage.prefix + conv_id + "/" # 这是OSS中要删除的前缀
        
        # 构造一个 NodeType 来代表要删除的根"文件夹"
        # 传递给 delete_node_recursive 的 path 参数应该是相对于用户根的
        # 例如，如果用户根在 "memory/" 下，而 conv_id 是 "test123",
        # 那么要删除的路径是 "/test123/" (相对于 "memory/")
        path_to_delete_for_storage_method = f"/{conv_id}/"

        # 先列出所有对象以确认，或者直接尝试删除
        # print(f"清理：准备删除路径 {path_to_delete_for_storage_method} (OSS前缀: {full_prefix_to_delete}) 下的所有对象...")

        # 在调用 delete_node_recursive 之前，确保该"文件夹"（或其标记）确实存在，以避免不必要的错误日志
        # 注意：这里的path_exists的path参数也是相对于用户根的
        node_exists = await oss_storage.path_exists(conv_id, "/") # 检查 conv_id 下是否有任何东西
        if node_exists:
            # print(f"清理：路径 {path_to_delete_for_storage_method} 存在，开始删除...")
            success = await oss_storage.delete_node_recursive(conv_id, path_to_delete_for_storage_method)
            if success:
                print(f"清理：成功删除测试数据，路径前缀: {full_prefix_to_delete}")
            else:
                print(f"警告：清理测试数据失败，路径前缀: {full_prefix_to_delete}。可能需要手动清理。")
        else:
            print(f"清理：路径 {path_to_delete_for_storage_method} 未找到或已为空，无需删除。")

    except Exception as e:
        print(f"警告：清理测试数据时发生错误 (路径前缀: {full_prefix_to_delete}): {e}。可能需要手动清理。")


# --- 测试用例 ---

async def test_oss_storage_initialization(oss_storage: OSSStorage):
    """测试OSSStorage是否可以成功初始化。"""
    assert oss_storage is not None
    assert oss_storage.client is not None
    assert oss_storage.bucket_name == config.OSS_BUCKET_NAME

async def test_create_and_read_file_node(oss_storage: OSSStorage, test_conversation_id: str):
    """测试创建文件节点并读取其内容。"""
    file_path = "/test_file.txt"
    file_content = "Hello, OSS!\nThis is a test file."
    
    # 创建文件
    created_node = await oss_storage.create_node(test_conversation_id, file_path, "file", file_content)
    assert created_node is not None
    assert created_node["name"] == "test_file.txt"
    assert created_node["path"] == file_path
    assert created_node["node_type"] == "file"
    assert created_node["conversation_id"] == test_conversation_id
    assert created_node["updated_at"] is not None

    # 读取文件内容
    read_content = await oss_storage.read_file_content(test_conversation_id, file_path)
    assert read_content == file_content

    # 再次获取节点信息进行验证
    fetched_node = await oss_storage.get_node_by_path(test_conversation_id, file_path)
    assert fetched_node is not None
    assert fetched_node["name"] == "test_file.txt"
    assert fetched_node["updated_at"] is not None


async def test_create_folder_node(oss_storage: OSSStorage, test_conversation_id: str):
    """测试创建文件夹节点。"""
    folder_path = "/my_test_folder/" # 文件夹路径应以 / 结尾
    
    created_node = await oss_storage.create_node(test_conversation_id, folder_path, "folder")
    assert created_node is not None
    assert created_node["name"] == "my_test_folder" # 名称不包含尾部斜杠
    assert created_node["path"] == folder_path # 规范化路径，get_node_by_path 可能返回不带尾部斜杠的
    assert created_node["node_type"] == "folder"
    assert created_node["conversation_id"] == test_conversation_id
    # updated_at 对于新创建的0字节文件夹标记对象应该存在
    assert created_node["updated_at"] is not None 

    # 验证文件夹是否存在
    fetched_node = await oss_storage.get_node_by_path(test_conversation_id, folder_path)
    assert fetched_node is not None
    assert fetched_node["node_type"] == "folder"

async def test_update_file_content(oss_storage: OSSStorage, test_conversation_id: str):
    """测试更新文件节点的内容。"""
    file_path = "/updatable_file.txt"
    initial_content = "Initial content."
    updated_content = "Updated content here!"

    # 1. 创建初始文件
    await oss_storage.create_node(test_conversation_id, file_path, "file", initial_content)
    
    # 2. 更新内容
    updated_node = await oss_storage.update_node_content(test_conversation_id, file_path, updated_content)
    assert updated_node is not None
    assert updated_node["name"] == "updatable_file.txt"
    
    # 3. 验证内容是否已更新
    read_back_content = await oss_storage.read_file_content(test_conversation_id, file_path)
    assert read_back_content == updated_content

async def test_list_children_of_node(oss_storage: OSSStorage, test_conversation_id: str):
    """测试列出父节点下的子节点。"""
    parent_folder_path = "/parent_dir/"
    file1_name = "child_file1.md"
    file2_name = "child_file2.log"
    subfolder_name = "child_subdir" # 不带尾部斜杠

    # 1. 创建结构
    await oss_storage.create_node(test_conversation_id, parent_folder_path, "folder")
    await oss_storage.create_node(test_conversation_id, f"{parent_folder_path}{file1_name}", "file", "content1")
    await oss_storage.create_node(test_conversation_id, f"{parent_folder_path}{file2_name}", "file", "content2")
    await oss_storage.create_node(test_conversation_id, f"{parent_folder_path}{subfolder_name}/", "folder") # 创建子文件夹

    # 2. 列出根目录下的子节点 (应该只有 parent_dir)
    root_children = await oss_storage.get_children_of_node(test_conversation_id, "/")
    assert len(root_children) == 1
    assert root_children[0]["name"] == "parent_dir"
    assert root_children[0]["node_type"] == "folder"

    # 3. 列出 parent_folder_path 下的子节点
    children = await oss_storage.get_children_of_node(test_conversation_id, parent_folder_path)
    assert len(children) == 3 # 两个文件，一个子文件夹
    
    child_names = sorted([child["name"] for child in children])
    expected_names = sorted([file1_name, file2_name, subfolder_name])
    assert child_names == expected_names

    # 验证类型
    for child in children:
        if child["name"] == subfolder_name:
            assert child["node_type"] == "folder"
        else:
            assert child["node_type"] == "file"

async def test_delete_file_node(oss_storage: OSSStorage, test_conversation_id: str):
    """测试删除文件节点。"""
    file_to_delete_path = "/deletable.txt"
    await oss_storage.create_node(test_conversation_id, file_to_delete_path, "file", "delete me")
    
    assert await oss_storage.path_exists(test_conversation_id, file_to_delete_path) == True
    
    delete_success = await oss_storage.delete_node_recursive(test_conversation_id, file_to_delete_path)
    assert delete_success == True
    
    assert await oss_storage.path_exists(test_conversation_id, file_to_delete_path) == False
    assert await oss_storage.get_node_by_path(test_conversation_id, file_to_delete_path) is None

async def test_delete_folder_node_recursive(oss_storage: OSSStorage, test_conversation_id: str):
    """测试递归删除文件夹及其内容。"""
    base_folder = "/folder_to_nuke/"
    sub_file1 = f"{base_folder}file1.txt"
    sub_folder = f"{base_folder}subnuke/"
    sub_sub_file2 = f"{sub_folder}file2.txt"

    # 1. 创建结构
    await oss_storage.create_node(test_conversation_id, base_folder, "folder")
    await oss_storage.create_node(test_conversation_id, sub_file1, "file", "1")
    await oss_storage.create_node(test_conversation_id, sub_folder, "folder")
    await oss_storage.create_node(test_conversation_id, sub_sub_file2, "file", "2")

    # 确认存在
    assert await oss_storage.path_exists(test_conversation_id, base_folder)
    assert await oss_storage.path_exists(test_conversation_id, sub_file1)
    assert await oss_storage.path_exists(test_conversation_id, sub_folder)
    assert await oss_storage.path_exists(test_conversation_id, sub_sub_file2)

    # 2. 递归删除
    delete_success = await oss_storage.delete_node_recursive(test_conversation_id, base_folder)
    assert delete_success == True

    # 3. 验证都已删除
    assert await oss_storage.path_exists(test_conversation_id, base_folder) == False
    assert await oss_storage.get_node_by_path(test_conversation_id, base_folder) is None # 显式检查文件夹节点
    assert await oss_storage.path_exists(test_conversation_id, sub_file1) == False
    assert await oss_storage.path_exists(test_conversation_id, sub_folder) == False
    assert await oss_storage.path_exists(test_conversation_id, sub_sub_file2) == False
    
    # 尝试列出子项，应该为空
    children_after_delete = await oss_storage.get_children_of_node(test_conversation_id, base_folder)
    assert len(children_after_delete) == 0


async def test_path_exists(oss_storage: OSSStorage, test_conversation_id: str):
    """测试路径存在性检查。"""
    existing_file = "/iamhere.txt"
    existing_folder = "/folder_here/"
    non_existing_path = "/nothere.dat"

    await oss_storage.create_node(test_conversation_id, existing_file, "file", "exists")
    await oss_storage.create_node(test_conversation_id, existing_folder, "folder")

    assert await oss_storage.path_exists(test_conversation_id, existing_file) == True
    assert await oss_storage.path_exists(test_conversation_id, existing_folder) == True
    assert await oss_storage.path_exists(test_conversation_id, non_existing_path) == False
    # 测试一个深层不存在但父文件夹存在的路径
    assert await oss_storage.path_exists(test_conversation_id, f"{existing_folder}sub_not_here.txt") == False


async def test_search_nodes_by_name(oss_storage: OSSStorage, test_conversation_id: str):
    """测试按名称搜索节点。"""
    # 创建测试结构
    await oss_storage.create_node(test_conversation_id, "/search_root/file_apple.txt", "file", "Apple text")
    await oss_storage.create_node(test_conversation_id, "/search_root/folder_apple/", "folder")
    await oss_storage.create_node(test_conversation_id, "/search_root/folder_banana/sub_file_apple.log", "file", "Sub Apple log")
    await oss_storage.create_node(test_conversation_id, "/search_root/another_file.md", "file", "other")

    # 1. 在根目录下递归搜索 "apple"
    results_root_recursive = await oss_storage.search_nodes_by_name(test_conversation_id, "apple", parent_path="/search_root/", recursive=True)
    assert len(results_root_recursive) == 3 # file_apple.txt, folder_apple, sub_file_apple.log
    names = sorted([r["name"] for r in results_root_recursive])
    assert names == sorted(["file_apple.txt", "folder_apple", "sub_file_apple.log"])

    # 2. 在根目录下非递归搜索 "apple"
    results_root_non_recursive = await oss_storage.search_nodes_by_name(test_conversation_id, "apple", parent_path="/search_root/", recursive=False)
    assert len(results_root_non_recursive) == 2 # file_apple.txt, folder_apple
    names_non_rec = sorted([r["name"] for r in results_root_non_recursive])
    assert names_non_rec == sorted(["file_apple.txt", "folder_apple"])
    
    # 3. 在特定子文件夹下搜索
    results_subfolder = await oss_storage.search_nodes_by_name(test_conversation_id, "apple", parent_path="/search_root/folder_banana/", recursive=True)
    assert len(results_subfolder) == 1
    assert results_subfolder[0]["name"] == "sub_file_apple.log"
    
    # 4. 搜索不存在的名称
    results_notfound = await oss_storage.search_nodes_by_name(test_conversation_id, "grape", parent_path="/search_root/")
    assert len(results_notfound) == 0

async def test_search_file_contents(oss_storage: OSSStorage, test_conversation_id: str):
    """测试按文件内容搜索。"""
    # 创建测试文件
    content1 = "The quick brown fox jumps over the lazy dog."
    content2 = "A lazy elephant and a quick ant."
    content3 = "No relevant keywords here."
    content_sub = "Deep inside, the fox sleeps."

    await oss_storage.create_node(test_conversation_id, "/content_search/doc1.txt", "file", content1)
    await oss_storage.create_node(test_conversation_id, "/content_search/doc2.md", "file", content2)
    await oss_storage.create_node(test_conversation_id, "/content_search/other.log", "file", content3)
    await oss_storage.create_node(test_conversation_id, "/content_search/sub_dir/doc_sub.txt", "file", content_sub)

    # 1. 递归搜索 "fox"
    results_fox_recursive = await oss_storage.search_file_contents(test_conversation_id, "fox", parent_path="/content_search/", recursive=True)
    assert len(results_fox_recursive) == 2 # doc1.txt, doc_sub.txt
    paths_fox = sorted([r["path"] for r in results_fox_recursive])
    assert paths_fox == sorted(["/content_search/doc1.txt", "/content_search/sub_dir/doc_sub.txt"])

    # 2. 非递归搜索 "lazy"
    results_lazy_non_recursive = await oss_storage.search_file_contents(test_conversation_id, "lazy", parent_path="/content_search/", recursive=False)
    assert len(results_lazy_non_recursive) == 2 # doc1.txt, doc2.md (doc_sub.txt 不在直接子级)
    paths_lazy = sorted([r["path"] for r in results_lazy_non_recursive])
    assert paths_lazy == sorted(["/content_search/doc1.txt", "/content_search/doc2.md"])


    # 3. 搜索 "quick" 在特定子文件夹 (应该找不到)
    results_quick_sub = await oss_storage.search_file_contents(test_conversation_id, "quick", parent_path="/content_search/sub_dir/", recursive=True)
    assert len(results_quick_sub) == 0 # "fox" 在子文件夹，但 "quick" 不在

    # 4. 搜索不存在的内容
    results_notfound = await oss_storage.search_file_contents(test_conversation_id, "nonexistentXYZ", parent_path="/content_search/")
    assert len(results_notfound) == 0

async def test_empty_folder_operations(oss_storage: OSSStorage, test_conversation_id: str):
    """测试对空文件夹的操作。"""
    empty_folder_path = "/empty_test_folder/"
    
    # 1. 创建空文件夹
    await oss_storage.create_node(test_conversation_id, empty_folder_path, "folder")
    assert await oss_storage.path_exists(test_conversation_id, empty_folder_path) == True
    
    # 2. 列出其子节点，应该为空
    children = await oss_storage.get_children_of_node(test_conversation_id, empty_folder_path)
    assert len(children) == 0
    
    # 3. 删除空文件夹
    delete_success = await oss_storage.delete_node_recursive(test_conversation_id, empty_folder_path)
    assert delete_success == True
    assert await oss_storage.path_exists(test_conversation_id, empty_folder_path) == False

async def test_operations_on_root(oss_storage: OSSStorage, test_conversation_id: str):
    """测试直接在用户根目录下的操作。"""
    root_file_path = "/root_file.txt"
    root_folder_path = "/root_folder/"

    # 1. 在根目录创建文件和文件夹
    await oss_storage.create_node(test_conversation_id, root_file_path, "file", "root content")
    await oss_storage.create_node(test_conversation_id, root_folder_path, "folder")

    assert await oss_storage.path_exists(test_conversation_id, root_file_path) == True
    assert await oss_storage.path_exists(test_conversation_id, root_folder_path) == True

    # 2. 列出根目录下的子节点
    # 使用 None 或 "/" 作为 parent_path 来表示根
    children_at_root = await oss_storage.get_children_of_node(test_conversation_id, None)
    assert len(children_at_root) >= 2 # 可能有其他测试残留的顶级文件夹，所以用 >=
    names_at_root = {child["name"] for child in children_at_root}
    assert "root_file.txt" in names_at_root
    assert "root_folder" in names_at_root

    # 3. 读取根目录文件
    content = await oss_storage.read_file_content(test_conversation_id, root_file_path)
    assert content == "root content"

    # 清理将由 test_conversation_id fixture 处理，它会删除整个 /<conv_id>/ 结构

async def test_error_handling_non_existent_read(oss_storage: OSSStorage, test_conversation_id: str):
    """测试读取不存在的文件。"""
    non_existent_file = "/i_do_not_exist.txt"
    content = await oss_storage.read_file_content(test_conversation_id, non_existent_file)
    assert content is None

async def test_error_handling_update_non_existent(oss_storage: OSSStorage, test_conversation_id: str):
    """测试更新不存在的文件。"""
    # 根据当前实现，update_node_content 如果文件不存在，会创建它（因为put_object会覆盖或创建）
    # 如果期望的行为是更新失败，则OSSStorage中的update_node_content需要先检查是否存在
    # 目前，它会成功创建。
    file_path = "/ghost_file.txt"
    updated_node = await oss_storage.update_node_content(test_conversation_id, file_path, "new ghost content")
    assert updated_node is not None # 验证它被创建了
    assert updated_node["name"] == "ghost_file.txt"
    read_back = await oss_storage.read_file_content(test_conversation_id, file_path)
    assert read_back == "new ghost content"

async def test_delete_non_existent_node(oss_storage: OSSStorage, test_conversation_id: str):
    """测试删除不存在的节点。"""
    # delete_node_recursive 对于不存在的文件，底层 delete_object 通常会静默成功或返回特定状态
    # V2 SDK delete_object 在对象不存在时不会抛错
    success_file = await oss_storage.delete_node_recursive(test_conversation_id, "/no_such_file.txt")
    assert success_file == True # OSS delete_object 不存在时通常不报错

    # 对于不存在的文件夹前缀，delete_node_recursive 会列举对象，如果列表为空，也应返回True
    success_folder = await oss_storage.delete_node_recursive(test_conversation_id, "/no_such_folder/")
    assert success_folder == True

# 注意：重命名和移动操作的测试已省略，因为它们在 OSSStorage 中标记为 NotImplementedError
# 如果实现了这些功能，应添加相应的测试用例。 