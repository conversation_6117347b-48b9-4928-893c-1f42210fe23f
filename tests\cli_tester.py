import asyncio
import re
import traceback
from collections import defaultdict
from typing import Dict, List, Any, Optional, Pattern, Union
import json

# 导入项目代码
from agent_manager import AgentManager
from connection import ConnectionPair, Connection
from conversation import Conversation
from message import SystemMessage
import config
# 导入日志记录器
from log import logger 
from utils import dumps

class TestMessageHandler:
    """
    测试专用的消息处理器，将收到的消息存储在内存中以供后续断言。
    """
    def __init__(self):
        """初始化消息存储。"""
        # 使用 defaultdict(asyncio.Queue) 存储每个用户的消息队列
        # Queue 可以更好地处理异步消息的接收和等待
        self._messages_by_user: Dict[str, asyncio.Queue] = defaultdict(asyncio.Queue)
        # 也保留一个列表副本，方便查看所有历史消息（可选）
        self._all_messages_by_user: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    async def handle_message(self, message: Dict[str, Any]):
        """
        处理从 Agent 收到的消息，将其放入对应用户的队列中。

        参数:
            message: 从 Agent 收到的消息字典。
        """
        conversation_id = message.get("conversation_id")
        if conversation_id:
            # 将消息放入队列
            await self._messages_by_user[conversation_id].put(message)
            # 同时添加到列表副本（可选）
            self._all_messages_by_user[conversation_id].append(message)
        else:
            # 处理没有 conversation_id 的消息（例如系统级错误）
            # 可以决定如何处理，例如放入一个特殊的 "system" 队列
            system_queue = self._messages_by_user["_system"]
            await system_queue.put(message)
            self._all_messages_by_user["_system"].append(message)

    def get_message_queue(self, conversation_id: str) -> Optional[asyncio.Queue]:
        """
        获取指定用户的消息队列。

        参数:
            conversation_id: 对话ID。

        返回:
            对应用户的 asyncio.Queue，如果用户不存在则返回 None。
        """
        return self._messages_by_user.get(conversation_id)

    def get_all_received_messages(self, conversation_id: str) -> List[Dict[str, Any]]:
        """
        获取指定用户收到的所有消息列表（用于调试或查看历史）。

        参数:
            conversation_id: 对话ID。

        返回:
            该用户收到的所有消息列表。
        """
        return list(self._all_messages_by_user.get(conversation_id, []))

    def clear_messages(self, conversation_id: Optional[str] = None):
        """
        清除指定用户或所有用户的消息记录。

        参数:
            conversation_id: 如果提供，则只清除该用户的消息；否则清除所有用户的消息。
        """
        if conversation_id:
            if conversation_id in self._messages_by_user:
                # 清空队列 (创建一个新队列)
                self._messages_by_user[conversation_id] = asyncio.Queue()
            if conversation_id in self._all_messages_by_user:
                del self._all_messages_by_user[conversation_id]
        else:
            self._messages_by_user.clear()
            self._all_messages_by_user.clear()

class CLITester:
    """
    用于 E2E 测试的辅助类，模拟 CLI 用户交互。
    """
    def __init__(self, agent_manager: AgentManager):
        """初始化 CLITester。"""
        if not agent_manager:
            # 使用 logger 记录错误
            logger.error("AgentManager 实例必须提供")
            raise ValueError("AgentManager 实例必须提供")
        self.agent_manager = agent_manager
        self.message_handler = TestMessageHandler()
        self._connections: Dict[str, ConnectionPair] = {}
        self._agents: Dict[str, Any] = {} # 存储 Agent 实例
        self._message_tasks: Dict[str, asyncio.Task] = {}
        logger.info("CLITester 初始化完成。") # 添加初始化日志

    async def _process_messages(self, conversation_id: str, client_conn: Connection):
        """
        内部任务，持续从客户端连接接收消息并交给 TestMessageHandler。
        """
        # logger.debug(f"用户 {conversation_id} 的消息处理任务已启动。") # 可以取消注释以获得更详细的调试日志
        while True:
            try:
                message = await client_conn.recv()
                if message:
                    # logger.debug(f"[{conversation_id}] CLITester 收到原始消息: {message}") # 详细调试
                    await self.message_handler.handle_message(message)
                # await asyncio.sleep(0.01) # 移除不必要的 sleep
            except asyncio.CancelledError:
                logger.debug(f"用户 {conversation_id} 的消息处理任务已取消。")
                break
            except Exception as e:
                logger.error(f"处理用户 {conversation_id} 消息时发生错误: {traceback.format_exc()}")
                error_msg = {
                    "type": "tester_error",
                    "content": f"处理消息时发生内部错误: {e}",
                    "conversation_id": conversation_id
                }
                await self.message_handler.handle_message(error_msg)
                break

    async def start_session(self, conversation_id: str, initial_system_message: Optional[str] = None, agent_config: Optional[Dict[str, Any]] = None):
        """
        为指定用户启动一个新的测试会话。
        """
        logger.info(f"用户 [{conversation_id}]: 正在启动测试会话...")
        if conversation_id in self._agents:
            error_msg = f"用户 {conversation_id} 已存在活动会话。"
            logger.error(error_msg)
            raise ValueError(error_msg)

        connection_pair = ConnectionPair()
        client_conn, agent_conn = connection_pair.get_connection()
        self._connections[conversation_id] = connection_pair
        logger.debug(f"用户 [{conversation_id}]: 连接对已创建。")

        conversation = Conversation(conversation_id)
        system_msg_content = initial_system_message if initial_system_message is not None else config.SYSTEM_MESSAGE
        system_message = SystemMessage(system_msg_content)
        await conversation.add_message(system_message)
        logger.debug(f"用户 [{conversation_id}]: Conversation 已创建，系统消息已添加。")

        try:
            agent = await self.agent_manager.create_agent(
                conversation_id=conversation_id, 
                conversation=conversation, 
                output_connection=agent_conn,
                agent_config=agent_config,
            )
            self._agents[conversation_id] = agent
            logger.debug(f"用户 [{conversation_id}]: Agent 实例已创建。")
        except Exception as e:
            await connection_pair.close()
            del self._connections[conversation_id]
            logger.error(f"用户 [{conversation_id}]: 创建 Agent 失败: {traceback.format_exc()}")
            raise RuntimeError(f"为用户 {conversation_id} 创建 Agent 失败: {e}") from e

        task = asyncio.create_task(self._process_messages(conversation_id, client_conn))
        self._message_tasks[conversation_id] = task
        logger.info(f"用户 [{conversation_id}]: 测试会话已成功启动。")

    async def close_session(self, conversation_id: str):
        """
        关闭指定用户的测试会话并清理资源。
        """
        logger.info(f"用户 [{conversation_id}]: 正在关闭测试会话...")
        if conversation_id not in self._agents:
            logger.warning(f"尝试关闭不存在的会话: {conversation_id}")
            return

        agent = self._agents.pop(conversation_id, None)
        if agent:
            try:
                await agent.close()
                logger.debug(f"用户 [{conversation_id}]: Agent 已关闭。")
            except Exception as e:
                logger.error(f"关闭 Agent {conversation_id} 时出错: {e}")
        
        task = self._message_tasks.pop(conversation_id, None)
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.debug(f"用户 [{conversation_id}]: 消息处理任务已成功取消。")
            except Exception as e:
                logger.error(f"等待消息任务 {conversation_id} 取消时出错: {e}")

        connection_pair = self._connections.pop(conversation_id, None)
        if connection_pair:
            await connection_pair.close()
            logger.debug(f"用户 [{conversation_id}]: 连接对已关闭。")

        self.message_handler.clear_messages(conversation_id)
        logger.info(f"用户 [{conversation_id}]: 测试会话已关闭并清理。")

    async def close(self):
        """
        关闭所有活动的测试会话。
        """
        logger.info("正在关闭所有 CLITester 会话...")
        conversation_ids = list(self._agents.keys())
        close_tasks = [self.close_session(uid) for uid in conversation_ids]
        # 使用 asyncio.gather 并发关闭，记录可能出现的异常
        results = await asyncio.gather(*close_tasks, return_exceptions=True)
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"关闭会话 {conversation_ids[i]} 时发生未捕获错误: {result}")
        logger.info("所有 CLITester 会话已关闭。")

    async def send_input(self, conversation_id: str, text: str, stream_mode: bool = False):
        """
        向指定用户的 Agent 发送用户输入。
        """
        logger.info(f"用户 [{conversation_id}]: 发送输入 -> '{text}'")
        if conversation_id not in self._agents:
            error_msg = f"用户 {conversation_id} 没有活动会话。"
            logger.error(error_msg)
            raise ValueError(error_msg)

        agent = self._agents[conversation_id]
        message_to_send = {
            "type": "user_input",
            "conversation_id": conversation_id,
            "content": text,
            "stream_mode": stream_mode
        }
        try:
            await agent.handle_incoming_message(message_to_send)
        except Exception as e:
            logger.error(f"向 Agent {conversation_id} 发送输入 '{text}' 时出错: {traceback.format_exc()}")
            raise RuntimeError(f"向 Agent {conversation_id} 发送输入失败") from e

    async def expect_output(
        self,
        conversation_id: str,
        pattern: Union[str, Pattern[str]],
        message_type: Optional[str] = None,
        timeout: float = 10.0
    ) -> Dict[str, Any]:
        """
        等待并断言指定用户收到符合条件的消息。
        """
        logger.info(f"用户 [{conversation_id}]: 期望收到 类型='{message_type or '任何'}' 内容匹配='{pattern}' (超时={timeout}s)")
        if conversation_id not in self._agents:
            error_msg = f"用户 {conversation_id} 没有活动会话。"
            logger.error(error_msg)
            raise ValueError(error_msg)

        message_queue = self.message_handler.get_message_queue(conversation_id)
        if not message_queue:
            error_msg = f"无法获取用户 {conversation_id} 的消息队列。"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        regex = re.compile(pattern) if isinstance(pattern, str) else pattern
        start_time = asyncio.get_event_loop().time()

        accumulated_content = ""
        is_streaming_assistant = False

        while True:
            remaining_time = timeout - (asyncio.get_event_loop().time() - start_time)
            if remaining_time <= 0:
                error_detail = (
                    f"在 {timeout} 秒内未收到用户 '{conversation_id}' 的类型为 '{message_type or 'any'}' "
                    f"且内容匹配 '{pattern}' 的消息。"
                    f"\n已累积内容(助手流): '{accumulated_content}'"
                    f"\n期间收到的所有消息: {dumps(self.message_handler.get_all_received_messages(conversation_id))}" # 使用 dumps
                )
                logger.error(f"用户 [{conversation_id}]: 等待消息超时。{error_detail}")
                raise TimeoutError(error_detail)

            try:
                message = await asyncio.wait_for(message_queue.get(), timeout=remaining_time)
                logger.debug(f"用户 [{conversation_id}]: 队列收到消息: {message}")
                msg_type = message.get("type")
                msg_content_data = message.get("content") 

                if message_type == "assistant":
                    if msg_type == "assistant_delta_start":
                        logger.debug(f"用户 [{conversation_id}]: 开始接收助手消息流...")
                        is_streaming_assistant = True
                        accumulated_content = ""
                        message_queue.task_done()
                        continue
                    elif msg_type == "assistant_delta" and is_streaming_assistant:
                        delta_content = msg_content_data if isinstance(msg_content_data, str) else ""
                        accumulated_content += delta_content
                        # logger.debug(f"用户 [{conversation_id}]: 累积助手消息: '{delta_content}'")
                        message_queue.task_done()
                        continue
                    elif msg_type == "assistant_delta_done" and is_streaming_assistant:
                        logger.debug(f"用户 [{conversation_id}]: 助手消息流接收完毕。完整内容: '{accumulated_content[:100]}...'")
                        is_streaming_assistant = False #    流结束
                        if regex.search(accumulated_content):
                            logger.info(f"用户 [{conversation_id}]: 找到匹配的助手消息流。")
                            synthetic_message = {
                                "type": "assistant", "content": accumulated_content,
                                "conversation_id": conversation_id, "reason": "从流中重建"
                            }
                            message_queue.task_done()
                            return synthetic_message
                        else:
                            logger.debug(f"用户 [{conversation_id}]: 累积的助手消息流内容不匹配模式 '{pattern}'. 重置并继续等待...")
                            accumulated_content = "" # 重置为空，等待下一个潜在的流/消息
                            message_queue.task_done()
                            continue
                    
                    # 4. 处理非流式消息，如果期望 "assistant"
                    # 这可能是完整的 assistant 消息 (type: "assistant" 或 type: "full_message_object")
                    current_message_is_assistant_type = False
                    assistant_content_to_check = None

                    if msg_type == "assistant": # 直接的，非流式的 assistant 消息
                        current_message_is_assistant_type = True
                        assistant_content_to_check = msg_content_data if isinstance(msg_content_data, str) else ""
                    
                    elif msg_type == "full_message_object":
                        try:
                            parsed_fmo_payload = json.loads(msg_content_data) if isinstance(msg_content_data, str) else msg_content_data
                            if isinstance(parsed_fmo_payload, dict) and parsed_fmo_payload.get("object") == "AssistantMessage":
                                current_message_is_assistant_type = True
                                assistant_content_to_check = parsed_fmo_payload.get("content", "")
                        except json.JSONDecodeError:
                            logger.warning(f"用户 [{conversation_id}]: full_message_object content解析JSON失败: {msg_content_data}")
                        except Exception as e_fmo:
                            logger.warning(f"用户 [{conversation_id}]: 解析full_message_object时出错: {e_fmo}, 内容: {msg_content_data}")

                    if current_message_is_assistant_type:
                        if regex.search(str(assistant_content_to_check)): # Убедиться, что контент это строка для regex
                            logger.info(f"用户 [{conversation_id}]: 找到匹配的助手消息 (非流式/FMO): {message}")
                            final_assistant_message = {
                                "type": "assistant",
                                "content": assistant_content_to_check,
                                "conversation_id": conversation_id,
                                "original_message_type": msg_type 
                            }
                            message_queue.task_done()
                            return final_assistant_message
                        else:
                            logger.debug(f"用户 [{conversation_id}]: 助手消息 (非流式/FMO) 内容 '{str(assistant_content_to_check)[:100]}...' 不匹配模式 '{pattern}'. 继续等待...")
                            message_queue.task_done()
                            continue 
                    
                    # 5. 如果 message_type "assistant", 但这是不是上面提到的 assistant 形式
                    #    并且我们处于流状态 (is_streaming_assistant), 这是一个意外的消息在流中。
                    elif is_streaming_assistant: 
                         logger.warning(f"用户 [{conversation_id}]: 在助手消息流中收到非预期的消息类型 '{msg_type}'. 忽略.")
                         message_queue.task_done()
                         continue
                    # 6. 如果 message_type "assistant", 不在流中, 并且不是上面提到的 assistant 形式:
                    else: 
                        logger.debug(f"用户 [{conversation_id}]: 收到消息类型 '{msg_type}', 但期望助手消息 (流式或完整). 忽略并继续等待.")
                        message_queue.task_done()
                        continue
                # --- 结束 message_type == "assistant" 的逻辑 ---
                
                # --- 处理其他类型的预期消息 (tool_call, tool_result 等) ---
                else: # message_type "assistant" (或 None, 即任何类型)
                    if message_type and msg_type != message_type: # 如果期望特定类型, 并且它不匹配
                        logger.debug(f"用户 [{conversation_id}]: 消息类型不匹配 (期望 '{message_type}', 收到 '{msg_type}'). 继续等待...")
                        message_queue.task_done()
                        continue
                    
                    content_to_check = ""
                    if msg_type in ["tool_call", "tool_result"]:
                        target_name = ""
                        if isinstance(msg_content_data, str): # Content 是 JSON 字符串
                            try:
                                parsed_json = json.loads(msg_content_data)
                                target_name = parsed_json.get("name", "")
                            except json.JSONDecodeError:
                                logger.warning(f"用户 [{conversation_id}]: {msg_type} content解析JSON失败: {msg_content_data}")
                                target_name = msg_content_data # 返回未处理的原始字符串, 如果 JSON 无效
                        elif isinstance(msg_content_data, dict): # Content 已经是 dict
                            target_name = msg_content_data.get("name", "")
                        elif msg_content_data is not None: # Content 是其他类型, 转换为字符串
                             target_name = str(msg_content_data)
                        content_to_check = target_name
                    elif isinstance(msg_content_data, str):
                        content_to_check = msg_content_data
                    elif isinstance(msg_content_data, dict):
                        try:
                            content_to_check = dumps(msg_content_data) # 序列化 dict 为字符串用于 regex
                        except TypeError:
                            content_to_check = str(msg_content_data) # 返回
                    elif msg_content_data is not None:
                        content_to_check = str(msg_content_data)
                    # 如果 msg_content_data None, content_to_check 保持为 ""

                    if regex.search(content_to_check):
                        logger.info(f"用户 [{conversation_id}]: 找到匹配消息: {message}")
                        message_queue.task_done()
                        return message # 返回原始消息
                    else:
                        logger.debug(f"用户 [{conversation_id}]: 消息内容 '{content_to_check[:100]}...' 不匹配模式 '{pattern}'. 继续等待...")
                        message_queue.task_done()
                        continue
            
            except asyncio.TimeoutError:
                # logger.debug(f"用户 [{conversation_id}]: 从队列获取消息超时 (内部捕获), 将在外层循环处理.")
                continue 
            except Exception as e:
                 logger.error(f"用户 [{conversation_id}]: 处理消息队列时发生意外错误: {traceback.format_exc()}")
                 message_queue.task_done() # 确保任务标记为已完成，以避免阻塞队列
                 raise # 重新抛出其他异常
                 
# # 移除 fixture 创建步骤提示 