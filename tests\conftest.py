import pytest
import pytest_asyncio # 确保安装了 pytest-asyncio
import asyncio
import logging
import sys # 需要导入 sys 来检查平台

# 导入需要测试的组件
from agent_manager import AgentManager
from tests.cli_tester import CLITester

# 确保 uvloop 在 Windows 上不被意外使用（如果安装了）
# if sys.platform == 'win32':
#     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def event_loop(request):
    """
    为整个测试会话创建一个事件循环实例。
    这可以防止在测试之间因事件循环关闭和重新创建而导致的问题，
    特别是对于那些缓存了事件循环引用的异步客户端（如 motor）。
    """
    # 在 Windows 和 Python 3.8+ 上，ProactorEventLoop 可能是默认的，
    # 有时 pytest-asyncio 与 ProactorEventLoop 的会话作用域循环存在兼容性问题。
    # 如果遇到问题，可以尝试强制使用 SelectorEventLoopPolicy。
    # 但通常 pytest-asyncio 会处理好，所以我们先用默认策略。
    # if sys.platform == 'win32' and sys.version_info >= (3, 8):
    #     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    loop = asyncio.get_event_loop_policy().new_event_loop()
    asyncio.set_event_loop(loop)
    logger.debug(f"Session-scoped event loop (ID: {id(loop)}) created and set.")

    yield loop

    logger.debug(f"Tearing down session-scoped event loop (ID: {id(loop)})...")
    # 清理：取消所有剩余的任务
    try:
        pending_tasks = asyncio.all_tasks(loop=loop)
        if pending_tasks:
            logger.info(f"Cancelling {len(pending_tasks)} pending tasks in session event loop (ID: {id(loop)}).")
            for task in pending_tasks:
                task.cancel()
            # 等待任务响应取消
            # loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
            # 在 loop 关闭前，给一小段时间让取消操作生效
            loop.run_until_complete(asyncio.sleep(0.1))


    except RuntimeError as e:
        logger.error(f"Error during task cancellation in session event_loop (ID: {id(loop)}) teardown: {e}")
    finally:
        if not loop.is_closed():
            logger.info(f"Closing session-scoped event loop (ID: {id(loop)}).")
            loop.close()
        else:
            logger.info(f"Session-scoped event loop (ID: {id(loop)}) was already closed.")


@pytest_asyncio.fixture(scope="session")
async def agent_manager(event_loop) -> AgentManager: # 明确依赖 event_loop
    """
    Pytest fixture，提供一个 session 范围的 AgentManager 实例。
    确保 AgentManager 在所有测试结束后正确关闭。
    """
    logger.debug(f"agent_manager fixture: Using event_loop (ID: {id(event_loop)}).")
    manager = await AgentManager.get_instance()
    yield manager
    # 在测试 session 结束后关闭 AgentManager
    logger.info("测试 Session 结束，正在关闭 AgentManager...")
    await manager.close()
    logger.info("AgentManager 已关闭。")

@pytest_asyncio.fixture
async def cli_tester(agent_manager: AgentManager, event_loop) -> CLITester: # 明确依赖 event_loop
    """
    Pytest fixture，提供一个函数范围的 CLITester 实例。
    它将在会话范围的事件循环中操作。
    确保每个测试使用独立的 CLITester 实例，并在测试结束后清理会话。
    """
    logger.debug(f"cli_tester fixture: Using event_loop (ID: {id(event_loop)}).")
    tester = CLITester(agent_manager)
    yield tester
    # 在每个测试函数结束后关闭 CLITester 中的所有活动会话
    active_agents_before_close = list(tester._agents.keys()) if hasattr(tester, '_agents') else []
    logger.info(f"测试函数结束，正在关闭 CLITester (管理中的 Agents: {active_agents_before_close})...")
    await tester.close()
    logger.info("CLITester 会话已关闭。") 