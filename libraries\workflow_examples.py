"""
【已废弃】
Deprecated
"""
from typing import Dict, List
import json
from utils import dumps
from libraries.workflow_models import WorkflowDefinition, ActionDefinition, ExecuteStep, WorkflowOperation

def generate_text_examples_from_structured(current_workflow_data: WorkflowDefinition, structured_examples: List[Dict], workflow_name: str) -> str:
    """
    根据结构化示例生成详细的文本示例

    参数:
        current_workflow_data: 工作流定义对象
        structured_examples: 结构化示例列表
        workflow_name: 工作流名称

    返回:
        生成的示例文本
    """
    if not structured_examples:
        return ""

    examples_text = f"**图灵完备工作流执行示例 (Few-Shot):**\n\n*假设当前工作流为 `{workflow_name}`*\n\n"

    for i, example in enumerate(structured_examples):
        title = example.get("title", f"示例 {i+1}")
        user_input = example.get("user_input", "")
        steps = example.get("steps", [])

        # 添加示例标题和用户输入
        examples_text += f"**{title}**\n\n"
        examples_text += f"1.  **用户输入:** \"{user_input}\"\n"

        # 处理每个步骤
        for j, step_data in enumerate(steps):
            step_name = step_data.get("step", "")
            step_obj = None

            # 查找步骤对象
            step_obj = current_workflow_data.get_step_by_index(step_name)
            if not step_obj:
                # 尝试按名称查找
                for s in current_workflow_data.get_parsed_steps():
                    if s.name == step_name:
                        step_obj = s
                        break

            if not step_obj:
                continue

            # 获取步骤属性
            step_index = step_obj.index
            step_name_display = step_obj.name or step_name
            step_operation = step_obj.operation

            # 获取步骤信息
            examples_text += f"2.  **(当前步骤: {step_index} - {step_name_display})**\n"

            # 不同操作类型的处理
            action = step_data.get("action", "")
            condition = step_data.get("condition", None)
            response = step_data.get("response", "")
            
            # 获取步骤属性
            next_step = getattr(step_obj, 'next', '?')
            true_branch = getattr(step_obj, 'true_branch', '?')
            false_branch = getattr(step_obj, 'false_branch', '?')
            wait_user = getattr(step_obj, 'wait_user', False)
            condition_description = getattr(step_obj, 'condition_description', '条件判断')

            if step_operation == WorkflowOperation.NOP:
                examples_text += f"      *   LLM 操作: NOP 步骤，前进。\n"
                examples_text += f"      *   LLM 工具调用: `workflow.next_step()`\n"
                examples_text += f"      *   (系统响应 -> 前进到步骤 {next_step})\n"

            elif step_operation == WorkflowOperation.EXECUTE:
                examples_text += f"      *   LLM 操作: 执行 `{action}` 动作。\n"
                # 根据动作类型生成参数
                examples_text += f"      *   LLM 工具调用: `{action}()`\n"
                examples_text += f"      *   (系统执行 {action.split('.')[-1]}, 返回结果 [], 并自动前进到步骤 {next_step})\n"

            elif step_operation == WorkflowOperation.CONDITION:
                condition_text = "True" if condition else "False"
                examples_text += f"      *   LLM 操作: 评估条件 \"{condition_description}\" (结果为 {condition_text})。\n"
                examples_text += f"      *   LLM 工具调用: `workflow.condition_branch(condition_result={str(condition).lower()})`\n"
                branch = true_branch if condition else false_branch
                examples_text += f"      *   (系统响应 -> 跳转到步骤 {branch})\n"

            elif step_operation == WorkflowOperation.GENERATE:
                examples_text += f"      *   LLM 操作: 直接生成回复文本（不调用工具）。\n"
                examples_text += f"      *   LLM 回复: \"{response}\"\n"
                if wait_user:
                    examples_text += f"      *   (系统等待用户输入。用户有新输入时，系统会自动跳转到步骤 {next_step})\n"

        # 添加段落间隔
        examples_text += "\n"

    return examples_text

def generate_history_examples_from_structured(current_workflow_data: WorkflowDefinition, structured_examples: List[Dict]) -> List[List[Dict]]:
    """
    根据结构化示例生成历史示例

    参数:
        current_workflow_data: 工作流定义对象
        structured_examples: 结构化示例列表

    返回:
        历史示例列表
    """
    if not structured_examples:
        return []

    history_examples = []

    for i, example in enumerate(structured_examples):
        user_input = example.get("user_input", "")
        steps = example.get("steps", [])

        # 创建一个对话历史
        conversation = []

        # 添加用户消息
        conversation.append({
            "role": "user",
            "content": user_input
        })

        # 处理每个步骤
        for j, step_data in enumerate(steps):
            step_name = step_data.get("step", "")
            step_obj = None

            # 查找步骤对象
            step_obj = current_workflow_data.get_step_by_index(step_name)
            if not step_obj:
                # 尝试按名称查找
                for s in current_workflow_data.get_parsed_steps():
                    if s.name == step_name:
                        step_obj = s
                        break

            if not step_obj:
                continue
                
            # 获取步骤属性
            step_operation = step_obj.operation
            action = step_data.get("action", "")
            condition = step_data.get("condition", None)
            response = step_data.get("response", "")
            step_index = step_obj.index
            
            # 获取相关步骤属性
            next_step = getattr(step_obj, 'next', '?')
            true_branch = getattr(step_obj, 'true_branch', '?')
            false_branch = getattr(step_obj, 'false_branch', '?')
            wait_user = getattr(step_obj, 'wait_user', False)

            # 生成工具调用和响应
            tool_call_id = f"hist_fs{i+1}_{step_name.lower() if step_name else step_index}" 

            # 助手消息 - 工具调用
            assistant_message = {
                "role": "assistant",
                "content": "",
                "tool_calls": []
            }

            # 工具响应
            tool_response = {
                "role": "tool",
                "tool_call_id": tool_call_id
            }

            # 根据步骤类型处理
            if step_operation == WorkflowOperation.NOP:
                assistant_message["tool_calls"].append({
                    "id": tool_call_id,
                    "type": "function",
                    "function": {"name": "workflow.next_step", "arguments": "{}"}
                })

                # 获取下一步对象
                next_step_obj = current_workflow_data.get_step_by_index(next_step)
                next_step_name = next_step_obj.name if next_step_obj else 'UNKNOWN'
                next_step_operation = next_step_obj.operation if next_step_obj else 'UNKNOWN'
                
                # 获取下一步动作信息 (直接访问属性)
                next_step_action = "unknown"
                if next_step_obj:
                    if isinstance(next_step_obj, ExecuteStep) and next_step_obj.actions:
                        first_action = next_step_obj.actions[0]
                        next_step_action = first_action.name
                    elif isinstance(next_step_obj, ExecuteStep) and next_step_obj.action:
                        # 假设 action 可能是 ActionDefinition 或 str
                        action_attr = next_step_obj.action
                        next_step_action = action_attr.name if isinstance(action_attr, ActionDefinition) else action_attr

                tool_response["content"] = dumps({
                    "success": True,
                    "message": f"...前进到步骤 {next_step_name}...",
                    "current_step": {
                        "index": next_step,
                        "name": next_step_name,
                        "operation": next_step_operation,
                        "action": next_step_action
                    }
                })

            elif step_operation == WorkflowOperation.EXECUTE:
                # 生成参数
                arguments = "{}"

                assistant_message["tool_calls"].append({
                    "id": tool_call_id,
                    "type": "function",
                    "function": {"name": action, "arguments": arguments}
                })

                # 获取下一步对象
                next_step_obj = current_workflow_data.get_step_by_index(next_step)
                next_step_name = next_step_obj.name if next_step_obj else 'UNKNOWN'
                next_step_operation = next_step_obj.operation if next_step_obj else 'UNKNOWN'

                # 生成工具响应
                tool_response["content"] = dumps({
                    "success": True,
                    "workflow_status": {
                        "success": True,
                        "message": f"自动前进到步骤 {next_step_name} (索引: {next_step})",
                        "current_step": {
                            "index": next_step,
                            "name": next_step_name,
                            "operation": next_step_operation
                        }
                    }
                })

            elif step_operation == WorkflowOperation.CONDITION:
                assistant_message["tool_calls"].append({
                    "id": tool_call_id,
                    "type": "function",
                    "function": {"name": "workflow.condition_branch", "arguments": dumps({"condition_result": condition})}
                })

                branch = true_branch if condition else false_branch
                branch_step_obj = current_workflow_data.get_step_by_index(branch)
                branch_step_name = branch_step_obj.name if branch_step_obj else 'UNKNOWN'
                branch_step_operation = branch_step_obj.operation if branch_step_obj else 'UNKNOWN'
                branch_step_wait_user = getattr(branch_step_obj, 'wait_user', False) if branch_step_obj else False

                tool_response["content"] = dumps({
                    "success": True,
                    "message": f"...跳转到步骤 {branch_step_name}...",
                    "current_step": {
                        "index": branch,
                        "name": branch_step_name,
                        "operation": branch_step_operation,
                        "wait_user": branch_step_wait_user
                    }
                })

            elif step_operation == WorkflowOperation.GENERATE and response:
                # 对于GENERATE操作，只添加助手的回复
                conversation.append({
                    "role": "assistant",
                    "content": response
                })
                # 不添加tool_call和tool_response
                continue

            # 添加到对话历史
            conversation.append(assistant_message)
            conversation.append(tool_response)

        # 确保对话以助手消息结束
        if conversation and conversation[-1]["role"] != "assistant":
            last_step_data = steps[-1] if steps else {}
            last_response = last_step_data.get("response", "")
            if last_response:
                conversation.append({
                    "role": "assistant",
                    "content": last_response
                })

        # 将完整的对话添加到历史示例中
        history_examples.append(conversation)

    return history_examples
