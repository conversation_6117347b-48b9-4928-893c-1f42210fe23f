"use client";

import { useState, useRef, useEffect } from "react";
import type { FormattedApiMessage, FormattedApiMessageToolCall } from "../types/message";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

// 辅助函数：根据角色获取样式类 (颜色部分保持，文本颜色将在具体元素上处理)
const getBaseStyleClass = (role: string) => {
  switch (role) {
    case "user":
      return "bg-blue-50 border-blue-200 dark:bg-blue-900 dark:border-blue-700";
    case "assistant":
      return "bg-emerald-50 border-emerald-200 dark:bg-emerald-900 dark:border-emerald-700";
    case "system":
      return "bg-amber-50 border-amber-200 dark:bg-amber-900 dark:border-amber-700";
    case "tool":
      return "bg-purple-50 border-purple-200 dark:bg-purple-900 dark:border-purple-700";
    default:
      return "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600";
  }
};

const getTextColorClass = (role: string) => {
  return "text-gray-700 dark:text-gray-300";
};

// 辅助函数：根据角色获取显示图标
const getDisplayIcon = (role: string) => {
  switch (role) {
    case "user":
      return "👤";
    case "assistant":
      return "🤖";
    case "system":
      return "⚙️";
    case "tool":
      return "🛠️";
    default:
      return "📝";
  }
};

// 辅助函数：根据角色获取显示标签
const getDisplayLabel = (role: string) => {
  switch (role) {
    case "user":
      return "用户";
    case "assistant":
      return "助手";
    case "system":
      return "系统";
    case "tool":
      return "工具结果";
    default:
      return role.charAt(0).toUpperCase() + role.slice(1);
  }
};

// 常量定义
const INITIAL_MAX_HEIGHT_PX = 120; // 与 message-item.tsx 保持一致
const EXPANDED_MAX_HEIGHT_PX = 400; // 修改为400px，与 message-item.tsx 保持一致

interface FormattedMessageItemProps {
  message: FormattedApiMessage;
  className?: string;
}

export default function FormattedMessageItem({ message, className }: FormattedMessageItemProps) {
  const isRightAligned = message.role === "user";
  const isFullWidthStyle = message.role === "system";

  // 折叠状态
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const contentRef = useRef<HTMLDivElement | HTMLPreElement>(null);

  useEffect(() => {
    const currentContent = contentRef.current;
    if (currentContent && typeof message.content === 'string') { // 只对字符串内容检查溢出
      // 使用 setTimeout 确保在 DOM 更新后进行计算
      setTimeout(() => {
        if (currentContent.scrollHeight > currentContent.clientHeight) {
          setIsOverflowing(true);
        } else {
          setIsOverflowing(false);
        }
      }, 0);
    }
  }, [message.content, isExpanded]);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const renderContent = (content: any) => {
    if (typeof content === 'string') {
      return (
        <div 
          ref={contentRef as React.RefObject<HTMLDivElement>}
          className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 text-sm relative scrollbar-thin"
          style={{
            maxHeight: isExpanded ? `${EXPANDED_MAX_HEIGHT_PX}px` : `${INITIAL_MAX_HEIGHT_PX}px`,
            overflowY: isExpanded ? "auto" : "hidden",
          }}
        >
          {content}
        </div>
      );
    }
    if (content === null || content === undefined) {
      if (message.role === 'assistant' && message.tool_calls && message.tool_calls.length > 0) {
        return null; 
      }
      return <span className="italic text-gray-500 dark:text-gray-400 text-sm">无文本内容</span>;
    }
    try {
      return (
        <div 
          className="text-gray-700 dark:text-gray-300 text-sm relative scrollbar-thin"
          style={{
            maxHeight: isExpanded ? `${EXPANDED_MAX_HEIGHT_PX}px` : `${INITIAL_MAX_HEIGHT_PX}px`,
            overflowY: isExpanded ? "auto" : "hidden",
          }}
        >
          <pre className="text-xs whitespace-pre-wrap bg-gray-200 dark:bg-gray-700 p-1 rounded">{JSON.stringify(content, null, 2)}</pre>
        </div>
      );
    } catch (e) {
      return <span className="italic text-red-500 text-sm">无法序列化内容</span>;
    }
  };

  const renderToolCalls = (toolCalls: FormattedApiMessageToolCall[]) => (
    <div className="mt-2 p-2 border-t border-gray-200 dark:border-gray-700">
      <h4 className="text-xs font-semibold mb-1 text-gray-600 dark:text-gray-400">工具调用:</h4>
      {toolCalls.map((tc) => (
        <div key={tc.id} className="text-xs p-1.5 bg-gray-100 dark:bg-gray-800 rounded-md mb-1 space-y-0.5">
          <p><strong className="text-gray-700 dark:text-gray-300">ID:</strong> {tc.id}</p>
          <p><strong className="text-gray-700 dark:text-gray-300">函数:</strong> {tc.function.name}</p>
          {tc.function.arguments && (
            <pre className="whitespace-pre-wrap text-xs bg-gray-200 dark:bg-gray-700 p-1 rounded mt-0.5">{tc.function.arguments}</pre>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <div className={`flex mb-2 ${isRightAligned && !isFullWidthStyle ? "justify-end" : ""} ${className || ''}`}>
      <div className={`${isFullWidthStyle ? 'w-full' : 'max-w-[85%]'} ${isFullWidthStyle ? '' : (isRightAligned ? 'mr-2' : 'ml-2')}`}>
        <div
          className={`rounded-lg p-3 shadow-sm border flex flex-col 
                    ${getBaseStyleClass(message.role)} 
                    ${isRightAligned && !isFullWidthStyle ? 'rounded-tr-none' : ''} 
                    ${!isRightAligned && !isFullWidthStyle ? 'rounded-tl-none' : ''}
                  `}
        >
          <div className="flex items-center gap-1.5 mb-1">
            <span className="text-sm">{getDisplayIcon(message.role)}</span>
            <span className={`font-medium text-sm ${getTextColorClass(message.role)}`}>{getDisplayLabel(message.role)}</span>
          </div>
          
          {(message.content !== undefined && message.content !== null) || (message.role === 'assistant' && message.tool_calls && message.tool_calls.length > 0) ? (
             <div className={`text-sm`}> 
                {renderContent(message.content)}
                {(isOverflowing || isExpanded) && (
                  <Button
                    variant="link"
                    size="sm"
                    onClick={toggleExpand}
                    className="mt-1 px-0 h-auto text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center text-xs"
                  >
                    {isExpanded ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />}
                  </Button>
                )}
             </div>
          ) : null}
          
          {message.tool_calls && message.tool_calls.length > 0 && renderToolCalls(message.tool_calls)}
        </div>
      </div>
    </div>
  );
} 