# MeowAgent

基于大型语言模型（LLM）的智能体框架，用于构建对话式AI应用。

## 文档

[工作流教程](docs/workflow_tutorial.md)

## 核心功能

- **工具集成系统**：通过模型上下文协议（MCP）集成远程工具，支持本地Python工具库自动注册
- **持久化记忆**：扩展LLM上下文能力，支持文件系统式存储和检索，兼容本地存储与阿里云OSS
- **异步流式架构**：提供模型响应到用户交互的全链路流式输出
- **工作流引擎**：支持通过YAML或Python定义任务流程，包含条件分支与状态管理
- **文档处理**：支持多格式文档加载与语义检索
- **多智能体管理**：管理多个智能体实例的生命周期和资源
- **多接口支持**：提供WebSocket API和CLI客户端

## 系统架构

```
                     Clients
   +-------------+   +-------------+   +-------------+
   |     CLI     |   |    API      |   | WebSocket   |
   |  (cli.py)   |   |  (api.py)   |<->| Connections |
   +------+------+   +------+------+   +-------------+
          |                 |                 |
          +-----------------+-----------------+
                            |
                 +----------+-----------+
                 |   AgentManager       |
                 | (agent_manager.py)   |
                 +---------+------------+
                           |
                           v
                 +---------+------------+
                 |        Agent         |
                 |      (agent.py)      |
                 +-+---------+----------+
                   |         |
      +------------+         +----------------+
      |                                       |
+-----+------+                     +----------+----------+
|    Model   |                     |     Libraries       |
| (model.py) |                     | (libraries/*.py)    |
+------------+                     |                     |
                                   |  - Memory           |
                                   |  - Document         |
                                   |  - Workflow         |
                                   |  - MCP Client       |
                                   +----------+----------+
                                              |
                                   +----------+----------+
                                   |  Database & Storage |
                                   | (MongoDB, OSS, FS)  |
                                   +---------------------+
```

## 快速开始

### 环境要求

- Python 3.12+
- MongoDB (用于对话历史存储)
- (可选) 阿里云OSS账户 (如使用OSS作为记忆存储)

### 安装依赖

```bash
# 使用 uv (推荐)
uv sync

# 或者使用 pip 和 virtualenv
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate  # Windows
pip install -e .
```

### 基本配置

创建 `.env` 文件设置必要配置：

```env
OPENAI_API_KEY="sk-your_openai_api_key"
DEFAULT_MODEL_NAME="gpt-4-turbo"
MONGO_URI="mongodb://localhost:27017/"
DB_NAME="meow_agent_db"

# 如果使用OSS记忆存储
# OSS_ACCESS_KEY_ID="your_access_key"
# OSS_ACCESS_KEY_SECRET="your_access_key_secret"
# OSS_ENDPOINT="your_endpoint"
# OSS_BUCKET_NAME="your_bucket"
# OSS_MEMORY_PREFIX="memory_data/"
# MEMORY_STORAGE_TYPE="oss"  # 或 "fs" 使用本地文件系统
```

## 运行方式

### WebSocket API服务

```bash
python api.py
```

服务默认在 `http://localhost:8000` 启动，客户端可通过 `ws://localhost:8000/ws` 连接。

### 命令行客户端

```bash
python cli.py --user my_session_id  # 指定会话ID
python cli.py --stream  # 启用流式输出
```

## 核心模块

### 智能体管理器 (AgentManager)

集中管理系统的智能体实例和共享资源：

- 单例模式，统一协调所有智能体
- 资源池化（语言模型、MCP客户端）
- 动态创建、跟踪与终止智能体实例
- 资源释放管理

### 智能体 (Agent)

核心交互逻辑的实现：

- 对话流程管理与消息处理
- 与LLM交互（流式/非流式）
- 工具调用与执行
- 工作流集成与状态管理

### 工具系统

可扩展的能力提供机制：

- 本地库：继承`Library`基类，使用`@register_tool`自动注册
- MCP远程工具：通过协议连接外部工具服务器
- 内置核心工具库：记忆、文档、工作流等

### 记忆系统

类文件系统的长期记忆能力：

- 分层存储（文件/文件夹结构）
- 多后端支持（本地文件系统、阿里云OSS）
- 标签页机制，提供快速访问摘要

### 工作流引擎

任务编排与控制机制：

- YAML声明式定义
- Python生成器动态定义
- 步骤类型：执行、条件、跳转、生成、用户输入等
- 寄存器与状态管理

### 渲染系统

消息转换与处理管道：

- 消息重排序与过滤
- Token自动裁剪
- 记忆提示生成

## 扩展与定制

### 添加新工具库

1. 创建继承自`Library`的类
2. 使用`@register_tool`装饰异步方法
3. 确保包含类型注解和文档字符串

```python
from libraries.library import Library, register_tool

class MyNewLibrary(Library):
    @register_tool
    async def my_tool(self, param1: str, param2: int) -> str:
        """我的自定义工具
        
        Args:
            param1: 第一个参数的说明
            param2: 第二个参数的说明
            
        Returns:
            处理结果
        """
        # 实现逻辑
        return f"处理结果: {param1}, {param2}"
```

### 创建新工作流

在`workflows/`目录下创建YAML文件：

```yaml
description: 自定义工作流
steps:
  - index: 0
    operation: EXECUTE
    description: 执行初始步骤
    actions:
      - names: ["memory.list_directory", "memory.read_file"]
        min_calls: 1
        max_calls: 3
  - index: 1
    operation: CONDITION
    description: 条件判断
    condition_description: 判断是否需要进一步操作
    true_branch: 2
    false_branch: 3
  # 更多步骤...
```

或使用Python生成器创建动态工作流：

```python
async def steps() -> AsyncGenerator[WorkflowStep, Any]:
    yield NopStep(name="init", description="初始化")
    
    user_input = yield UserInputStep(
        name="get_input",
        description="等待用户输入"
    )
    
    # 更多步骤...
```

## 开发规划

- 多模态支持（图像、音频）
- 工具调用处理增强（失败重试、并行调用）
- 上下文压缩优化
- 工作流功能扩展
- 智能体间通信
- Web用户界面
- 数据库支持扩展

## Web前端集成

MeowAgent提供Web前端集成能力：

- WebSocket双向通信
- REST API管理
- 标准化消息格式
- 内置类型定义支持

---

欢迎贡献与反馈。如有问题或建议，请通过Issues或Pull Requests联系我们。
