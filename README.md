# MeowAgent Web - 开发后台

## 项目概览

MeowAgent Web 是一个基于 Web 技术的开发与调试后台，专为 MeowAgent 项目设计。它的主要目标是提供一个可视化的交互界面，方便开发者调试 Agent 的行为、管理对话流程、查看内部状态以及迭代和优化 MeowAgent 的核心功能。

此项目旨在辅助 MeowAgent 的开发过程，提供比命令行界面更丰富、更直观的工具。

## 核心功能

*   **对话可视化与交互**：以清晰的界面展示用户与 Agent 之间的对话历史。
*   **实时状态监控**：通过控制台面板实时查看 Agent 的内部日志、工具调用、推理过程等。
*   **对话历史同步**：前端展示的消息列表与后端 Agent 的 `Conversation` 对象中的历史记录进行严格的双向同步（主要通过 HTTP API 实现）。这允许开发者在前端直接修改、排序、增删对话消息，并实时反映到后端的对话状态中，用于测试和场景模拟。
*   **Agent 控制**：提供运行、停止、配置 Agent 的基本操作。
*   **配置管理**：方便地调整 Agent 的模型参数、加载的库等。

## 技术栈（示例）

*   Next.js
*   TypeScript
*   Tailwind CSS
*   Shadcn UI
*   FastAPI (后端 `meowagent` 项目提供 API 支持)

## 主要用途

*   **Agent 行为调试**：观察 Agent 如何响应不同的用户输入，以及工具调用的详细过程。
*   **对话流测试**：模拟各种对话场景，测试 Agent 在多轮对话中的表现。
*   **Prompt 工程**：通过修改系统消息或用户消息，快速测试不同提示词的效果。
*   **工具与库集成测试**：验证新开发的工具或库是否能被 Agent 正确调用和解析。
*   **教学与演示**：清晰地展示 Agent 的工作原理和能力。

## 同步机制说明

前端的消息列表（聊天区域）与后端 Agent 的实际对话历史（`Conversation` 实例）保持严格同步。这意味着：
1.  前端加载时，会通过 HTTP API 从后端获取当前对话的完整历史。
2.  用户在前端对消息列表进行的任何修改（如编辑内容、更改角色、删除消息、调整顺序）都会通过 HTTP API 同步到后端，直接影响 Agent 的 `Conversation` 状态。
3.  Agent 执行后产生的新的助手消息或工具消息，后端会更新其 `Conversation` 历史。前端会通过机制（如 WebSocket 通知后由 HTTP API 拉取更新）刷新消息列表，以反映这些由 Agent 产生的变化。

这种设计使得 `meowagent-web` 成为一个强大的开发工具，可以直接操纵和观察 Agent 的核心对话状态。

## 未来规划

*   更细致的状态展示面板（例如内存、工作流步骤可视化）。
*   支持多用户会话隔离的切换与管理。
*   更完善的配置项管理界面。

---

此 README 旨在帮助参与 MeowAgent 项目开发的成员理解此 Web 后台的定位和使用方式。
