"use client"

import { <PERSON>, <PERSON>ting<PERSON>, <PERSON>, <PERSON>, <PERSON>u } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useTheme } from "next-themes"
import { useState } from "react"

interface HeaderProps {
  onOpenConfig: () => void
}

export default function Header({ onOpenConfig }: HeaderProps) {
  const { theme, setTheme } = useTheme()
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <header className="border-b border-gray-200 dark:border-gray-800">
      <div className="flex items-center justify-between h-14 px-4">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-2">
            <span className="font-semibold text-lg">MeowAgent</span>
          </div>
        </div>

        <div className="hidden md:flex items-center max-w-md w-full relative">
          <div className="relative w-full">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              type="search"
              placeholder="搜索..."
              className="pl-9 h-9 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            title={theme === "dark" ? "切换到亮色模式" : "切换到暗色模式"}
          >
            {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
          </Button>
          <Button variant="ghost" size="icon" title="设置" onClick={onOpenConfig}>
            <Settings className="h-5 w-5" />
          </Button>
          <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            用户
          </div>
        </div>
      </div>

      <div className="flex items-center h-10 px-4 border-t border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
        <div className="flex space-x-4 text-sm">
          <button className="px-3 py-1 rounded-md bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 font-medium">
            对话
          </button>
          <button className="px-3 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800">模型</button>
          <button className="px-3 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800">历史</button>
          <button className="px-3 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-800">文档</button>
        </div>
      </div>
    </header>
  )
}
