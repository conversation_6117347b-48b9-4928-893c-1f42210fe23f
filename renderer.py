"""
该模块定义了一套灵活的消息渲染管道，用于将内部的 `Message` 对象列表转换成两种主要的目标格式：
1. 大语言模型 (LLM) API（尤其是 OpenAI API）所需的输入格式（通常是字典列表）。
2. 用于记忆库或上下文摘要的纯字符串格式。

它采用了责任链设计模式，其中每个 `Renderer` 类代表管道中的一个处理步骤。
这些步骤可以被组合到 `RendererPipeline` 中以实现复杂的消息转换逻辑。

核心组件包括：
- `Renderer` (ABC): 所有渲染器的抽象基类。
- `ReorderRenderer`: 处理 `AgentSystemMessage`，将其内容（基础提示、工具库提示、Few-Shot示例）
  提取并整合为标准的 `SystemMessage` 置于列表顶端，并移除原始列表中的首个 `SystemMessage`。
- `ExcludeSystemRenderer`: 根据上下文标志，选择性地移除所有 `SystemMessage`。
- `BasicRenderer`: 将内部 `Message` 对象列表转换为 LLM API（如OpenAI）兼容的字典列表格式，
  正确处理角色、内容、工具调用（tool_calls）和工具响应（tool_call_id）。
- `TrimRenderer`: 根据指定的最大 token 数量和所使用的模型，对 API 格式的消息列表进行裁剪，
  优先从列表早期移除非系统消息，以确保不超过 token 限制。
- `FilterMemoryPromptRenderer`: 过滤消息列表，仅保留适合用于构建记忆提示（memory prompt）的消息类型。
  会移除 `AgentSystemMessage`、`SystemMessage` 以及不含实质内容的消息（除非是带工具调用的助手消息）。
- `StringToMemoryPromptRenderer`: 将由 `FilterMemoryPromptRenderer` 筛选过的消息列表转换为一个单一的、
  格式化的多行字符串，便于用作记忆提示或上下文记录。

模块还预定义了两个主要的渲染管道：
- `DEFAULT_RENDERER_PIPELINE`: 用于将消息列表处理成 LLM API 的标准输入。
  执行顺序: `ReorderRenderer` -> `ExcludeSystemRenderer` -> `BasicRenderer` -> `TrimRenderer`。
- `MEMORY_PROMPT_PIPELINE`: 用于将消息列表处理成紧凑的字符串格式，适用于记忆系统。
  执行顺序: `FilterMemoryPromptRenderer` -> `StringToMemoryPromptRenderer`。

这种设计提供了高度的灵活性，允许开发者根据具体需求定制或扩展渲染流程。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from message import Message, SystemMessage, AgentSystemMessage, ToolMessage, AssistantMessage
from model import OpenAIModel # 需要 OpenAIModel 来进行 Token 计数
from log import logger

# --- 渲染器基类 ---

class Renderer(ABC):
    """渲染器抽象基类"""
    @abstractmethod
    async def render(self, messages: Any, **context) -> Any:
        """
        执行渲染步骤。

        参数:
            messages: 输入的消息列表（类型取决于前一个渲染器）
            **context: 渲染上下文，可能包含 model, max_tokens, exclude_system 等

        返回:
            处理后的消息列表（类型取决于当前渲染器）
        """
        pass

# --- 具体渲染器实现 ---

class ReorderRenderer(Renderer):
    """
    处理 AgentSystemMessage，确保只保留最后一个并展开。
    删除最前面的 SystemMessage，将 AgentSystemMessage 放到最前面。
    输入: List[Message]
    输出: List[Message]
    """
    async def render(self, messages: List[Message], **context) -> List[Message]:
        # 1. 找出消息列表中的AgentSystemMessage
        agent_system_messages = [msg for msg in messages if isinstance(msg, AgentSystemMessage)]
        # 只保留最后一个AgentSystemMessage（如果有）
        last_agent_system_message = agent_system_messages[-1] if agent_system_messages else None

        expanded_messages: List[Message] = []
        
        # 先处理最后一个AgentSystemMessage（如果存在）
        if last_agent_system_message:
            # 创建系统消息（包含基础提示和库提示）
            combined_prompt = last_agent_system_message.base_prompt
            if last_agent_system_message.library_prompts:
                combined_prompt += "\n\n可用工具库信息：\n" + "\n\n".join(last_agent_system_message.library_prompts)

            system_msg = SystemMessage(combined_prompt)
            # 重要的时间戳继承：让系统消息的时间戳与原始AgentSystemMessage一致
            system_msg.timestamp = last_agent_system_message.timestamp
            expanded_messages.append(system_msg)

            # 添加Few-Shot示例，并尝试继承时间戳
            for example_list in last_agent_system_message.few_shot_examples:
                for example_msg in example_list:
                    # 尝试让 few-shot 示例的时间戳也跟随 AgentSystemMessage
                    # 如果示例消息已有时间戳，则保留；否则，设为 AgentSystemMessage 的时间戳
                    if not hasattr(example_msg, 'timestamp') or example_msg.timestamp is None:
                        try:
                            example_msg.timestamp = last_agent_system_message.timestamp
                        except AttributeError: # 如果 example_msg 不允许设置 timestamp
                            pass
                expanded_messages.extend(example_list)
        
        # 找出前面连续的SystemMessage的范围
        consecutive_system_end = 0
        for i, msg in enumerate(messages):
            if isinstance(msg, SystemMessage):
                consecutive_system_end = i + 1
            else:
                break
                
        # 然后处理其他消息，跳过AgentSystemMessage和前面连续的SystemMessage（除了最后一个）
        for i, msg in enumerate(messages):
            if isinstance(msg, AgentSystemMessage):
                # 跳过所有AgentSystemMessage
                continue
            elif isinstance(msg, SystemMessage):
                # 如果这是前面连续SystemMessage中的一个
                if i < consecutive_system_end:
                    # 只保留连续SystemMessage中的最后一个
                    if i == consecutive_system_end - 1:
                        expanded_messages.append(msg)
                else:
                    # 保留后面的非连续SystemMessage
                    expanded_messages.append(msg)
            else:
                # 保留其他所有消息
                expanded_messages.append(msg)
                
        return expanded_messages

class ExcludeSystemRenderer(Renderer):
    """
    根据上下文 exclude_system 标志过滤掉 SystemMessage。
    输入: List[Message]
    输出: List[Message]
    """
    async def render(self, messages: List[Message], **context) -> List[Message]:
        exclude_system = context.get('exclude_system', False)
        if exclude_system:
            return [msg for msg in messages if not isinstance(msg, SystemMessage)]
        return messages

class BasicRenderer(Renderer):
    """
    将 List[Message] 转换为 OpenAI API 所需的 List[Dict[str, Any]] 格式。
    输入: List[Message]
    输出: List[Dict[str, Any]]
    """
    async def render(self, messages: List[Message], **context) -> List[Dict[str, Any]]:
        api_messages = []
        for msg in messages:
            # AgentSystemMessage 应该已被 ReorderRenderer 处理并转换/移除，
            # BasicRenderer 通常处理的是 SystemMessage, UserMessage, AssistantMessage, ToolMessage
            if isinstance(msg, AgentSystemMessage):
                logger.warning("BasicRenderer遇到了AgentSystemMessage，这不应该发生，它应该已被ReorderRenderer处理。")
                continue

            role = msg.get_role() # 使用 get_role() 获取标准角色

            message_dict = {
                "role": role,
                "content": msg.get_content()
            }

            # 处理工具响应消息
            if isinstance(msg, ToolMessage) and msg.tool_call_id:
                message_dict["tool_call_id"] = msg.tool_call_id

            # 处理带工具调用的助手消息
            if isinstance(msg, AssistantMessage) and msg.tool_calls:
                tool_calls = []
                for tc in msg.tool_calls:
                    tool_call_dict = {
                        "id": tc.id,
                        "type": "function",
                        "function": {
                            "name": tc.function.name,
                            "arguments": tc.function.arguments
                        }
                    }
                    tool_calls.append(tool_call_dict)

                if tool_calls:
                    # 确保 content 存在，即使为空字符串，当 tool_calls 存在时
                    if message_dict["content"] is None:
                        message_dict["content"] = ""
                    message_dict["tool_calls"] = tool_calls
            
            # 对于没有内容的助手消息（例如，只有工具调用），确保 content 不为 None
            if role == "assistant" and message_dict["content"] is None:
                if "tool_calls" not in message_dict: # 只有当没有工具调用时，才可能完全没有内容
                    message_dict["content"] = "" # 或许留 None？OpenAI API 似乎允许 null content

            api_messages.append(message_dict)
        return api_messages


class TrimRenderer(Renderer):
    """
    根据 max_tokens 和 model 裁剪消息列表。
    输入: List[Dict[str, Any]] (来自 BasicRenderer)
    输出: List[Dict[str, Any]]
    """
    async def render(self, messages: List[Dict[str, Any]], **context) -> List[Dict[str, Any]]:
        max_tokens: Optional[int] = context.get('max_tokens')
        model: Optional[OpenAIModel] = context.get('model')

        # 仅当提供了 max_tokens 和 model 时才进行裁剪
        if max_tokens is None or model is None:
            return messages

        try:
            current_tokens = model.count_tokens(messages)
        except Exception as e:
            logger.error(f"TrimRenderer 无法计算 token 数: {e}. 跳过裁剪。")
            return messages # 如果无法计数，则不裁剪

        if current_tokens <= max_tokens:
            return messages # 不需要裁剪

        logger.debug(f"Token 数量 ({current_tokens}) 超出限制 ({max_tokens})，开始裁剪...")

        # 分离系统消息和非系统消息
        system_indices = [i for i, msg in enumerate(messages) if msg.get("role") == "system"]
        non_system_indices = [i for i, msg in enumerate(messages) if msg.get("role") != "system"]

        # 从最早的非系统消息开始移除
        messages_to_remove = 0
        trimmed_messages = list(messages) # 创建副本进行操作

        while non_system_indices and current_tokens > max_tokens:
            idx_to_remove = non_system_indices.pop(0) # 获取最早的非系统消息索引
            # 在副本中标记删除（例如设置为None），而不是直接删除，以保持索引稳定
            # 或者，更简单的方式是重建列表
            messages_to_remove += 1

            # 重新计算 token，这里效率较低，但逻辑清晰
            # 优化：可以估算移除消息减少的 token 数，但精确计算更可靠
            temp_trimmed_list = [msg for i, msg in enumerate(messages) if i in system_indices or i in non_system_indices]

            try:
                current_tokens = model.count_tokens(temp_trimmed_list)
            except Exception as e:
                 logger.error(f"TrimRenderer 裁剪过程中无法计算 token 数: {e}. 停止裁剪。")
                 # 返回裁剪到当前状态的列表，或者返回原始列表？返回当前状态可能更好
                 return temp_trimmed_list


        final_trimmed_messages = [msg for i, msg in enumerate(messages) if i in system_indices or i in non_system_indices]
        logger.debug(f"裁剪完成。移除了 {messages_to_remove} 条非系统消息。最终 token 数: {current_tokens}")

        return final_trimmed_messages

# --- 新增：用于记忆提示的渲染器 ---

class FilterMemoryPromptRenderer(Renderer):
    """
    过滤消息，仅保留适合放入记忆提示的消息类型和内容。
    输入: List[Message]
    输出: List[Message]
    """
    async def render(self, messages: List[Message], **context) -> List[Message]:
        filtered = []
        for msg in messages:
            content = msg.get_content()

            # 过滤掉内部系统消息和显式系统消息
            if isinstance(msg, AgentSystemMessage) or isinstance(msg, SystemMessage):
                continue

            # 过滤掉没有内容的 User/Tool 消息
            if not content:
                 # 但保留有 tool_calls 的 Assistant 消息即使 content 为空
                 if not (isinstance(msg, AssistantMessage) and msg.tool_calls):
                     continue
            
            filtered.append(msg)
        return filtered


class StringToMemoryPromptRenderer(Renderer):
    """
    将过滤后的 List[Message] 转换为记忆提示所需的单字符串格式。
    输入: List[Message]
    输出: str
    """
    async def render(self, messages: List[Message], **context) -> str:
        formatted_lines = []
        for msg in messages:
            role_str = msg.get_role().capitalize() # User, Assistant, Tool, System
            content = msg.get_content() # 内容可能为 None 或 ""

            # 处理助手消息的工具调用部分
            if isinstance(msg, AssistantMessage) and msg.tool_calls:
                tool_info = ", ".join([f"(调用工具: {tc.function.name})" for tc in msg.tool_calls])
                if content: # 检查 content 是否非空
                     formatted_lines.append(f"{role_str}: {content} {tool_info}")
                else:
                    formatted_lines.append(f"{role_str}: {tool_info}") # 只有工具调用
            elif isinstance(msg, ToolMessage) and msg.tool_call_id:
                # 标记工具结果 (确保 content 存在才添加)
                if content:
                    formatted_lines.append(f"{role_str} (ID: {msg.tool_call_id}): {content}")
            else:
                # 普通用户或助手消息 (无工具调用或内容不为空)
                 if content: # 确保 content 存在才添加
                    formatted_lines.append(f"{role_str}: {content}")
                    
        return "\n".join(formatted_lines)


# --- 渲染管道 ---

class RendererPipeline:
    """组合并执行一系列渲染器"""
    def __init__(self, renderers: List[Renderer]):
        """
        初始化渲染管道。

        参数:
            renderers: 按顺序执行的渲染器列表
        """
        self.renderers = renderers

    async def render(self, messages: List[Message], **context) -> Any:
        """
        按顺序执行所有渲染器。

        参数:
            messages: 初始的消息列表 (通常是 List[Message])
            **context: 传递给每个渲染器的上下文

        返回:
            最后一个渲染器的输出结果 (通常是 List[Dict[str, Any]])
        """
        current_data = messages
        for renderer in self.renderers:
            # logger.debug(f"执行渲染器: {renderer.__class__.__name__}...")
            current_data = await renderer.render(current_data, **context)
            # logger.debug(f"渲染器 {renderer.__class__.__name__} 完成。")
        return current_data

# --- 默认管道实例 ---

# 定义默认的渲染顺序
DEFAULT_RENDERER_PIPELINE = RendererPipeline([
    ReorderRenderer(),        # 处理 AgentSystemMessage
    ExcludeSystemRenderer(),  # 根据需要排除 SystemMessage
    BasicRenderer(),          # 转换为 API Dict 格式
    TrimRenderer()            # 执行裁剪
])

# --- 新增：记忆提示渲染管道 ---
MEMORY_PROMPT_PIPELINE = RendererPipeline([
    # 注意：我们不需要 ReorderRenderer，因为 AgentSystemMessage 会被 FilterMemoryPromptRenderer 过滤掉
    FilterMemoryPromptRenderer(), # 过滤不相关消息
    StringToMemoryPromptRenderer() # 转换为字符串格式
])
